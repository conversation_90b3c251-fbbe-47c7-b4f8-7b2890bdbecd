---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 21-违规举报服务/责任追究整改

## POST 分页查询所有责任追究整改列表

POST /whiskerguardviolationservice/api/responsibility/investigate/corrections/search

描述：分页查询所有责任追究整改列表，支持排序和过滤。

> Body 请求参数

```json
{
  "tenantId": 0,
  "name": "string",
  "correctionCode": "string",
  "correctionType": "COMPLIANCE_RISK",
  "level": "LOW",
  "investigateId": 0,
  "dutyEmployeeId": 0,
  "dutyEmployeeOrgId": 0,
  "collaborationEmployeeId": 0,
  "supervisionEmployeeId": 0,
  "startDate": "string",
  "finishDate": "string",
  "status": "NO_START",
  "createdAtStart": "string",
  "createdAtEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |当前页码|
|size|query|integer| 否 |记录数|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[ResponsibilityInvestigateCorrectionReq](#schemaresponsibilityinvestigatecorrectionreq)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "name": "",
      "correctionCode": "",
      "correctionType": "",
      "level": "",
      "investigateId": 0,
      "dutyEmployeeId": 0,
      "dutyEmployeeOrgId": 0,
      "collaborationEmployeeId": 0,
      "supervisionEmployeeId": 0,
      "startDate": "",
      "finishDate": "",
      "status": "",
      "correctionBackground": "",
      "correctionRequire": "",
      "correctionRange": "",
      "correctionScheme": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "total": 0,
  "empty": false,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "totalPages": 0,
  "totalElements": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageResponsibilityInvestigateCorrectionDTO](#schemaresponseentitypageresponsibilityinvestigatecorrectiondto)|

# 数据模型

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_Sort">Sort</h2>

<a id="schemasort"></a>
<a id="schema_Sort"></a>
<a id="tocSsort"></a>
<a id="tocssort"></a>

```json
{
  "direction": "ASC",
  "property": "string",
  "ignoreCase": true,
  "nullHandling": "NATIVE",
  "ascending": true,
  "descending": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|direction|string|false|none||none|
|property|string|false|none||none|
|ignoreCase|boolean|false|none||none|
|nullHandling|string|false|none||none|
|ascending|boolean|false|none||Returns whether sorting for this property shall be ascending.|
|descending|boolean|false|none||Returns whether sorting for this property shall be descending.|

#### 枚举值

|属性|值|
|---|---|
|direction|ASC|
|direction|DESC|
|nullHandling|NATIVE|
|nullHandling|NULLS_FIRST|
|nullHandling|NULLS_LAST|

<h2 id="tocS_Pageable">Pageable</h2>

<a id="schemapageable"></a>
<a id="schema_Pageable"></a>
<a id="tocSpageable"></a>
<a id="tocspageable"></a>

```json
{
  "paged": true,
  "unpaged": true,
  "pageNumber": 0,
  "pageSize": 0,
  "offset": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|paged|boolean|false|none||Returns whether the current{@link Pageable} contains pagination information.|
|unpaged|boolean|false|none||Returns whether the current{@link Pageable} does not contain pagination information.|
|pageNumber|integer|false|none||Returns the page to be returned.|
|pageSize|integer|false|none||Returns the number of items to be returned.|
|offset|integer(int64)|false|none||Returns the offset to be taken according to the underlying page and page size.|
|sort|[[Sort](#schemasort)]|false|none||Returns the sorting parameters.|

<h2 id="tocS_ResponsibilityInvestigateCorrectionDTO">ResponsibilityInvestigateCorrectionDTO</h2>

<a id="schemaresponsibilityinvestigatecorrectiondto"></a>
<a id="schema_ResponsibilityInvestigateCorrectionDTO"></a>
<a id="tocSresponsibilityinvestigatecorrectiondto"></a>
<a id="tocsresponsibilityinvestigatecorrectiondto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "correctionCode": "string",
  "correctionType": "COMPLIANCE_RISK",
  "level": "LOW",
  "investigateId": 0,
  "dutyEmployeeId": 0,
  "dutyEmployeeOrgId": 0,
  "collaborationEmployeeId": 0,
  "supervisionEmployeeId": 0,
  "startDate": "string",
  "finishDate": "string",
  "status": "NO_START",
  "correctionBackground": "string",
  "correctionRequire": "string",
  "correctionRange": "string",
  "correctionScheme": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|name|string|false|none||整改项目名称|
|correctionCode|string|true|none||整改编号|
|correctionType|string|false|none||整改类型：合规风险、操作风险、系统风险|
|level|string|false|none||优先级：高、中、低|
|investigateId|integer(int64)|true|none||违规调查id|
|dutyEmployeeId|integer(int64)|true|none||责任人id|
|dutyEmployeeOrgId|integer(int64)|true|none||责任部门id|
|collaborationEmployeeId|integer(int64)|true|none||协作人id|
|supervisionEmployeeId|integer(int64)|true|none||监督人id|
|startDate|string|false|none||开始日期|
|finishDate|string|false|none||完成日期|
|status|string|false|none||状态：未开始、进行中、已完成、已暂停、已取消|
|correctionBackground|string|false|none||整改背景|
|correctionRequire|string|false|none||整改要求|
|correctionRange|string|false|none||整改范围|
|correctionScheme|string|false|none||整改方案|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|correctionType|COMPLIANCE_RISK|
|correctionType|OPERATIONAL_RISK|
|correctionType|SYSTEM_RISK|
|level|LOW|
|level|MIDDLE|
|level|HIGH|
|status|NO_START|
|status|PROGRESSING|
|status|FINISHED|
|status|PAUSED|
|status|CANCELED|

<h2 id="tocS_ResponseEntityPageResponsibilityInvestigateCorrectionDTO">ResponseEntityPageResponsibilityInvestigateCorrectionDTO</h2>

<a id="schemaresponseentitypageresponsibilityinvestigatecorrectiondto"></a>
<a id="schema_ResponseEntityPageResponsibilityInvestigateCorrectionDTO"></a>
<a id="tocSresponseentitypageresponsibilityinvestigatecorrectiondto"></a>
<a id="tocsresponseentitypageresponsibilityinvestigatecorrectiondto"></a>

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "name": "string",
      "correctionCode": "string",
      "correctionType": "COMPLIANCE_RISK",
      "level": "LOW",
      "investigateId": 0,
      "dutyEmployeeId": 0,
      "dutyEmployeeOrgId": 0,
      "collaborationEmployeeId": 0,
      "supervisionEmployeeId": 0,
      "startDate": "string",
      "finishDate": "string",
      "status": "NO_START",
      "correctionBackground": "string",
      "correctionRequire": "string",
      "correctionRange": "string",
      "correctionScheme": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ],
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "total": 0,
  "empty": true,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "totalPages": 0,
  "totalElements": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|content|[[ResponsibilityInvestigateCorrectionDTO](#schemaresponsibilityinvestigatecorrectiondto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|total|integer(int64)|false|none||none|
|empty|boolean|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|

<h2 id="tocS_ResponsibilityInvestigateCorrectionReq">ResponsibilityInvestigateCorrectionReq</h2>

<a id="schemaresponsibilityinvestigatecorrectionreq"></a>
<a id="schema_ResponsibilityInvestigateCorrectionReq"></a>
<a id="tocSresponsibilityinvestigatecorrectionreq"></a>
<a id="tocsresponsibilityinvestigatecorrectionreq"></a>

```json
{
  "tenantId": 0,
  "name": "string",
  "correctionCode": "string",
  "correctionType": "COMPLIANCE_RISK",
  "level": "LOW",
  "investigateId": 0,
  "dutyEmployeeId": 0,
  "dutyEmployeeOrgId": 0,
  "collaborationEmployeeId": 0,
  "supervisionEmployeeId": 0,
  "startDate": "string",
  "finishDate": "string",
  "status": "NO_START",
  "createdAtStart": "string",
  "createdAtEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|name|string|false|none||整改项目名称|
|correctionCode|string|false|none||整改编号|
|correctionType|string|false|none||整改类型：合规风险、操作风险、系统风险|
|level|string|false|none||优先级：高、中、低|
|investigateId|integer(int64)|false|none||违规调查id|
|dutyEmployeeId|integer(int64)|false|none||责任人id|
|dutyEmployeeOrgId|integer(int64)|false|none||责任部门id|
|collaborationEmployeeId|integer(int64)|false|none||协作人id|
|supervisionEmployeeId|integer(int64)|false|none||监督人id|
|startDate|string|false|none||开始日期|
|finishDate|string|false|none||完成日期|
|status|string|false|none||状态：未开始、进行中、已完成、已暂停、已取消|
|createdAtStart|string|false|none||创建开始时间|
|createdAtEnd|string|false|none||创建结束时间|

#### 枚举值

|属性|值|
|---|---|
|correctionType|COMPLIANCE_RISK|
|correctionType|OPERATIONAL_RISK|
|correctionType|SYSTEM_RISK|
|level|LOW|
|level|MIDDLE|
|level|HIGH|
|status|NO_START|
|status|PROGRESSING|
|status|FINISHED|
|status|PAUSED|
|status|CANCELED|

