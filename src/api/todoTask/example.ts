// 学习培训API使用示例
// 这个文件展示了如何在Vue组件中使用学习培训相关的API

import taskApi from './task'
import type {
  LearningProgressStatisticsDTO,
  CourseProgressDTO,
  RecommendedCourse
} from './types'

// 示例：获取用户学习进度概览
export async function getUserLearningOverview(userId?: string) {
  try {
    const response = await taskApi.learningApi({ userId }, 'overview')
    const data: LearningProgressStatisticsDTO = response.data
    
    // 返回index.vue中需要的字段
    return {
      // 培训完成率 - 用于合规状态模块
      courseCompletionRate: data.courseCompletionRate,
      
      // 当前学习的课程 - 用于学习培训模块主要课程显示
      currentCourse: data.courseProgresses?.find(course => !course.isCompleted && course.progressPercent > 0) || null,
      
      // 所有课程进度 - 用于推荐课程列表
      allCourses: data.courseProgresses || [],
      
      // 学习统计数据
      totalCourses: data.totalCourses,
      completedCourses: data.completedCourses,
      inProgressCourses: data.inProgressCourses,
      
      // 原始数据
      rawData: data
    }
  } catch (error) {
    console.error('获取学习进度概览失败:', error)
    throw error
  }
}

// 示例：获取推荐课程列表
export async function getRecommendedCourses(userId?: string) {
  try {
    const response = await taskApi.learningApi({ userId }, 'recommended')
    const courses: RecommendedCourse[] = response.data
    
    // 格式化课程数据以适配index.vue的显示需求
    return courses.map(course => ({
      id: course.id,
      name: course.courseName,
      cover: course.coverImageUrl,
      type: course.courseType,
      duration: course.durationHours || `${Math.ceil(course.totalDurationMinutes / 60)}小时`,
      reason: course.reason || '系统推荐'
    }))
  } catch (error) {
    console.error('获取推荐课程失败:', error)
    // 返回默认推荐课程（如果API不可用）
    return [
      {
        id: 1,
        name: '反腐败合规实务',
        cover: 'https://ai-public.mastergo.com/ai/img_res/0fd2b91d26f6d4f487e0a81dce86b2a3.jpg',
        type: '合规培训',
        duration: '1.5小时',
        reason: '基于您的学习历史推荐'
      },
      {
        id: 2,
        name: '数据保护合规指南',
        cover: 'https://ai-public.mastergo.com/ai/img_res/29f9c738a94a077e07e18df49ec435d1.jpg',
        type: '数据保护',
        duration: '2小时',
        reason: '热门课程推荐'
      }
    ]
  }
}

// 示例：格式化课程进度数据用于index.vue显示
export function formatCourseForDisplay(course: CourseProgressDTO) {
  return {
    id: course.courseId,
    name: course.courseName,
    cover: course.coverImageUrl,
    progress: Math.round(course.progressPercent),
    progressText: `${Math.round(course.progressPercent)}%`,
    remainingTime: calculateRemainingTime(course),
    isCompleted: course.isCompleted,
    currentChapter: course.currentChapterTitle
  }
}

// 计算剩余学习时间
function calculateRemainingTime(course: CourseProgressDTO): string {
  const totalMinutes = course.totalDurationMinutes
  const studiedMinutes = course.studiedDurationMinutes
  const remainingMinutes = totalMinutes - studiedMinutes
  
  if (remainingMinutes <= 0) {
    return '已完成'
  }
  
  const hours = Math.ceil(remainingMinutes / 60)
  return `还需${hours}小时`
}

// 示例：在Vue组件中的使用方法
/*
// 在Vue组件的setup()函数中使用：

import { ref, onMounted } from 'vue'
import { getUserLearningOverview, getRecommendedCourses, formatCourseForDisplay } from '@/api/todoTask/example'

export default {
  setup() {
    const learningData = ref(null)
    const recommendedCourses = ref([])
    const currentCourse = ref(null)
    
    // 获取学习数据
    async function loadLearningData() {
      try {
        const overview = await getUserLearningOverview()
        learningData.value = overview
        
        // 格式化当前学习课程
        if (overview.currentCourse) {
          currentCourse.value = formatCourseForDisplay(overview.currentCourse)
        }
        
        // 获取推荐课程
        recommendedCourses.value = await getRecommendedCourses()
      } catch (error) {
        console.error('加载学习数据失败:', error)
      }
    }
    
    onMounted(() => {
      loadLearningData()
    })
    
    return {
      learningData,
      recommendedCourses,
      currentCourse,
      loadLearningData
    }
  }
}
*/