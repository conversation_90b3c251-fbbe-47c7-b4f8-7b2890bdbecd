<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Calendar,
  Check,
  Document,
  Edit,
  Key,
  Monitor,
  More,
  PieChart,
  Search,
  Setting,
  Share,
  Star,
  Top,
  User,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import experienceApi from '@/api/monitor/exprience'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'

const router = useRouter()
const viewType = ref('table')
const activeTab = ref('type')
const timeRange = ref('month')

// 响应式数据
const tableData = ref<any[]>([])
const loading = ref(false)
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
})

// 筛选字段
const filterForm = ref({
  title: '',
  experienceCode: '',
  experienceType: '',
  experienceSource: '',
  level: '',
  sourceCode: '',
  dutyEmployeeOrgId: null,
  status: '',
  createdAtStart: null,
  createdAtEnd: null,
})

// 日期范围绑定变量
const dateRange = ref<[Date, Date] | undefined>(undefined)

function handleAdd() {
  router.push({
    path: '/respond/improveAndOptimize/experienceAndLesson/addEdit',
  })
}

function queryList() {
  pagination.value.page = 1
  getExperienceList()
}

function resetList() {
  filterForm.value = {
    title: '',
    experienceCode: '',
    experienceType: '',
    experienceSource: '',
    level: '',
    sourceCode: '',
    dutyEmployeeOrgId: null,
    status: '',
    createdAtStart: null,
    createdAtEnd: null,
  }
  dateRange.value = undefined
  pagination.value.page = 1
  getExperienceList()
}
// 获取经验教训列表数据
async function getExperienceList() {
  try {
    loading.value = true
    const params = {
      title: filterForm.value.title || null,
      experienceCode: filterForm.value.experienceCode || null,
      experienceType: filterForm.value.experienceType || null,
      experienceSource: filterForm.value.experienceSource || null,
      level: filterForm.value.level || null,
      sourceCode: filterForm.value.sourceCode || null,
      dutyEmployeeOrgId: filterForm.value.dutyEmployeeOrgId || null,
      status: filterForm.value.status || null,
      createdAtStart: filterForm.value.createdAtStart || null,
      createdAtEnd: filterForm.value.createdAtEnd || null,
    }

    const response = await experienceApi.experienceList(pagination.value, params)
    if (response) {
      // 转换API数据格式为页面需要的格式
      tableData.value = response.content?.map((item: any) => ({
        id: item.id,
        title: item.title || '',
        type: getTypeLabel(item.experienceType),
        source: getSourceLabel(item.experienceSource),
        severity: getLevelLabel(item.level),
        department: item.dutyEmployeeOrgName || '',
        creator: item.createdBy || '',
        createTime: item.createdAt,
        status: getStatusLabel(item.status),
        sourceCode: item.sourceCode || '',
        dutyEmployeeOrgName: item.dutyEmployeeOrgName || '',
      })) || []

      pagination.value.total = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('获取经验教训列表失败:', error)
    // 保留原有硬编码数据作为fallback
    tableData.value = []
  }
  finally {
    loading.value = false
  }
}

// 数据转换辅助函数
function getTypeLabel(type: string) {
  const typeMap: Record<string, string> = {
    PROCESS_ISSUE: '流程问题',
    PERSONNEL_ISSUE: '人员问题',
    SYSTEM_ISSUE: '系统问题',
    POLICY_ISSUE: '制度问题',
    OTHER: '其他',
  }
  return typeMap[type] || type
}

function getSourceLabel(source: string) {
  const sourceMap: Record<string, string> = {
    AUDIT_FINDING: '审计发现',
    EMPLOYEE_SUBMISSION: '员工提交',
    PROCESSING_RESULT: '处理结果',
  }
  return sourceMap[source] || source
}

function getLevelLabel(level: string) {
  const levelMap: Record<string, string> = {
    LOW: '低',
    MIDDLE: '中',
    HIGH: '高',
  }
  return levelMap[level] || level
}

function getStatusLabel(status: string) {
  const statusMap: Record<string, string> = {
    DRAFT: '草稿',
    PUBLISHED: '已发布',
    ARCHIVED: '已归档',
  }
  return statusMap[status] || status
}

function _formatDate(dateObj: any) {
  if (!dateObj) {
    return ''
  }
  if (dateObj.seconds) {
    return new Date(dateObj.seconds * 1000).toLocaleDateString()
  }
  return dateObj
}

const _contributionData: any[] = [

]

const _hotData: any[] = [

]
function getTagType(type: string) {
  switch (type) {
    case '流程问题': return 'primary'
    case '人员问题': return 'success'
    case '制度问题': return 'warning'
    case '系统问题': return 'danger'
    default: return 'info'
  }
}
function getPriorityTagType(priority: string) {
  switch (priority) {
    case '高': return 'danger'
    case '中': return 'warning'
    case '低': return 'success'
    default: return 'info'
  }
}
function getStatusTagType(status: string) {
  switch (status) {
    case '已发布': return 'success'
    case '草稿': return 'info'
    case '已归档': return 'warning'
    default: return 'info'
  }
}

// 分页处理
function handlePageChange(page: number) {
  pagination.value.page = page
  getExperienceList()
}

// 日期范围处理
function handleDateRangeChange(dateRange: any) {
  if (dateRange && dateRange.length === 2) {
    // 格式化日期为 YYYY-MM-DD 格式
    const startDate = new Date(dateRange[0])
    const endDate = new Date(dateRange[1])
    filterForm.value.createdAtStart = startDate.toISOString().split('T')[0]
    filterForm.value.createdAtEnd = endDate.toISOString().split('T')[0]
  }
  else {
    filterForm.value.createdAtStart = null
    filterForm.value.createdAtEnd = null
  }
}

// 编辑方法
function handleEdit(row: any) {
  router.push({
    path: '/respond/improveAndOptimize/experienceAndLesson/addEdit',
    query: {
      id: row.id,
      mode: 'edit',
    },
  })
}

// 删除方法
function handleDel(row: any) {
  ElMessageBox.confirm(
    '确定要删除这条经验教训吗？删除后将无法恢复。',
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger',
    },
  ).then(async () => {
    try {
      await experienceApi.deleteExperience(row.id)
      ElMessage.success('删除成功')
      // 重新加载列表
      getExperienceList()
    }
    catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 查看方法
function handleView(row: any) {
  router.push({
    path: '/respond/improveAndOptimize/experienceAndLesson/detail',
    query: {
      id: row.id,
    },
  })
}

// 页面初始化
onMounted(() => {
  getExperienceList()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              经验教训库
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'experienceAndLesson/index/save'" type="primary" class="!rounded-button whitespace-nowrap" @click="handleAdd">
              新增经验教训
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row class="!mx-0" :gutter="20">
          <el-col :span="24" :lg="24" :md="24" :sm="24" :xs="24">
            <!-- 页面内容 -->
            <div class="p-6">
              <div class="pr-6">
                <!-- 统计卡片区 -->
                <div v-if="false" class="grid grid-cols-4 mb-6 gap-4">
                  <el-card shadow="hover" class="!rounded-lg">
                    <div class="flex items-center">
                      <div class="mr-3 h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                        <el-icon class="text-xl text-blue-500">
                          <Document />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm text-gray-500">
                          总条目数
                        </div>
                        <div class="text-2xl font-bold">
                          1,248
                        </div>
                        <div class="flex items-center text-xs text-green-500">
                          <el-icon>
                            <Top />
                          </el-icon>
                          <span>12.5%</span>
                        </div>
                      </div>
                    </div>
                  </el-card>
                  <el-card shadow="hover" class="!rounded-lg">
                    <div class="flex items-center">
                      <div class="mr-3 h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
                        <el-icon class="text-xl text-green-500">
                          <Calendar />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm text-gray-500">
                          本月新增
                        </div>
                        <div class="text-2xl font-bold">
                          86
                        </div>
                        <div class="flex items-center text-xs text-green-500">
                          <el-icon>
                            <Top />
                          </el-icon>
                          <span>5.2%</span>
                        </div>
                      </div>
                    </div>
                  </el-card>
                  <el-card shadow="hover" class="!rounded-lg">
                    <div class="flex items-center">
                      <div class="mr-3 h-12 w-12 flex items-center justify-center rounded-full bg-purple-100">
                        <el-icon class="text-xl text-purple-500">
                          <PieChart />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm text-gray-500">
                          来源分布
                        </div>
                        <div class="text-sm text-gray-600">
                          审计发现 45%
                        </div>
                        <div class="text-xs text-gray-500">
                          员工提交 32%
                        </div>
                      </div>
                    </div>
                  </el-card>
                  <el-card shadow="hover" class="!rounded-lg">
                    <div class="flex items-center">
                      <div class="mr-3 h-12 w-12 flex items-center justify-center rounded-full bg-orange-100">
                        <el-icon class="text-xl text-orange-500">
                          <view />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm text-gray-500">
                          阅读总量
                        </div>
                        <div class="text-2xl font-bold">
                          8,742
                        </div>
                        <div class="flex items-center text-xs text-green-500">
                          <el-icon>
                            <Top />
                          </el-icon>
                          <span>18.3%</span>
                        </div>
                      </div>
                    </div>
                  </el-card>
                </div>
                <!-- 最近访问区 -->
                <el-card v-if="false" shadow="hover" class="mb-6 !rounded-lg">
                  <div class="mb-3 text-gray-800 font-medium">
                    最近访问
                  </div>
                  <div class="space-y-3">
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-blue-100">
                        <el-icon class="text-blue-500">
                          <Document />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          销售合同审批流程优化
                        </div>
                        <div class="text-xs text-gray-500">
                          流程问题 · 审计发现
                        </div>
                      </div>
                    </div>
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-green-100">
                        <el-icon class="text-green-500">
                          <User />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          新员工合规培训缺失
                        </div>
                        <div class="text-xs text-gray-500">
                          人员问题 · 员工提交
                        </div>
                      </div>
                    </div>
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-purple-100">
                        <el-icon class="text-purple-500">
                          <Setting />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          报销制度漏洞修复
                        </div>
                        <div class="text-xs text-gray-500">
                          制度问题 · 处理结果
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="mt-3 cursor-pointer text-sm text-blue-500">
                    查看全部 >
                  </div>
                </el-card>
                <!-- 热门推荐区 -->
                <el-card v-if="false" shadow="hover" class="mb-6 !rounded-lg">
                  <div class="mb-3 text-gray-800 font-medium">
                    热门推荐
                  </div>
                  <div class="space-y-3">
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-orange-100">
                        <el-icon class="text-orange-500">
                          <Star />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          数据隐私保护最佳实践
                        </div>
                        <div class="text-xs text-gray-500">
                          阅读量 1,245 · 点赞 86
                        </div>
                      </div>
                    </div>
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-blue-100">
                        <el-icon class="text-blue-500">
                          <Star />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          反洗钱系统使用指南
                        </div>
                        <div class="text-xs text-gray-500">
                          阅读量 982 · 点赞 72
                        </div>
                      </div>
                    </div>
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-green-100">
                        <el-icon class="text-green-500">
                          <Star />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          跨部门协作沟通技巧
                        </div>
                        <div class="text-xs text-gray-500">
                          阅读量 856 · 点赞 65
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="mt-3 cursor-pointer text-sm text-blue-500">
                    查看全部 >
                  </div>
                </el-card>
                <!-- 筛选区 -->
                <el-card shadow="hover" class="mb-6 !rounded-lg">
                  <div class="mb-4 flex items-center space-x-4">
                    <el-select v-model="filterForm.experienceType" placeholder="类型" class="w-48">
                      <el-option label="流程问题" value="PROCESS_ISSUE" />
                      <el-option label="人员问题" value="PERSONNEL_ISSUE" />
                      <el-option label="系统问题" value="SYSTEM_ISSUE" />
                      <el-option label="制度问题" value="POLICY_ISSUE" />
                      <el-option label="其他" value="OTHER" />
                    </el-select>
                    <el-select v-model="filterForm.experienceSource" placeholder="来源" class="w-48">
                      <el-option label="审计发现" value="AUDIT_FINDING" />
                      <el-option label="员工提交" value="EMPLOYEE_SUBMISSION" />
                      <el-option label="处理结果" value="PROCESSING_RESULT" />
                    </el-select>
                    <el-select v-model="filterForm.level" placeholder="优先级" class="w-48">
                      <el-option label="低" value="LOW" />
                      <el-option label="中" value="MIDDLE" />
                      <el-option label="高" value="HIGH" />
                    </el-select>
                    <DepartmentTreeSelect
                      v-model="filterForm.dutyEmployeeOrgId"
                      placeholder="责任部门"
                      width="192px"
                      clearable
                    />
                    <el-date-picker
                      v-model="dateRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="w-64"
                      @change="handleDateRangeChange"
                    />
                    <el-input v-model="filterForm.title" placeholder="关键字搜索" class="w-48">
                      <template #prefix>
                        <el-icon>
                          <Search />
                        </el-icon>
                      </template>
                    </el-input>
                  </div>
                  <div class="flex justify-end">
                    <el-button v-auth="'experienceAndLesson/index/inquire'" type="primary" class="!rounded-button whitespace-nowrap" @click="queryList">
                      查询
                    </el-button>
                    <el-button v-auth="'experienceAndLesson/index/reset'" class="!rounded-button whitespace-nowrap" @click="resetList">
                      重置
                    </el-button>
                  </div>
                </el-card>
                <!-- 经验教训列表区 -->
                <el-card shadow="hover" class="mb-6 !rounded-lg">
                  <div class="mb-4 flex items-center justify-between">
                    <div class="flex space-x-2">
                      <!-- <el-button v-auth="'experienceAndLesson/index/batchImport'" size="small" class="!rounded-button whitespace-nowrap">
                        批量导入
                      </el-button>
                      <el-button v-auth="'experienceAndLesson/index/export'" size="small" class="!rounded-button whitespace-nowrap">
                        导出
                      </el-button> -->
                      <!-- <el-button v-auth="'experienceAndLesson/index/export'" class="!rounded-button whitespace-nowrap" @click="queryList">
                        查询
                      </el-button>
                      <el-button v-auth="'experienceAndLesson/index/export'" class="!rounded-button whitespace-nowrap" @click="resetList">
                        重置
                      </el-button> -->
                      <!-- <el-radio-group v-model="viewType" size="small">
                        <el-radio-button v-auth="'experienceAndLesson/index/tableView'" label="table">
                          表格视图
                        </el-radio-button>
                        <el-radio-button v-auth="'experienceAndLesson/index/cardView'" label="card">
                          卡片视图
                        </el-radio-button>
                      </el-radio-group> -->
                    </div>
                  </div>
                  <el-table v-if="viewType === 'table'" v-loading="loading" :data="tableData" style="width: 100%;">
                    <el-table-column type="selection" width="50" />
                    <el-table-column prop="title" label="标题" width="180" />
                    <el-table-column prop="type" label="类型" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getTagType(row.type)" size="small">
                          {{ row.type }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="source" label="来源" width="120" />
                    <el-table-column prop="severity" label="优先级" width="100">
                      <template #default="{ row }">
                        <el-tag :type="getPriorityTagType(row.severity)" size="small">
                          {{ row.severity }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="dutyEmployeeOrgName" label="责任部门" width="120" />
                    <el-table-column prop="creator" label="创建人" width="120" />
                    <el-table-column prop="createTime" label="创建日期" width="120" />
                    <el-table-column prop="status" label="状态" width="80">
                      <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status)" size="small">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="220">
                      <template #default="{ row }">
                        <el-button v-auth="'experienceAndLesson/index/view'" size="small" plain @click="handleView(row)">
                          查看
                        </el-button>
                        <el-button v-auth="'experienceAndLesson/index/edit'" type="primary" plain size="small" @click="handleEdit(row)">
                          编辑
                        </el-button>
                        <el-button v-auth="'experienceAndLesson/index/delete'" type="danger" plain size="small" @click="handleDel(row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div v-else class="grid grid-cols-3 gap-4">
                    <el-card v-for="item in tableData" :key="item.id" shadow="hover" class="!rounded-lg">
                      <div class="mb-2 flex items-start justify-between">
                        <div class="text-gray-800 font-medium">
                          {{ item.title }}
                        </div>
                        <el-tag :type="getTagType(item.type)" size="small">
                          {{ item.type }}
                        </el-tag>
                      </div>
                      <div class="mb-2 flex items-center justify-between">
                        <el-tag :type="getPriorityTagType(item.severity)" size="small">
                          {{ item.severity }}
                        </el-tag>
                        <div class="text-xs text-gray-500">
                          {{ item.createTime }}
                        </div>
                      </div>
                      <div class="line-clamp-2 mb-3 text-sm text-gray-600">
                        这是一条关于{{ item.type }}的经验教训描述内容，详细说明了问题的发现过程和解决方案...
                      </div>
                      <div class="flex items-center justify-between text-xs text-gray-500">
                        <div>{{ item.creator }} · {{ item.department }}</div>
                        <div class="flex space-x-2">
                          <span class="flex items-center">
                            <el-icon class="mr-1">
                              <view />
                            </el-icon>
                            {{ item.views }}
                          </span>
                          <span class="flex items-center">
                            <el-icon class="mr-1">
                              <Star />
                            </el-icon>
                            {{ item.likes }}
                          </span>
                        </div>
                      </div>
                      <div class="mt-3 flex justify-end border-t border-gray-100 pt-3 space-x-2">
                        <el-button v-auth="'experienceAndLesson/index/view'" size="small" class="!rounded-button whitespace-nowrap" type="text">
                          <el-icon>
                            <view />
                          </el-icon>
                        </el-button>
                        <el-button v-auth="'experienceAndLesson/index/edit'" size="small" class="!rounded-button whitespace-nowrap" type="text">
                          <el-icon>
                            <Edit />
                          </el-icon>
                        </el-button>
                        <el-button v-auth="'experienceAndLesson/index/share'" size="small" class="!rounded-button whitespace-nowrap" type="text">
                          <el-icon>
                            <Share />
                          </el-icon>
                        </el-button>
                      </div>
                    </el-card>
                  </div>
                  <div class="mt-4 flex justify-center">
                    <el-pagination
                      :total="pagination.total"
                      :page-size="pagination.size"
                      :current-page="pagination.page"
                      layout="prev, pager, next, total"
                      @current-change="handlePageChange"
                    />
                  </div>
                </el-card>
                <!-- 分类浏览区 -->
                <el-card v-if="false" shadow="hover" class="mb-6 !rounded-lg">
                  <div class="mb-4 text-gray-800 font-medium">
                    分类浏览
                  </div>
                  <el-tabs v-model="activeTab">
                    <el-tab-pane label="按类型" name="type">
                      <div class="grid grid-cols-5 gap-4">
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                            <el-icon class="text-xl text-blue-500">
                              <Document />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            流程问题
                          </div>
                          <div class="text-sm text-gray-500">
                            328 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div
                            class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-green-100"
                          >
                            <el-icon class="text-xl text-green-500">
                              <User />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            人员问题
                          </div>
                          <div class="text-sm text-gray-500">
                            256 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div
                            class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-purple-100"
                          >
                            <el-icon class="text-xl text-purple-500">
                              <Setting />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            制度问题
                          </div>
                          <div class="text-sm text-gray-500">
                            198 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div
                            class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-orange-100"
                          >
                            <el-icon class="text-xl text-orange-500">
                              <Monitor />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            系统问题
                          </div>
                          <div class="text-sm text-gray-500">
                            156 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-gray-100">
                            <el-icon class="text-xl text-gray-500">
                              <More />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            其他
                          </div>
                          <div class="text-sm text-gray-500">
                            310 条
                          </div>
                        </el-card>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="按来源" name="source">
                      <div class="grid grid-cols-5 gap-4">
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                            <el-icon class="text-xl text-blue-500">
                              <Search />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            审计发现
                          </div>
                          <div class="text-sm text-gray-500">
                            562 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div
                            class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-green-100"
                          >
                            <el-icon class="text-xl text-green-500">
                              <User />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            员工提交
                          </div>
                          <div class="text-sm text-gray-500">
                            399 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div
                            class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-purple-100"
                          >
                            <el-icon class="text-xl text-purple-500">
                              <Check />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            处理结果
                          </div>
                          <div class="text-sm text-gray-500">
                            287 条
                          </div>
                        </el-card>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </el-card>
                <!-- 热门经验教训区 -->
                <el-card v-if="false" shadow="hover" class="!rounded-lg">
                  <div class="mb-4 flex items-center justify-between">
                    <div class="text-gray-800 font-medium">
                      热门经验教训
                    </div>
                    <el-select v-model="timeRange" size="small" class="w-48">
                      <el-option label="本周" value="week" />
                      <el-option label="本月" value="month" />
                      <el-option label="本季度" value="quarter" />
                      <el-option label="本年" value="year" />
                    </el-select>
                  </div>
                  <el-table :data="_hotData" style="width: 100%;">
                    <el-table-column prop="rank" label="排名" width="80">
                      <template #default="{ row }">
                        <span
                          v-if="row.rank <= 3" class="font-bold"
                          :class="{ 'text-red-500': row.rank === 1, 'text-orange-500': row.rank === 2, 'text-yellow-500': row.rank === 3 }"
                        >
                          {{ row.rank }}
                        </span>
                        <span v-else>{{ row.rank }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="title" label="标题">
                      <template #default="{ row }">
                        <span class="cursor-pointer hover:text-blue-500">{{ row.title }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="type" label="类型" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getTagType(row.type)" size="small">
                          {{ row.type }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="source" label="来源" width="120" />
                    <el-table-column prop="views" label="阅读量" width="100" />
                    <el-table-column prop="likes" label="点赞数" width="100" />
                    <el-table-column prop="date" label="发布日期" width="120" />
                  </el-table>
                  <div class="mt-3 cursor-pointer text-sm text-blue-500">
                    查看更多 >
                  </div>
                </el-card>
              </div>
            </div>
          </el-col>
          <el-col v-if="false" :span="6" :lg="6" :md="8" :sm="24" :xs="24">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  我的收藏
                </div>
              </template>
              <div class="space-y-3">
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    销售合同审批流程优化
                  </div>
                  <div class="text-xs text-gray-500">
                    收藏于 2023-06-15
                  </div>
                </div>
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    新员工合规培训缺失
                  </div>
                  <div class="text-xs text-gray-500">
                    收藏于 2023-07-02
                  </div>
                </div>
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    报销制度漏洞修复
                  </div>
                  <div class="text-xs text-gray-500">
                    收藏于 2023-05-28
                  </div>
                </div>
              </div>
              <div v-auth="'experienceAndLesson/index/manageFavorites'" class="mt-3 cursor-pointer text-sm text-blue-500">
                管理收藏夹
              </div>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  热门标签
                </div>
              </template>
              <div class="flex flex-wrap gap-2">
                <el-tag size="small" class="cursor-pointer">
                  合同管理
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  数据隐私
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  反洗钱
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  培训
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  报销
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  权限管理
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  供应商
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  合规检查
                </el-tag>
              </div>
              <div v-auth="'experienceAndLesson/index/viewAllTags'" class="mt-3 cursor-pointer text-sm text-blue-500">
                查看全部标签 >
              </div>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  学习建议
                </div>
              </template>
              <div class="space-y-3">
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    跨部门协作沟通技巧
                  </div>
                  <div class="text-xs text-gray-500">
                    基于您近期查看的协作问题
                  </div>
                </div>
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    数据隐私保护最佳实践
                  </div>
                  <div class="text-xs text-gray-500">
                    与您部门相关的高频问题
                  </div>
                </div>
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    反洗钱系统使用指南
                  </div>
                  <div class="text-xs text-gray-500">
                    您团队近期关注的领域
                  </div>
                </div>
              </div>
              <div v-auth="'experienceAndLesson/index/viewAllSuggestions'" class="mt-3 cursor-pointer text-sm text-blue-500">
                查看全部建议 >
              </div>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  贡献排行
                </div>
              </template>
              <el-table :data="_contributionData" size="small" class="w-full">
                <el-table-column prop="rank" label="排名" width="50" />
                <el-table-column prop="name" label="用户" width="80" />
                <el-table-column prop="department" label="部门" />
                <el-table-column prop="count" label="贡献" />
                <el-table-column prop="likes" label="点赞" />
              </el-table>
              <div v-auth="'experienceAndLesson/index/myContribution'" class="mt-3 cursor-pointer text-sm text-blue-500">
                我的贡献 >
              </div>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  AI助手
                </div>
              </template>
              <div class="space-y-2">
                <el-button v-auth="'experienceAndLesson/index/aiRecommend'" size="small" class="!rounded-button w-full whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Star />
                  </el-icon>
                  推荐相关教训
                </el-button>
                <el-button v-auth="'experienceAndLesson/index/aiSummary'" size="small" class="!rounded-button w-full whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Document />
                  </el-icon>
                  生成教训摘要
                </el-button>
                <el-button v-auth="'experienceAndLesson/index/aiExtract'" size="small" class="!rounded-button w-full whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Key />
                  </el-icon>
                  提取关键经验
                </el-button>
              </div>
              <div class="mt-3 rounded bg-gray-50 p-2 text-sm text-gray-600">
                根据您最近的浏览记录，建议关注"数据隐私保护"相关经验教训。
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .line-clamp-2 {
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
</style>
