<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import {
  ArrowDown as ElIconArrowDown,
  DataAnalysis as ElIconDataAnalysis,
  Document as ElIconDocument,
  Download as ElIconDownload,
  Plus as ElIconPlus,
  Refresh as ElIconRefresh,
  Search as ElIconSearch,
  SetUp as ElIconSetUp,
  Tickets as ElIconTickets,
} from '@element-plus/icons-vue'
import otherApi from '@/api/review/other'

// 路由
const router = useRouter()

// 加载状态
const loading = ref(false)

// 标签页
const activeTab = ref('other')
// 筛选条件
const filter = ref({
  reviewCode: null,
  name: null,
  reviewObject: null,
  status: null,
  level: null,
  department: null,
  auditNameBy: null,
  // dateRange: [] as string[],
  keyword: null,
  reviewer: null,
  result: null,
  risk: null,
  // complianceTypes: [] as string[],
})

// 表格数据
const contractData = ref<any[]>([])
// 重大决策数据
const decisionData = ref<any[]>([])
// 其他审查数据
const otherData = ref<ReviewItem[]>([])

// 分页数据
const paging = ref({
  page: 1,
  limit: 10,
  total: 0,
})

// 选择项
const selectedOtherItems = ref<ReviewItem[]>([])
const selectAllOther = ref(false)
// 我的审查
const myReviews = ref<ReviewItem[]>([])
// 定义数据类型
interface TrendDataItem {
  month: string
  count: number
}

interface ReviewItem {
  id: string
  title?: string
  status: string
  deadline?: string
  [key: string]: any
}

// 审查趋势数据
const trendData = ref<TrendDataItem[]>([])

// 统计数据
const otherStatistic = ref({})

// 获取其他审查统计数据
async function getOtherStatistic() {
  try {
    otherStatistic.value = await otherApi.getSupplementalStatistic()
  }
  catch (error) {
    console.error('获取其他审查统计数据失败:', error)
  }
}
// 初始化图表
const trendChart = ref<HTMLDivElement | null>(null)
const pieChart = ref<HTMLDivElement | null>(null)
let trendChartInstance: echarts.ECharts | null = null
let pieChartInstance: echarts.ECharts | null = null

function initCharts() {
  if (trendChart.value) {
    trendChartInstance = echarts.init(trendChart.value)
    const trendOption = {
      animation: false,
      xAxis: {
        type: 'category',
        data: trendData.value.map((item: TrendDataItem) => item.month),
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          data: trendData.value.map((item: TrendDataItem) => item.count),
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#409EFF',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(64, 158, 255, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(64, 158, 255, 0.1)',
              },
            ]),
          },
        },
      ],
    }
    trendChartInstance.setOption(trendOption)

    if (pieChart.value) {
      pieChartInstance = echarts.init(pieChart.value)
      const pieOption = {
        animation: false,
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data:
              activeTab.value === 'contract'
                ? ['采购合同', '销售合同', '服务合同', '劳动合同', '技术许可合同', '其他合同']
                : activeTab.value === 'decision'
                  ? ['投资决策', '战略规划', '重组决策', '融资决策', '重大采购']
                  : ['安全合规', '品质合规', '环保合规', 'IT合规', '数据合规', '人事合规', '其他'],
        },
        series: [
          {
            name: '分布比例',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data:
                activeTab.value === 'contract'
                  ? [
                      { value: 32, name: '采购合同' },
                      { value: 28, name: '销售合同' },
                      { value: 18, name: '服务合同' },
                      { value: 10, name: '劳动合同' },
                      { value: 8, name: '技术许可合同' },
                      { value: 4, name: '其他合同' },
                    ]
                  : activeTab.value === 'decision'
                    ? [
                        { value: 38, name: '投资决策' },
                        { value: 24, name: '战略规划' },
                        { value: 18, name: '重组决策' },
                        { value: 12, name: '融资决策' },
                        { value: 8, name: '重大采购' },
                      ]
                    : [
                        { value: 26, name: '安全合规' },
                        { value: 20, name: '品质合规' },
                        { value: 16, name: '环保合规' },
                        { value: 14, name: 'IT合规' },
                        { value: 12, name: '数据合规' },
                        { value: 8, name: '人事合规' },
                        { value: 4, name: '其他' },
                      ],
          },
        ],
      }
      pieChartInstance.setOption(pieOption)
    }

    window.addEventListener('resize', () => {
      if (trendChartInstance) {
        trendChartInstance.resize()
      }
      if (pieChartInstance) {
        pieChartInstance.resize()
      }
    })
  }
}

watch(activeTab, () => {
  nextTick(() => {
    initCharts()
  })
})
// 方法
function getLevelTagType(level: string): 'success' | 'warning' | 'danger' | 'info' | 'primary' {
  const levelTypes: Record<string, 'success' | 'warning' | 'danger' | 'info' | 'primary'> = {
    CRITICAL: 'danger',
    IMPORTANT: 'warning',
    GENERAL: 'success',
  }
  return levelTypes[level] || 'primary'
}

function getStatusLabel(status: string): string {
  const statusLabels: Record<string, string> = {
    PENDING: '待审查',
    PUBLISHED: '发布',
    REVIEWING: '审核中',
    REVOKE: '已撤回',
    MODIFY: '需修改',
  }
  return statusLabels[status] || status
}

function getLevelLabel(level: string): string {
  const levelLabels: Record<string, string> = {
    CRITICAL: '关键',
    IMPORTANT: '重要',
    GENERAL: '一般',
  }
  return levelLabels[level] || level
}

function getStatusTagType(status: string): 'success' | 'warning' | 'danger' | 'info' | 'primary' {
  const statusTypes: Record<string, 'success' | 'warning' | 'danger' | 'info' | 'primary'> = {
    PENDING: 'info',
    PUBLISHED: 'success',
    REVIEWING: 'warning',
    REVOKE: 'danger',
    MODIFY: 'warning',
  }
  return statusTypes[status] || 'primary'
}

function handleOtherSelectionChange(val: ReviewItem[]) {
  selectedOtherItems.value = val
  selectAllOther.value = val.length === otherData.value.length
}

function resetFilter() {
  filter.value = {
    reviewCode: null,
    name: null,
    reviewObject: null,
    status: null,
    level: null,
    department: null,
    auditNameBy: null,
    // dateRange: [] as string[],
    keyword: null,
    reviewer: null,
    result: null,
    risk: null,
    // complianceTypes: [] as string[],
  }
}

// 查询按钮处理
function handleQuery() {
  paging.value.page = 1
  getOtherList()
}

// 重置按钮处理
function handleReset() {
  resetFilter()
  paging.value.page = 1
  getOtherList()
}

// 获取其他审查列表
async function getOtherList() {
  try {
    loading.value = true
    const params = {
      ...filter.value,
    }
    delete (params as any).dateRange

    const response = await otherApi.supplementalReview(paging.value, params, 'get')
    if (response) {
      const pageData = response
      otherData.value = pageData.content || []
      paging.value.total = pageData.totalElements || 0

      // 通过统一接口获取其他数据
      contractData.value = pageData.contractReviews || []
      decisionData.value = pageData.decisionReviews || []
      myReviews.value = pageData.myReviews || []
      trendData.value = pageData.trendData || []
    }
  }
  catch (error) {
    ElMessage.error('获取数据失败')
  }
  finally {
    loading.value = false
  }
}

// 分页变化处理
function pagChange(page: number) {
  paging.value.page = page
  getOtherList()
}
// 查看其他审查详情
function viewOtherDetail(row: ReviewItem) {
  router.push({
    path: '/monitor/examination/ohter/detail',
    query: { id: row.id },
  })
}

// 编辑其他审查
function editOtherItem(row: ReviewItem) {
  router.push({
    path: '/monitor/examination/ohter/addEdit',
    query: { id: row.id },
  })
}

// 删除其他审查
async function deleteOtherItem(row: ReviewItem) {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条其他审查记录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await otherApi.supplementalReview(paging.value, { id: row.id }, 'delete')
    ElMessage.success('删除成功')
    getOtherList()
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 导出其他审查
function exportOtherItem(_row: ReviewItem) {
  ElMessage.info('导出功能开发中')
}

// 撤回其他审查
function withdrawOtherItem(_row: ReviewItem) {
  ElMessage.info('撤回功能开发中')
}

// 新增其他审查
function addOtherItem() {
  router.push('/monitor/examination/ohter/addEdit')
}

// 格式化日期
function formatDate(dateObj: any) {
  if (!dateObj || !dateObj.seconds) {
    return '-'
  }
  const date = new Date(dateObj.seconds * 1000)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 页面初始化
onMounted(() => {
  getOtherList()
  getOtherStatistic()
  initCharts()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              其他审查
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'otherReviews/index/add'" v-debounce="2000" type="primary" @click="addOtherItem">
              <!-- <el-icon class="mr-2">
                <ElIconPlus />
              </el-icon> -->
              新增审查
            </el-button>
            <!-- <el-button v-auth="'otherReviews/index/export'" v-debounce="2000">
              <el-icon class="mr-2">
                <ElIconDownload />
              </el-icon>
              导出数据
            </el-button> -->
            <el-button v-auth="'otherReviews/index/statistics'" v-debounce="2000">
              <el-icon class="mr-2">
                <ElIconDataAnalysis />
              </el-icon>
              审查统计
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!-- <template #header>
                <div class="f-16 fw-600">基本信息</div>
              </template> -->
              <el-tabs v-model="activeTab" class="px-6 pt-4">
                <!-- <el-tab-pane label="其他审查" name="other"> -->
                <div class="p-6">
                  <!-- 查询表单 -->
                  <el-form :model="filter" inline class="mb-4">
                    <el-form-item label="审查编号">
                      <el-input v-model="filter.reviewCode" placeholder="请输入审查编号" clearable style="width: 180px" />
                    </el-form-item>
                    <el-form-item label="审查名称">
                      <el-input v-model="filter.name" placeholder="请输入审查名称" clearable style="width: 180px" />
                    </el-form-item>
                    <!-- <el-form-item label="审查对象">
                      <el-input v-model="filter.reviewObject" placeholder="请输入审查对象" clearable style="width: 180px" />
                    </el-form-item> -->
                    <!-- <el-form-item label="发起部门">
                      <el-select v-model="filter.department" placeholder="请选择部门" clearable style="width: 150px">
                        <el-option label="产品部" value="产品部" />
                        <el-option label="IT部" value="IT部" />
                        <el-option label="财务部" value="财务部" />
                        <el-option label="数据部" value="数据部" />
                        <el-option label="业务部" value="业务部" />
                      </el-select>
                    </el-form-item> -->
                    <el-form-item label="审查级别">
                      <el-select v-model="filter.level" placeholder="请选择级别" clearable style="width: 120px">
                        <el-option label="关键" value="CRITICAL" />
                        <el-option label="重要" value="IMPORTANT" />
                        <el-option label="一般" value="GENERAL" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="状态">
                      <el-select v-model="filter.status" placeholder="请选择状态" clearable style="width: 120px">
                        <el-option label="待审查" value="PENDING" />
                        <el-option label="发布" value="PUBLISHED" />
                        <el-option label="审核中" value="REVIEWING" />
                        <el-option label="需修改" value="MODIFY" />
                        <el-option label="已撤回" value="REVOKE" />
                      </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="审查人">
                      <el-input v-model="filter.auditNameBy" placeholder="请输入审查人" clearable style="width: 120px" />
                    </el-form-item> -->
                    <el-form-item>
                      <el-button v-debounce="1000" type="primary" @click="handleQuery">
                        <el-icon class="mr-1">
                          <ElIconSearch />
                        </el-icon>
                        查询
                      </el-button>
                      <el-button v-debounce="1000" @click="handleReset">
                        <el-icon class="mr-1">
                          <ElIconRefresh />
                        </el-icon>
                        重置
                      </el-button>
                    </el-form-item>
                  </el-form>
                  <!-- 表格 -->
                  <el-table v-loading="loading" :data="otherData" style="width: 100%;" @selection-change="handleOtherSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="reviewCode" label="审查编号" width="150" />
                    <el-table-column prop="name" label="审查名称" width="200" />
                    <el-table-column prop="reviewObject" label="审查对象" width="150" />
                    <el-table-column prop="departmentName" label="发起部门" width="120" />
                    <el-table-column prop="level" label="审查级别" width="100">
                      <template #default="{ row }">
                        <el-tag :type="getLevelTagType(row.level)" size="small">
                          {{ getLevelLabel(row.level) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status)" size="small">
                          {{ getStatusLabel(row.status) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="auditNameBy" label="审查人" width="120" />
                    <el-table-column prop="deadlineDate" label="截止时间" width="150" />
                    <el-table-column prop="createdAt" label="创建时间" width="150" />
                    <el-table-column label="操作" width="220" fixed="right">
                      <template #default="{ row }">
                        <el-button v-auth="'otherReviews/index/view'" v-debounce="2000" class="!ml-0" type="primary" plain size="small" @click="viewOtherDetail(row)">
                          查看
                        </el-button>
                        <el-button v-auth="'otherReviews/index/edit'" v-debounce="2000" type="warning" size="small" plain @click="editOtherItem(row)">
                          编辑
                        </el-button>
                        <el-button v-auth="'otherReviews/index/delete'" v-debounce="2000" type="danger" size="small" plain @click="deleteOtherItem(row)">
                          删除
                        </el-button>
                        <el-dropdown v-if="false">
                          <el-button type="text" size="small">
                            更多
                            <el-icon class="el-icon--right">
                              <ElIconArrowDown />
                            </el-icon>
                          </el-button>
                          <template #dropdown>
                            <!-- v-auth="'otherReviews/index/delete'"
                            v-auth="'otherReviews/index/exportItem'"
                            v-auth="'otherReviews/index/withdraw'" -->
                            <el-dropdown-menu>
                              <el-dropdown-item @click="deleteOtherItem(row)">
                                删除
                              </el-dropdown-item>
                              <el-dropdown-item @click="exportOtherItem(row)">
                                导出
                              </el-dropdown-item>
                              <el-dropdown-item @click="withdrawOtherItem(row)">
                                撤回
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </template>
                    </el-table-column>
                  </el-table>
                  <page-compon
                    :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
                    @pag-change="pagChange"
                  />
                  <!-- <div class="flex justify-between items-center mt-4">
                      <div class="text-sm text-gray-500">共 {{ otherTotalItems }} 条记录</div>
                      <el-pagination v-model:current-page="otherCurrentPage" :page-size="pageSize"
                        :total="otherTotalItems" layout="prev, pager, next, jumper"></el-pagination>
                    </div> -->
                </div>
                <!-- </el-tab-pane> -->
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  审查统计
                </div>
              </template>
              <div class="grid grid-cols-2 mb-6 gap-4">
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    总审查数
                  </div>
                  <div class="text-2xl font-bold">
                    {{ otherStatistic.totalCount || 0 }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    待审查
                  </div>
                  <div class="text-2xl text-blue-500 font-bold">
                    {{ otherStatistic.pendingCount || 0 }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    审查中
                  </div>
                  <div class="text-2xl text-yellow-500 font-bold">
                    {{ otherStatistic.reviewingCount || 0 }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    已完成
                  </div>
                  <div class="text-2xl text-green-500 font-bold">
                    {{ otherStatistic.completedCount || 0 }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    需修改
                  </div>
                  <div class="text-2xl text-red-500 font-bold">
                    {{ otherStatistic.modifyCount || 0 }}
                  </div>
                </div>
              </div>
              <!-- 审查分布图 -->
              <div v-if="false" class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    审查分布
                  </div>
                  <el-link type="primary" :underline="false">
                    查看更多
                  </el-link>
                </div>
                <div class="h-40">
                  <div ref="pieChart" class="h-full w-full" />
                </div>
              </div>
              <!-- 审查趋势图 -->
              <div v-if="false" class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    审查趋势
                  </div>
                  <el-link type="primary" :underline="false">
                    查看更多
                  </el-link>
                </div>
                <div class="h-40">
                  <!-- 折线图 -->
                  <div ref="trendChart" class="h-full w-full" />
                </div>
              </div>
              <!-- 我的审查 -->
              <div v-if="false" class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    我的审查
                  </div>
                  <el-link type="primary" :underline="false">
                    查看全部
                  </el-link>
                </div>
                <div class="space-y-3">
                  <div v-for="item in myReviews" :key="item.id" class="border-b pb-3 last:border-b-0 last:pb-0">
                    <div class="flex justify-between">
                      <div class="font-medium">
                        {{ item.title }}
                      </div>
                      <el-tag :type="getStatusTagType(item.status)" size="small">
                        {{ getStatusLabel(item.status) }}
                      </el-tag>
                    </div>
                    <div class="text-sm text-gray-500">
                      截止: {{ item.deadline }}
                    </div>
                  </div>
                </div>
              </div>
              <!-- 快捷功能 -->
              <div v-if="false" class="border rounded-lg p-4">
                <div class="mb-3 font-medium">
                  快捷功能
                </div>
                <div class="grid grid-cols-2 gap-3">
                  <button
                    v-auth="'otherReviews/index/templateManage'"
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconDocument />
                    </el-icon>
                    模板管理
                  </button>
                  <button
                    v-auth="'otherReviews/index/processConfig'"
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconSetUp />
                    </el-icon>
                    流程配置
                  </button>
                  <button
                    v-auth="'otherReviews/index/reportGenerate'"
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconTickets />
                    </el-icon>
                    报告生成
                  </button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  /* 自定义样式 */
  :deep(.el-tabs__header) {
    margin: 0;
  }

  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    background-color: #e4e7ed;
  }

  :deep(.el-tree) {
    background: transparent;
  }

  :deep(.el-tree-node__content) {
    height: 36px;
  }

  :deep(.el-table) {
    --el-table-border-color: #f0f0f0;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f8f8f8;
  }

  :deep(.el-table .el-table__cell) {
    padding: 12px 0;
  }

  :deep(.el-table .cell) {
    padding-right: 16px;
    padding-left: 16px;
  }
</style>
