<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Close, Search } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import problemTaskApi from '@/api/problemTask'

// 定义组件属性
interface Props {
  modelValue?: any
  displayValue?: string // 外部提供的显示值，用于回显
  valueKey?: string // 返回值的字段名，默认为 'id'
  displayKey?: string // 显示值的字段名，默认为 'title'
  placeholder?: string
}

// 定义事件
interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any, row: any): void
}

const props = withDefaults(defineProps<Props>(), {
  valueKey: 'id',
  displayKey: 'title',
  placeholder: '请选择违规问题调查',
})

const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const tableData = ref<any[]>([])
const selectedRow = ref<any>(null)
const tableRef = ref()

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
})

// 搜索参数
const searchParams = reactive({
  title: '',
  investigateCode: '',
  status: '',
})

// 计算显示值
const displayValue = computed(() => {
  // 如果外部提供了显示值，优先使用外部显示值
  if (props.displayValue) {
    return props.displayValue
  }

  if (!props.modelValue) { return '' }

  // 如果当前选中的行存在，直接使用
  if (selectedRow.value && selectedRow.value[props.valueKey] === props.modelValue) {
    return selectedRow.value[props.displayKey]
  }

  // 否则从表格数据中查找
  const foundRow = tableData.value.find(row => row[props.valueKey] === props.modelValue)
  return foundRow ? foundRow[props.displayKey] : props.modelValue
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && tableData.value.length > 0) {
    const foundRow = tableData.value.find(row => row[props.valueKey] === newValue)
    if (foundRow) {
      selectedRow.value = foundRow
    }
  }
  else {
    selectedRow.value = null
  }
}, { immediate: true })

// 打开弹窗
async function openDialog() {
  dialogVisible.value = true
  await fetchTableData()

  // 如果有当前值，需要回显选中状态
  if (props.modelValue && tableData.value.length > 0) {
    const foundRow = tableData.value.find(row => row[props.valueKey] === props.modelValue)
    if (foundRow) {
      selectedRow.value = foundRow
      // 设置表格当前行
      setTimeout(() => {
        if (tableRef.value) {
          tableRef.value.setCurrentRow(foundRow)
        }
      }, 100)
    }
  }
}

// 关闭弹窗
function handleClose() {
  dialogVisible.value = false
  // 不重置搜索条件，保持用户的搜索状态
}

// 清空选择
function handleClear() {
  selectedRow.value = null
  // 清除表格选中状态
  if (tableRef.value) {
    tableRef.value.setCurrentRow(null)
  }
  emit('update:modelValue', null)
  emit('change', null, null)
}

// 获取表格数据
async function fetchTableData() {
  loading.value = true
  try {
    const params: any = {
      title: searchParams.title || undefined,
      investigateCode: searchParams.investigateCode || undefined,
      status: searchParams.status || undefined,
    }

    const res = await problemTaskApi.searchTasks(params, pagination.page - 1, pagination.size)
    if (res) {
      tableData.value = res.content || []
      pagination.total = res.totalElements || 0
    }
    else {
      ElMessage.error('获取数据失败')
    }
  }
  catch (error) {
    console.error('获取数据失败', error)
    ElMessage.error('获取数据失败')
  }
  finally {
    loading.value = false
  }
}

// 处理当前行变化
function handleCurrentChange(currentRow: any) {
  selectedRow.value = currentRow
}

// 处理行点击
function handleRowClick(row: any) {
  selectedRow.value = row
  if (tableRef.value) {
    tableRef.value.setCurrentRow(row)
  }
}

// 处理搜索
function handleSearch() {
  pagination.page = 1
  fetchTableData()
}

// 重置搜索
function resetSearch() {
  searchParams.title = ''
  searchParams.investigateCode = ''
  searchParams.status = ''
  pagination.page = 1
  fetchTableData()
}

// 处理页码变化
function handlePageChange(page: number) {
  pagination.page = page
  fetchTableData()
}

// 处理页大小变化
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchTableData()
}

// 确认选择
function handleConfirm() {
  if (!selectedRow.value) {
    ElMessage.warning('请选择一条记录')
    return
  }

  const value = selectedRow.value[props.valueKey]
  emit('update:modelValue', value)
  emit('change', value, selectedRow.value)
  dialogVisible.value = false
}

// 格式化函数
function formatStatus(status: string) {
  const statusMap: Record<string, { type: 'info' | 'success' | 'primary' | 'warning' | 'danger', label: string }> = {
    NO_START: { type: 'info', label: '未开始' },
    PROGRESSING: { type: 'success', label: '进行中' },
    FINISHED: { type: 'primary', label: '已完成' },
    PAUSED: { type: 'warning', label: '已暂停' },
    CANCELED: { type: 'danger', label: '已取消' },
  }
  return statusMap[status] || { type: 'info', label: '未知' }
}

function formatLevel(level: string) {
  const levelMap: Record<string, { type: 'info' | 'warning' | 'danger', label: string }> = {
    LOW: { type: 'info', label: '低' },
    MIDDLE: { type: 'warning', label: '中' },
    HIGH: { type: 'danger', label: '高' },
  }
  return levelMap[level] || { type: 'info', label: '未知' }
}

function formatInvestigateType(type: string) {
  const typeMap: Record<string, string> = {
    ADVERTISING_COMPLIANCE: '广告合规',
    SUPPLIER_MANAGEMENT: '供应商管理',
    EMPLOYEE_TRAINING: '员工培训',
    FINANCIAL_AUDITING: '财务审计',
  }
  return typeMap[type] || '未知'
}
</script>

<template>
  <div class="violation-investigation-selector">
    <!-- 输入框 -->
    <el-input
      :model-value="displayValue"
      :placeholder="placeholder"
      readonly
      @click="openDialog"
    >
      <template #suffix>
        <div class="flex items-center gap-1">
          <!-- 清除按钮 -->
          <el-icon
            v-if="displayValue"
            class="cursor-pointer text-gray-400 hover:text-gray-600"
            @click.stop="handleClear"
          >
            <Close />
          </el-icon>
          <!-- 搜索按钮 -->
          <el-icon class="cursor-pointer text-gray-400 hover:text-gray-600" @click="openDialog">
            <Search />
          </el-icon>
        </div>
      </template>
    </el-input>

    <!-- 选择弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择违规问题调查"
      width="80%"
      :before-close="handleClose"
    >
      <!-- 搜索区域 -->
      <div class="mb-4 rounded-lg bg-gray-50 p-4">
        <div class="grid grid-cols-3 gap-4">
          <div>
            <label class="mb-1 block text-sm text-gray-700 font-medium">调查标题</label>
            <el-input
              v-model="searchParams.title"
              placeholder="输入调查标题搜索..."
              clearable
            />
          </div>
          <div>
            <label class="mb-1 block text-sm text-gray-700 font-medium">调查编号</label>
            <el-input
              v-model="searchParams.investigateCode"
              placeholder="输入调查编号搜索..."
              clearable
            />
          </div>
          <div>
            <label class="mb-1 block text-sm text-gray-700 font-medium">调查状态</label>
            <el-select v-model="searchParams.status" placeholder="全部状态" class="w-full">
              <el-option label="全部状态" value="" />
              <el-option label="未开始" value="NO_START" />
              <el-option label="进行中" value="PROGRESSING" />
              <el-option label="已完成" value="FINISHED" />
              <el-option label="已暂停" value="PAUSED" />
              <el-option label="已取消" value="CANCELED" />
            </el-select>
          </div>
        </div>
        <div class="mt-4 flex justify-end">
          <el-button @click="resetSearch">
            重置
          </el-button>
          <el-button type="primary" @click="handleSearch">
            搜索
          </el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        highlight-current-row
        @current-change="handleCurrentChange"
        @row-click="handleRowClick"
      >
        <el-table-column prop="investigateCode" label="调查编号" width="120" />
        <el-table-column prop="title" label="调查标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="investigateType" label="调查类型" width="120">
          <template #default="scope">
            {{ formatInvestigateType(scope.row.investigateType) }}
          </template>
        </el-table-column>
        <el-table-column prop="level" label="优先级" width="100">
          <template #default="scope">
            <el-tag
              :type="formatLevel(scope.row.level).type"
              size="small"
            >
              {{ formatLevel(scope.row.level).label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="formatStatus(scope.row.status).type"
              size="small"
            >
              {{ formatStatus(scope.row.status).label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dutyEmployeeName" label="负责人" width="120" />
        <el-table-column prop="startDate" label="开始日期" width="120">
          <template #default="scope">
            {{ scope.row.startDate ? dayjs(scope.row.startDate).format('YYYY-MM-DD') : '-' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-center">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>

      <!-- 弹窗底部按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">
            取消
          </el-button>
          <el-button type="primary" :disabled="!selectedRow" @click="handleConfirm">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.violation-investigation-selector {
  width: 100%;
}

.el-input {
  cursor: pointer;
}

.el-input :deep(.el-input__inner) {
  cursor: pointer;
}

/* 确保图标按钮正确显示 */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.gap-1 {
  gap: 0.25rem;
}

.text-gray-400 {
  color: #9ca3af;
}

.hover\:text-gray-600:hover {
  color: #4b5563;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
