<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ChatRound as ElIconChatRound,
  Check as <PERSON><PERSON><PERSON><PERSON>he<PERSON>,
  Close as ElIconClose,
  Delete as ElIconDelete,
  Document as ElIconDocument,
  Download as ElIconDownload,
  Plus as ElIconPlus,
  QuestionFilled as ElIconQuestionFilled,
  Upload as ElIconUpload,
  UploadFilled as ElIconUploadFilled,
} from '@element-plus/icons-vue'
import threeListApi from '@/api/complianceApi/prevention/threeList'
import threeAiApi from '@/api/complianceApi/prevention/threeAi'
import useUserStore from '@/store/modules/user'
import UploadMbb from '@/components/uploadMbb/index.vue'
import FileUpload from '@/components/FileUpload/index.vue'
import DocumentUpload from '@/components/DocumentUpload/index.vue'

const action = `${(import.meta.env.DEV && import.meta.env.VITE_OPEN_PROXY === 'true') ? '/proxy/' : import.meta.env.VITE_APP_API_BASEURL}whiskerguardgeneralservice/api/file/upload?serviceName=whiskerguardgeneralservice`
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const processId = ref<string | number>('')
const isEditMode = ref(false)
const lawyer = ref(false)
// Form Data
const form = ref({
  businessDomainName: '',
  businessDomainType: '',
  businessProcess: '',
  processDescription: '',
  processDiagramUrl: '',
  processFiles: [] as Array<{
    fileName: string
    fileUrl: string
    fileType: string
    fileSize: number
  }>,
  controlLinks: [{
    controlLinkName: '',
    linkDescription: '',
    approvalDepartment: '',
    approver: '',
    requiresGeneralManager: false,
    requiresChairman: false,
    controlDetail: {
      riskPoints: '',
      riskDescriptions: '',
      complianceRequirements: '',
      complianceBasis: '',
      controlMeasures: '',
      responsibleDepartments: [] as Array<{ departmentId: string | number, departmentName: string }>,
      responsiblePositions: [] as Array<{ positionId: string | number, positionName: string }>,
    },
  }] as Array<{
    controlLinkName: string
    linkDescription: string
    approvalDepartment: string
    approver: string
    requiresGeneralManager: boolean
    requiresChairman: boolean
    controlDetail: {
      riskPoints: string
      riskDescriptions: string
      complianceRequirements: string
      complianceBasis: string
      controlMeasures: string
      responsibleDepartments: Array<{ departmentId: string | number, departmentName: string }>
      responsiblePositions: Array<{ positionId: string | number, positionName: string }>
    }
  }>,
  approvalStatus: 'DRAFT',
  processStatus: 'ACTIVE',
})
// UI State
const sections = ref({
  businessProcess: true,
  controlLinks: true,
})
const showUploadDialog = ref(false)
const showRegenerateDialog = ref(false)
const showAnalyzeProgress = ref(false)
// 文档上传相关状态已移至uploadMbb组件
const isAnalyzing = ref(false)
const analyzeComplete = ref(false)
const analyzeProgress = ref(0)
const analyzeStatus = ref<'success' | 'warning' | 'exception' | ''>('')
const currentStep = ref(0)
// AI生成状态控制
const isGenerating = ref(false)
// 进度提示
const currentProgress = ref('')
// 会话ID
const conversationId = ref('')
const regenerateFieldName = ref('')
const regenerateOptions = ref<string[]>([])
const selectedRegenerateOption = ref('')
const regenerateTarget = ref<any>(null)
const regenerateFieldKey = ref('')

// 部门和岗位选择器相关状态
const showDepartmentSelector = ref(false)
const showPositionSelector = ref(false)
const currentControlIndex = ref(0)

const tempSelectedDepartments = ref<any[]>([])
const tempSelectedPositions = ref<any[]>([])

// 流程步骤选择弹窗相关状态
const showProcessStepsDialog = ref(false)
const processStepsList = ref<any[]>([])
const selectedProcessStep = ref<number>(-1)
const selectedProcessStepData = ref<any>(null)
const processStepsLoading = ref(false)

// Data

const _quickQuestions = ref([
  '如何描述这个业务流程？',
  '这个环节有哪些风险点？',
  '合规要求有哪些？',
  '建议的管控措施是什么？',
])
// Computed
const departments = computed(() => userStore.getDepartments() || [])
const positions = computed(() => userStore.getPostList() || [])

// 解析AI返回的责任部门/岗位数据
function parseResponsibleData(data: any) {
  if (!data) {
    return []
  }

  // 如果已经是数组，直接返回
  if (Array.isArray(data)) {
    return data.map(item => ({
      ...item,
      isExternal: !item.departmentId && !item.positionId, // 标记外部数据（无ID）
    }))
  }

  // 如果是字符串，尝试解析JSON
  if (typeof data === 'string') {
    try {
      const parsed = JSON.parse(data)
      if (Array.isArray(parsed)) {
        return parsed.map(item => ({
          ...item,
          isExternal: !item.departmentId && !item.positionId, // 标记外部数据（无ID）
        }))
      }
    }
    catch (e) {
      console.warn('解析责任数据失败:', e)
    }
  }

  return []
}

// Methods
function toggleSection(section: string) {
  sections.value[section as keyof typeof sections.value] = !sections.value[section as keyof typeof sections.value]
}
function addControlLink() {
  form.value.controlLinks.push({
    controlLinkName: '',
    linkDescription: '',
    approvalDepartment: '',
    approver: '',
    requiresGeneralManager: false,
    requiresChairman: false,
    controlDetail: {
      riskPoints: '',
      riskDescriptions: '',
      complianceRequirements: '',
      complianceBasis: '',
      controlMeasures: '',
      responsibleDepartments: [],
      responsiblePositions: [],
    },
  })
}
function removeControlLink(index: number) {
  form.value.controlLinks.splice(index, 1)
}

// 部门选择器相关方法
function openDepartmentSelector(controlIndex: number) {
  currentControlIndex.value = controlIndex
  tempSelectedDepartments.value = [...form.value.controlLinks[controlIndex].controlDetail.responsibleDepartments]
  showDepartmentSelector.value = true
}

function closeDepartmentSelector() {
  showDepartmentSelector.value = false
  tempSelectedDepartments.value = []
}

function toggleDepartmentSelection(department: any) {
  const index = tempSelectedDepartments.value.findIndex(d => d.departmentId === department.id)
  if (index > -1) {
    tempSelectedDepartments.value.splice(index, 1)
  }
  else {
    tempSelectedDepartments.value.push({
      departmentId: department.id,
      departmentName: department.departmentName || department.name,
    })
  }
}

function isDepartmentSelected(department: any) {
  return tempSelectedDepartments.value.some(d => d.departmentId === department.id)
}

function confirmDepartmentSelection() {
  form.value.controlLinks[currentControlIndex.value].controlDetail.responsibleDepartments = [...tempSelectedDepartments.value]
  closeDepartmentSelector()
}

function removeDepartment(controlIndex: number, deptIndex: number) {
  form.value.controlLinks[controlIndex].controlDetail.responsibleDepartments.splice(deptIndex, 1)
}

// 岗位选择器相关方法
function openPositionSelector(controlIndex: number) {
  currentControlIndex.value = controlIndex
  tempSelectedPositions.value = [...form.value.controlLinks[controlIndex].controlDetail.responsiblePositions]
  showPositionSelector.value = true
}

function closePositionSelector() {
  showPositionSelector.value = false
  tempSelectedPositions.value = []
}

function togglePositionSelection(position: any) {
  const index = tempSelectedPositions.value.findIndex(p => p.positionId === position.id)
  if (index > -1) {
    tempSelectedPositions.value.splice(index, 1)
  }
  else {
    tempSelectedPositions.value.push({
      positionId: position.id,
      positionName: position.positionName || position.name,
    })
  }
}

function isPositionSelected(position: any) {
  return tempSelectedPositions.value.some(p => p.positionId === position.id)
}

function confirmPositionSelection() {
  form.value.controlLinks[currentControlIndex.value].controlDetail.responsiblePositions = [...tempSelectedPositions.value]
  closePositionSelector()
}

function removePosition(controlIndex: number, posIndex: number) {
  form.value.controlLinks[controlIndex].controlDetail.responsiblePositions.splice(posIndex, 1)
}

// 流程步骤选择弹窗相关方法
function closeProcessStepsDialog() {
  showProcessStepsDialog.value = false
  selectedProcessStep.value = -1
  selectedProcessStepData.value = null
  processStepsList.value = []
}

function selectProcessStep(step: any) {
  selectedProcessStepData.value = step
}

function confirmProcessStepSelection() {
  if (selectedProcessStep.value !== -1 && selectedProcessStepData.value) {
    // 这里可以根据需要处理选中的流程步骤
    ElMessage.success(`已选择流程步骤: ${selectedProcessStepData.value.stepName || selectedProcessStepData.value.name || '未知步骤'}`)
    form.value.businessProcess = selectedProcessStepData.value.processDescription
    form.value.businessDomainName = selectedProcessStepData.value.businessDomain
    // 可以在这里将选中的步骤数据应用到表单中
    // 例如：form.value.businessProcess = selectedProcessStepData.value.description
  }
  else {
    ElMessage.warning('请选择一个流程步骤')
    return
  }

  closeProcessStepsDialog()
}

// API Methods
async function getConversationId() {
  try {
    const response = await threeAiApi.getSessionId()
    if (response) {
      conversationId.value = response.conversationId
    }
  }
  catch (error) {
    console.error('获取会话ID失败:', error)
    ElMessage.error('获取会话ID失败')
  }
}

async function fetchProcessDetail() {
  if (!processId.value) {
    return
  }

  try {
    const response = await threeListApi.getProcessDetail(Number(processId.value))
    if (response) {
      const data = response
      form.value = {
        businessDomainName: data.businessDomainName || '',
        businessDomainType: '',
        businessProcess: data.businessProcess || '',
        processDescription: data.processDescription || '',
        processDiagramUrl: data.processDiagramUrl,
        processFiles: data.processFiles || [],
        controlLinks: data.controlLinks && data.controlLinks.length > 0
          ? data.controlLinks.map((link: any) => ({
            ...link,
            controlDetail: {
              ...link.controlDetail,
              responsibleDepartments: parseResponsibleData(link.controlDetail?.responsibleDepartments) || [],
              responsiblePositions: parseResponsibleData(link.controlDetail?.responsiblePositions) || [],
            },
          }))
          : [{
              controlLinkName: '',
              linkDescription: '',
              approvalDepartment: '',
              approver: '',
              requiresGeneralManager: false,
              requiresChairman: false,
              controlDetail: {
                riskPoints: '',
                riskDescriptions: '',
                complianceRequirements: '',
                complianceBasis: '',
                controlMeasures: '',
                responsibleDepartments: [],
                responsiblePositions: [],
              },
            }],
        approvalStatus: data.approvalStatus || 'DRAFT',
        processStatus: data.processStatus || 'ACTIVE',
      }
    }
  }
  catch (error) {
    console.error('获取流程详情失败:', error)
    ElMessage.error('获取流程详情失败')
  }
}

async function autoProcess() {
  if (processStepsLoading.value || isGenerating.value) {
    ElMessage.warning('正在生成中，请稍候...')
    return
  }

  processStepsLoading.value = true
  isGenerating.value = true
  try { // 调用自动生成业务流程接口
    const response = await threeListApi.autoProcess()

    if (response && response.success) {
      // 如果有流程步骤数据，显示选择弹窗
      if (response.processSteps && response.processSteps.length > 0) {
        processStepsList.value = response.processSteps
        selectedProcessStep.value = null
        showProcessStepsDialog.value = true
      }
      else {
        ElMessage.info('未获取到流程步骤数据')
      }

      // 显示其他信息
      if (response.regulationCount !== undefined) {
        ElMessage.info(`查询到 ${response.regulationCount} 个企业内部制度`)
      }
    }
    else {
      ElMessage.error(response?.message || '自动生成流程失败')
    }
  }
  catch (error: any) {
    console.error('自动生成流程失败:', error)
    ElMessage.error(error?.message || '自动生成流程失败')
  }
  finally {
    processStepsLoading.value = false
    isGenerating.value = false
  }
}
async function saveDraft() {
  try {
    // 构建提交数据，添加 bizProcessMainV2Id 参数
    const submitData = {
      ...form.value,
      orgUnitId: null,
      bizProcessMainV2Id: processId.value ? Number(processId.value) : null,
      controlLinks: form.value.controlLinks.map(link => ({
        ...link,
        controlDetail: {
          ...link.controlDetail,
          responsibleDepartments: JSON.stringify(Array.isArray(link.controlDetail.responsibleDepartments)
            ? link.controlDetail.responsibleDepartments.map(dept => ({
              departmentName: dept.departmentName || '',
              departmentId: dept.departmentId || '',
            }))
            : []),
          responsiblePositions: JSON.stringify(Array.isArray(link.controlDetail.responsiblePositions)
            ? link.controlDetail.responsiblePositions.map(pos => ({
              positionName: pos.positionName || '',
              positionId: pos.positionId || '',
            }))
            : []),
        },
      })),
    }

    let response
    if (lawyer.value) {
      // 律师模式：直接调用更新接口并设置为已通过状态
      submitData.approvalStatus = 'APPROVED'
      response = await threeListApi.updateProcess(processId.value, submitData)
      ElMessage.success('发布成功')
    }
    else {
      // 普通用户模式：根据是否有ID判断调用哪个接口
      if (processId.value) {
        // 编辑模式：调用更新接口，状态为草稿
        submitData.approvalStatus = 'DRAFT'
        response = await threeListApi.updateProcess(processId.value, submitData)
      }
      else {
        // 新增模式：调用草稿接口
        response = await threeListApi.draftProcess(submitData)
      }
      ElMessage.success('保存成功')
    }

    if (response) {
      if (!isEditMode.value && response) {
        processId.value = response.bizProcessMainV2Id
        isEditMode.value = true
        // 更新URL参数
        router.replace({
          name: route.name as string,
          params: { ...route.params, id: response.bizProcessMainV2Id },
          query: route.query,
        })
      }
    }
    else {
      ElMessage.error(response.message || '保存失败')
    }
  }
  catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}
async function submitProcess() {
  try {
    // 律师模式下不允许提交，只能保存
    if (lawyer.value) {
      ElMessage.warning('律师模式下请使用保存功能')
      return
    }

    // 先保存草稿
    // if (!isEditMode.value) {
    //   await saveDraft()
    // }

    // 构建提交数据，添加 bizProcessMainV2Id 参数
    const submitData = {
      ...form.value,
      orgUnitId: null,
      bizProcessMainV2Id: processId.value ? Number(processId.value) : null,
      approvalStatus: 'PENDING',
      controlLinks: form.value.controlLinks.map(link => ({
        ...link,
        controlDetail: {
          ...link.controlDetail,
          responsibleDepartments: JSON.stringify(Array.isArray(link.controlDetail.responsibleDepartments)
            ? link.controlDetail.responsibleDepartments.map(dept => ({
              departmentName: dept.departmentName || '',
              departmentId: dept.departmentId || '',
            }))
            : []),
          responsiblePositions: JSON.stringify(Array.isArray(link.controlDetail.responsiblePositions)
            ? link.controlDetail.responsiblePositions.map(pos => ({
              positionName: pos.positionName || '',
              positionId: pos.positionId || '',
            }))
            : []),
        },
      })),
    }

    const response = await threeListApi.submitProcess(submitData)
    if (response) {
      ElMessage.success('提交成功')
      router.back()
    }
    else {
      ElMessage.error(response.message || '提交失败')
    }
  }
  catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}
function handleCancel() {
  router.back()
}

function handleFlowChartChange(file: any) {
  form.value.processDiagramUrl = URL.createObjectURL(file.raw)
  showUploadDialog.value = false
}
function confirmUpload() {
  showUploadDialog.value = false
}
// 流程图上传成功处理
function handleFlowChartUploadSuccess(fileKey: string) {
  if (fileKey) {
    form.value.processDiagramUrl = fileKey
    ElMessage.success('流程图上传成功')
  }
}
// 文档上传成功处理
function handleDocumentUploadSuccess(files: any[]) {
  // uploadMbb组件已经将文件数据绑定到v-model，这里可以做额外处理
  ElMessage.success(`成功上传 ${files.length} 个文件`)
}

function removeFlowChart() {
  form.value.processDiagramUrl = ''
}
async function handleAnalyze() {
  if (isAnalyzing.value || isGenerating.value) {
    ElMessage.warning('正在识别中，请稍候...')
    return
  }

  // 检查必要参数
  if (!form.value.businessDomainName) {
    ElMessage.warning('请先填写业务领域名称')
    return
  }

  isAnalyzing.value = true
  isGenerating.value = true
  analyzeComplete.value = false
  showAnalyzeProgress.value = true
  analyzeProgress.value = 0
  currentStep.value = 0
  analyzeStatus.value = ''
  currentProgress.value = '正在分析业务流程...'

  try {
    // 调用AI接口生成业务流程数据
    const response = await threeListApi.getProcessAI({
      businessDomain: form.value.businessDomainName,
      processDiagramUrl: form.value.processDiagramUrl,
      conversationId: conversationId.value,
    })

    if (response) {
      const processData = response.processData

      // 更新会话ID
      if (processData.conversationId) {
        conversationId.value = processData.conversationId
      }

      // 填充生成的数据
      if (processData.businessDomainType) {
        form.value.businessDomainName = processData.businessDomainType
      }
      if (processData.businessProcess) {
        form.value.businessProcess = processData.businessProcess
      }
      if (processData.processDescription) {
        form.value.processDescription = processData.processDescription
      }

      // 填充控制环节数据
      if (processData.controlLinks && processData.controlLinks.length > 0) {
        form.value.controlLinks = processData.controlLinks.map((link: any) => ({
          controlLinkName: link.controlLinkName || '',
          linkDescription: link.linkDescription || '',
          approvalDepartment: link.approvalDepartment || '',
          approver: link.approver || '',
          requiresGeneralManager: link.requiresGeneralManager || false,
          requiresChairman: link.requiresChairman || false,
          controlDetail: {
            riskPoints: link.controlDetail?.riskPoints || '',
            riskDescriptions: link.controlDetail?.riskDescriptions || '',
            complianceRequirements: link.controlDetail?.complianceRequirements || '',
            complianceBasis: link.controlDetail?.complianceBasis || '',
            controlMeasures: link.controlDetail?.controlMeasures || '',
            responsibleDepartments: parseResponsibleData(link.controlDetail?.responsibleDepartments) || [],
            responsiblePositions: parseResponsibleData(link.controlDetail?.responsiblePositions) || [],
          },
        }))
      }

      // 处理流程文件
      if (processData.processFiles && processData.processFiles.length > 0) {
        form.value.processFiles = processData.processFiles
      }

      analyzeProgress.value = 100
      analyzeComplete.value = true
      analyzeStatus.value = 'success'
      currentProgress.value = '业务流程智能识别完成'

      setTimeout(() => {
        showAnalyzeProgress.value = false
        ElMessage.success('业务流程智能识别完成')
      }, 1000)
    }
    else {
      throw new Error(response.message || '智能识别失败')
    }
  }
  catch (error: any) {
    console.error('AI生成失败:', error)
    analyzeStatus.value = 'exception'
    currentProgress.value = '智能识别失败'
    ElMessage.error(error.message || '智能识别失败，请重试')

    setTimeout(() => {
      showAnalyzeProgress.value = false
    }, 2000)
  }
  finally {
    isAnalyzing.value = false
    isGenerating.value = false
  }
}
function cancelAnalyze() {
  isAnalyzing.value = false
  isGenerating.value = false
  showAnalyzeProgress.value = false
  ElMessage.info('已取消业务流程智能识别')
}

function regenerateField(target: any, field: string) {
  regenerateTarget.value = target
  regenerateFieldKey.value = field
  regenerateFieldName.value = getFieldName(field)
  // Generate sample options
  regenerateOptions.value = [
`这是关于${regenerateFieldName.value}的第一个生成选项`,
`这是关于${regenerateFieldName.value}的第二个生成选项，包含更多细节`,
`这是关于${regenerateFieldName.value}的第三个生成选项，考虑了最新法规要求`,
  ]
  showRegenerateDialog.value = true
}
function getFieldName(field: string) {
  const map: Record<string, string> = {
    riskPoints: '风险点',
    riskDescriptions: '风险描述',
    complianceRequirements: '合规要求',
    complianceBasis: '合规依据',
    controlMeasures: '管控措施',
    responsibleDepartments: '责任部门',
    responsiblePositions: '责任岗位',
  }
  return map[field] || field
}
function selectRegenerateOption(option: string) {
  selectedRegenerateOption.value = option
}
function confirmRegenerate() {
  if (selectedRegenerateOption.value && regenerateTarget.value && regenerateFieldKey.value) {
    regenerateTarget.value[regenerateFieldKey.value] = selectedRegenerateOption.value
    ElMessage.success(`${regenerateFieldName.value}已更新`)
  }
  showRegenerateDialog.value = false
  selectedRegenerateOption.value = ''
}
// Lifecycle
onMounted(async () => {
  // 初始化lawyer变量
  lawyer.value = userStore.lawyer

  // 获取会话ID
  await getConversationId()

  // 获取路由参数
  const id = route.params.id || route.query.id
  if (id) {
    processId.value = id as string
    isEditMode.value = true
    fetchProcessDetail()
  }
})
</script>

<template>
  <div class="w-full">
    <!-- Header -->
    <header class="mx-auto bg-white px-6 py-4">
      <div class="flex items-center justify-between">
        <h1 class="text-xl text-gray-800 font-semibold">
          关键业务流程管控清单编辑
        </h1>
        <el-button
          v-debounce="3000"
          type="primary"
          class="!rounded-button flex items-center whitespace-nowrap bg-blue-500"
          :loading="isAnalyzing"
          @click="handleAnalyze"
        >
          <template v-if="!isAnalyzing">
            业务流程智能识别
            <el-icon v-if="analyzeComplete" class="ml-1">
              <ElIconCheck />
            </el-icon>
          </template>
          <span v-else>识别中...</span>
        </el-button>
        <div class="flex space-x-2">
          <!-- 律师模式：只显示发布按钮 -->
          <template v-if="lawyer && form.approvalStatus === 'PENDING'">
            <el-button v-debounce="3000" type="primary" class="!rounded-button whitespace-nowrap" @click="saveDraft">
              发布
            </el-button>
          </template>
          <!-- 普通用户模式：显示保存草稿和提交审核按钮 -->
          <template v-else-if="!lawyer && (form.approvalStatus === 'DRAFT' || !form.approvalStatus)">
            <el-button v-debounce="3000" type="primary" class="!rounded-button whitespace-nowrap" :loading="processStepsLoading || isGenerating" @click="autoProcess">
              <template v-if="!processStepsLoading && !isGenerating">
                自动生成业务流程
              </template>
              <span v-else>生成中...</span>
            </el-button>
            <el-button v-debounce="3000" class="!rounded-button whitespace-nowrap" @click="saveDraft">
              保存草稿
            </el-button>
            <el-button v-debounce="3000" type="success" class="!rounded-button whitespace-nowrap bg-green-600" @click="submitProcess">
              提交审核
            </el-button>
          </template>
          <el-button v-debounce="3000" class="!rounded-button whitespace-nowrap" @click="handleCancel">
            取消
          </el-button>
        </div>
      </div>
    </header>

    <!-- 主体内容区 -->
    <div class="mx-auto px-6 pb-10 pt-6 container">
      <!-- 业务流程 -->
      <div class="mb-6 rounded-lg bg-white shadow-sm">
        <div class="item-title flex cursor-pointer items-center justify-between px-6 py-1" @click="toggleSection('businessProcess')">
          <h2 class="text-base text-gray-800 font-bold">
            业务流程
          </h2>
          <el-button text>
            {{ sections.businessProcess ? '收起' : '展开' }}
          </el-button>
        </div>
        <div v-show="sections.businessProcess" class="p-6">
          <!-- 上传流程图区域 -->
          <div class="mb-6">
            <label class="mb-2 block text-sm text-gray-700 font-medium">
              流程图
            </label>
            <DocumentUpload
              v-model="form.processDiagramUrl"
              :action="action"
              :ext="['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']"
              placeholder="上传流程图，支持图片格式"
              @on-success="handleFlowChartUploadSuccess"
              @on-remove="removeFlowChart"
            />
          </div>

          <div>
            <div class="mb-4">
              <label class="mb-1 block text-sm text-gray-700 font-medium">
                业务领域名称
                <span class="text-red-500">*</span>
              </label>
              <el-input
                v-model="form.businessDomainName"
                placeholder="请输入业务领域名称"
                class="w-full"
              />
            </div>
          </div>

          <div class="mb-4">
            <label class="mb-1 block text-sm text-gray-700 font-medium">
              流程描述
            </label>
            <el-input
              v-model="form.businessProcess"
              type="textarea"
              :rows="3"
              placeholder="请描述业务流程的主要内容和步骤"
              class="w-full"
            />
          </div>
        </div>
      </div>
      <!-- Control Links Section -->
      <div class="mb-6 overflow-hidden rounded-lg bg-white pb-3 shadow-sm">
        <div class="item-title flex cursor-pointer items-center justify-between border-b border-gray-200 px-6 py-1">
          <div class="w-full flex items-center justify-between">
            <h2 class="text-base text-gray-800 font-bold">
              管控环节
            </h2>
            <div>
              <el-button
                text
                class="mr-2"
                @click="toggleSection('controlLinks')"
              >
                {{ sections.controlLinks ? '收起' : '展开' }}
              </el-button>

              <el-button
                type="primary"
                class="!rounded-button whitespace-nowrap bg-blue-500"
                @click="addControlLink"
              >
                <el-icon class="mr-1">
                  <ElIconPlus />
                </el-icon>
                添加环节
              </el-button>
            </div>
          </div>
        </div>
        <div v-show="sections.controlLinks">
          <div class="space-y-4">
            <div
              v-for="(link, index) in form.controlLinks"
              :key="index"
              class="border rounded-lg bg-white px-6 py-4"
            >
              <div class="mb-4 flex items-center justify-between">
                <div class="text-gray-800 font-bold">
                  管控环节 {{ index + 1 }}
                </div>
                <el-button
                  type="danger"
                  text
                  size="small"
                  @click="removeControlLink(index)"
                >
                  <el-icon><ElIconDelete /></el-icon>
                </el-button>
              </div>
              <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label class="mb-1 block text-sm text-gray-700 font-medium">管控环节名称</label>
                  <el-input
                    v-model="link.controlLinkName"
                    placeholder="请输入管控环节名称"
                    class="w-full"
                  />
                </div>
                <div>
                  <label class="mb-1 block text-sm text-gray-700 font-medium">环节描述</label>
                  <el-input
                    v-model="link.linkDescription"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入环节描述"
                    class="w-full"
                  />
                </div>
                <div>
                  <label class="mb-1 block text-sm text-gray-700 font-medium">审批部门</label>
                  <el-input
                    v-model="link.approvalDepartment"
                    placeholder="请输入审批部门"
                    class="w-full"
                  />
                </div>
                <div>
                  <label class="mb-1 block text-sm text-gray-700 font-medium">审批人</label>
                  <el-input
                    v-model="link.approver"
                    placeholder="请输入审批人"
                    class="w-full"
                  />
                </div>
                <div class="flex items-center space-x-4">
                  <el-checkbox v-model="link.requiresGeneralManager">
                    需要总经理审批
                  </el-checkbox>
                  <el-checkbox v-model="link.requiresChairman">
                    需要董事长审批
                  </el-checkbox>
                </div>
              </div>
              <div class="grid grid-cols-1 mt-4 gap-4 md:grid-cols-2">
                <div>
                  <label class="mb-1 block flex items-center justify-between text-sm text-gray-700 font-medium">
                    <span>风险点</span>
                    <el-button
                      v-if="link.controlDetail.riskPoints"
                      v-debounce="3000"
                      type="text"
                      size="small"
                      :disabled="!analyzeComplete"
                      @click="regenerateField(link.controlDetail, 'riskPoints')"
                    >
                      重新生成
                    </el-button>
                  </label>
                  <el-input
                    v-model="link.controlDetail.riskPoints"
                    type="textarea"
                    :rows="3"
                    placeholder="系统将根据业务流程信息智能生成"
                    readonly
                    :class="{ 'bg-green-50': link.controlDetail.riskPoints }"
                    class="w-full"
                  />
                </div>
                <div>
                  <label class="mb-1 block flex items-center justify-between text-sm text-gray-700 font-medium">
                    <span>风险描述</span>
                    <el-button
                      v-if="link.controlDetail.riskDescriptions"
                      v-debounce="3000"
                      type="text"
                      size="small"
                      :disabled="!analyzeComplete"
                      @click="regenerateField(link.controlDetail, 'riskDescriptions')"
                    >
                      重新生成
                    </el-button>
                  </label>
                  <el-input
                    v-model="link.controlDetail.riskDescriptions"
                    type="textarea"
                    :rows="3"
                    placeholder="系统将根据业务流程信息智能生成"
                    readonly
                    :class="{ 'bg-green-50': link.controlDetail.riskDescriptions }"
                    class="w-full"
                  />
                </div>
                <div>
                  <label class="mb-1 block flex items-center justify-between text-sm text-gray-700 font-medium">
                    <span>合规要求</span>
                    <el-button
                      v-if="link.controlDetail.complianceRequirements"
                      v-debounce="3000"
                      type="text"
                      size="small"
                      :disabled="!analyzeComplete"
                      @click="regenerateField(link.controlDetail, 'complianceRequirements')"
                    >
                      重新生成
                    </el-button>
                  </label>
                  <el-input
                    v-model="link.controlDetail.complianceRequirements"
                    type="textarea"
                    :rows="3"
                    placeholder="系统将根据业务流程信息智能生成"
                    readonly
                    :class="{ 'bg-green-50': link.controlDetail.complianceRequirements }"
                    class="w-full"
                  />
                </div>
                <div>
                  <label class="mb-1 block flex items-center justify-between text-sm text-gray-700 font-medium">
                    <span>合规依据</span>
                    <el-button
                      v-if="link.controlDetail.complianceBasis"
                      v-debounce="3000"
                      type="text"
                      size="small"
                      :disabled="!analyzeComplete"
                      @click="regenerateField(link.controlDetail, 'complianceBasis')"
                    >
                      重新生成
                    </el-button>
                  </label>
                  <el-input
                    v-model="link.controlDetail.complianceBasis"
                    type="textarea"
                    :rows="3"
                    placeholder="系统将根据业务流程信息智能生成"
                    readonly
                    :class="{ 'bg-green-50': link.controlDetail.complianceBasis }"
                    class="w-full"
                  />
                </div>
                <div>
                  <label class="mb-1 block flex items-center justify-between text-sm text-gray-700 font-medium">
                    <span>管控措施</span>
                    <el-button
                      v-if="link.controlDetail.controlMeasures"
                      v-debounce="3000"
                      type="text"
                      size="small"
                      :disabled="!analyzeComplete"
                      @click="regenerateField(link.controlDetail, 'controlMeasures')"
                    >
                      重新生成
                    </el-button>
                  </label>
                  <el-input
                    v-model="link.controlDetail.controlMeasures"
                    type="textarea"
                    :rows="3"
                    placeholder="系统将根据业务流程信息智能生成"
                    readonly
                    :class="{ 'bg-green-50': link.controlDetail.controlMeasures }"
                    class="w-full"
                  />
                </div>
                <div>
                  <label class="mb-1 block text-sm text-gray-700 font-medium">
                    责任部门
                  </label>
                  <div class="space-y-2">
                    <div class="flex flex-wrap gap-2">
                      <el-tag
                        v-for="(dept, deptIndex) in link.controlDetail.responsibleDepartments"
                        :key="dept.departmentId || deptIndex"
                        :type="!dept.departmentId || dept.departmentId === '' ? 'warning' : 'info'"
                        closable
                        @close="removeDepartment(index, deptIndex)"
                      >
                        {{ dept.departmentName }}
                        <span v-if="!dept.departmentId || dept.departmentId === ''" class="ml-1 text-xs">(待选择)</span>
                      </el-tag>
                      <el-button
                        v-debounce="3000"
                        type="primary"
                        size="small"
                        plain
                        @click="openDepartmentSelector(index)"
                      >
                        <el-icon><ElIconPlus /></el-icon>
                        选择部门
                      </el-button>
                    </div>
                  </div>
                </div>
                <div>
                  <label class="mb-1 block text-sm text-gray-700 font-medium">
                    责任岗位
                  </label>
                  <div class="space-y-2">
                    <div class="flex flex-wrap gap-2">
                      <el-tag
                        v-for="(pos, posIndex) in link.controlDetail.responsiblePositions"
                        :key="pos.positionId || posIndex"
                        :type="!pos.positionId || pos.positionId === '' ? 'warning' : 'info'"
                        closable
                        @close="removePosition(index, posIndex)"
                      >
                        {{ pos.positionName }}
                        <span v-if="!pos.positionId || pos.positionId === ''" class="ml-1 text-xs">(待选择)</span>
                      </el-tag>
                      <el-button
                        v-debounce="3000"
                        type="primary"
                        size="small"
                        plain
                        @click="openPositionSelector(index)"
                      >
                        <el-icon><ElIconPlus /></el-icon>
                        选择岗位
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Work Documents Section -->
      <div class="mb-6 overflow-hidden rounded-lg bg-white shadow-sm">
        <div class="item-title flex cursor-pointer items-center justify-between border-b border-gray-200 px-6 py-1">
          <div class="w-full flex items-center justify-between">
            <h2 class="text-base text-gray-800 font-bold">
              工作文档
            </h2>
          </div>
        </div>
        <div class="p-6">
          <UploadMbb
            v-model="form.processFiles"
            :auto-upload="true"
            :max="10"
            service-name="whiskerguardregulatoryservice"
            category-name="process"
            tip-text="支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 10MB"
            @upload-success="handleDocumentUploadSuccess"
          />
        </div>
      </div>
      <!-- 文档上传和预览功能已集成到UploadMbb组件中 -->
      <!-- Upload Dialog -->
      <el-dialog
        v-model="showUploadDialog"
        title="上传流程图"
        width="500px"
      >
        <el-upload
          class="upload-demo"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFlowChartChange"
          :show-file-list="false"
        >
          <el-icon class="el-icon--upload">
            <ElIconUploadFilled />
          </el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 JPG/PNG 格式文件，大小不超过 5MB
            </div>
          </template>
        </el-upload>
        <template #footer>
          <el-button v-debounce="3000" type="primary" @click="confirmUpload">
            确认
          </el-button>
        </template>
      </el-dialog>

      <!-- Regenerate Dialog -->
      <el-dialog
        v-model="showRegenerateDialog"
        :title="`重新生成 ${regenerateFieldName}`"
        width="600px"
      >
        <div class="space-y-4">
          <div
            v-for="(option, index) in regenerateOptions"
            :key="index"
            class="cursor-pointer border rounded p-4 hover:bg-gray-50"
            @click="selectRegenerateOption(option)"
          >
            <div class="mb-2 font-medium">
              选项 {{ index + 1 }}
            </div>
            <div class="text-gray-600">
              {{ option }}
            </div>
          </div>
        </div>
        <template #footer>
          <el-button v-debounce="3000" @click="showRegenerateDialog = false">
            取消
          </el-button>
          <el-button v-debounce="3000" type="primary" @click="confirmRegenerate">
            确认
          </el-button>
        </template>
      </el-dialog>
      <!-- Analyze Progress -->
      <el-dialog
        v-model="showAnalyzeProgress"
        title="业务流程智能识别中..."
        width="500px"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <div class="space-y-4">
          <el-progress :percentage="analyzeProgress" :status="analyzeStatus" />
          <div class="text-center text-gray-600">
            {{ currentProgress }}
          </div>
          <div v-if="isAnalyzing" class="flex justify-center">
            <el-button v-debounce="3000" @click="cancelAnalyze">
              取消
            </el-button>
          </div>
        </div>
      </el-dialog>

      <!-- Department Selector Dialog -->
      <el-dialog
        v-model="showDepartmentSelector"
        title="选择责任部门"
        width="600px"
        @close="closeDepartmentSelector"
      >
        <div class="space-y-4">
          <div class="max-h-96 overflow-y-auto">
            <div class="grid grid-cols-1 gap-2">
              <div
                v-for="department in departments"
                :key="department.id"
                class="flex cursor-pointer items-center justify-between border rounded p-3 hover:bg-gray-50"
                :class="{ 'bg-blue-50 border-blue-300': isDepartmentSelected(department) }"
                @click="toggleDepartmentSelection(department)"
              >
                <div class="flex items-center space-x-3">
                  <el-checkbox
                    :model-value="isDepartmentSelected(department)"
                    @change="toggleDepartmentSelection(department)"
                  />
                  <span class="font-medium">{{ department.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <template #footer>
          <div class="flex justify-end space-x-2">
            <el-button v-debounce="3000" @click="closeDepartmentSelector">
              取消
            </el-button>
            <el-button v-debounce="3000" type="primary" @click="confirmDepartmentSelection">
              确认选择
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- Position Selector Dialog -->
      <el-dialog
        v-model="showPositionSelector"
        title="选择责任岗位"
        width="600px"
        @close="closePositionSelector"
      >
        <div class="space-y-4">
          <div class="max-h-96 overflow-y-auto">
            <div class="grid grid-cols-1 gap-2">
              <div
                v-for="position in positions"
                :key="position.id"
                class="flex cursor-pointer items-center justify-between border rounded p-3 hover:bg-gray-50"
                :class="{ 'bg-blue-50 border-blue-300': isPositionSelected(position) }"
                @click="togglePositionSelection(position)"
              >
                <div class="flex items-center space-x-3">
                  <el-checkbox
                    :model-value="isPositionSelected(position)"
                    @change="togglePositionSelection(position)"
                  />
                  <span class="font-medium">{{ position.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <template #footer>
          <div class="flex justify-end space-x-2">
            <el-button v-debounce="3000" @click="closePositionSelector">
              取消
            </el-button>
            <el-button v-debounce="3000" type="primary" @click="confirmPositionSelection">
              确认选择
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- Process Steps Selection Dialog -->
      <el-dialog
        v-model="showProcessStepsDialog"
        title="选择流程步骤"
        width="800px"
        :before-close="closeProcessStepsDialog"
      >
        <div v-loading="processStepsLoading" element-loading-text="加载中..." element-loading-background="rgba(255, 255, 255, 0.8)">
          <div class="process-steps-container" style="position: relative; max-height: 400px;">
            <div class="process-steps-list" style="max-height: 400px; overflow-y: auto;">
              <!-- 单选模式 -->
              <el-radio-group v-model="selectedProcessStep" class="process-steps-radio-group">
                <div v-for="(step, index) in processStepsList" :key="index" class="process-step-item">
                  <el-radio :label="index" class="process-step-radio" @change="selectProcessStep(step)">
                    <div class="process-step-content">
                      <div class="process-step-title text-gray-900 font-medium">
                        {{ step.businessDomain }}
                      </div>
                      <div v-if="step.processDescription" class="process-step-description mt-1 text-sm text-gray-600">
                        {{ step.processDescription }}
                      </div>
                    </div>
                  </el-radio>
                </div>
              </el-radio-group>

              <!-- 空状态 -->
              <div v-if="processStepsList.length === 0 && !processStepsLoading" class="py-8 text-center text-gray-500">
                <el-empty description="暂无流程步骤数据" />
              </div>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <template #footer>
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
              已选择 {{ selectedProcessStep !== -1 ? 1 : 0 }} 项
            </div>
            <div>
              <el-button v-debounce="3000" @click="closeProcessStepsDialog">
                取消
              </el-button>
              <el-button v-debounce="3000" type="primary" @click="confirmProcessStepSelection">
                确定
              </el-button>
            </div>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<style scoped>
.el-card {
margin-bottom: 20px;
}
.el-card__header {
padding: 16px 20px;
}
.el-card__body {
padding: 20px;
}
.upload-demo {
text-align: center;
}
.el-upload-dragger {
width: 100%;
padding: 40px 0;
}
.el-drawer__body {
padding: 0;
}
.el-drawer__header {
margin-bottom: 0;
padding: 16px 20px;
}
.el-progress {
margin-bottom: 20px;
}
.el-tag {
margin-left: 10px;
}
.el-input__wrapper {
background-color: var(--el-fill-color-blank);
font-size: 14px;
}
.el-textarea__inner {
min-height: 100px;
font-size: 14px;
}
.bg-green-50 {
background-color: #f0f9eb;
}
.item-title{
  border-bottom: 1px solid #dcdfe6;
}

.process-steps-container {
  position: relative;

  :deep(.el-loading-mask) {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
  }
}

.process-steps-radio-group {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.process-step-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s;
  width: 100%;
  display: block;

  &:hover {
    border-color: #409eff;
    background-color: #f5f7fa;
  }

  .process-step-radio {
    width: 100%;
    display: flex;
    align-items: flex-start;

    :deep(.el-radio__input) {
      margin-top: 2px;
      flex-shrink: 0;
    }

    :deep(.el-radio__label) {
      width: 100%;
      padding-left: 8px;
      line-height: 1.4;
    }
  }

  .process-step-content {
    width: 100%;
    min-width: 0;
  }

  .process-step-title {
    word-break: break-all;
    white-space: normal;
    line-height: 1.4;
  }

  .process-step-description {
    word-break: break-all;
    white-space: normal;
    line-height: 1.3;
  }

  .process-step-meta {
    word-break: break-all;
    white-space: normal;
    line-height: 1.3;
  }
}
</style>
