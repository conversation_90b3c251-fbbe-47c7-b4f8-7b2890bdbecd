<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ChatRound,
  DataAnalysis,
  Document,
  Download,
  Edit,
  List,
  Location,
  More,
  Notebook,
  Printer,
  Search,
  Upload,
  User,
  VideoCamera,
} from '@element-plus/icons-vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import problemTaskApi from '@/api/problemTask'
import dictApi from '@/api/modules/system/dict'

const route = useRoute()
const router = useRouter()

const taskId = route.query.id as string
const taskDetail = ref<any>(null)
const loading = ref(true)

// 调查来源选项 - 字典33
const investigateSourceOptions = ref<Array<{ label: string, value: string }>>([])

const activeTab = ref('reports')
const recordSummary = ref('')
const recordDate = ref('')
const participants = ref<string[]>([])
const conclusionStatus = ref('draft')
const conclusionContent = ref('')

// 新增调查报告
function handleAddReport() {
  router.push({
    path: '/respond/violationIssues/taskManagement/taskReport',
    query: {
      investigateId: taskId,
    },
  })
}

// 新增调查记录
function handleAddReview() {
  router.push({
    path: '/respond/violationIssues/taskManagement/taskReview',
    query: {
      investigateId: taskId,
    },
  })
}

// 获取字典数据
async function fetchDictOptions() {
  try {
    // 获取调查来源选项 (33)
    const sourceResponse = await dictApi.dictAll(33)
    if (sourceResponse) {
      investigateSourceOptions.value = sourceResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 获取任务详情
async function fetchTaskDetail() {
  try {
    loading.value = true
    const res = await problemTaskApi.getTaskDetail(taskId)
    if (res) {
      taskDetail.value = res
    }
    else {
      ElMessage.error('获取任务详情失败')
    }
  }
  catch (error) {
    console.error('获取任务详情出错:', error)
    ElMessage.error('获取任务详情出错')
  }
  finally {
    loading.value = false
  }
}

onMounted(async () => {
  // 先获取字典数据
  await fetchDictOptions()
  // 再获取任务详情
  fetchTaskDetail()
})

const evidenceFilter = ref({
  type: '',
  status: '',
  keyword: '',
})

const involvedPersons = ref<Array<{ name: string, role: string, department: string }>>([

])

const investigationActivities = ref<Array<{ date: string, type: string, description: string, participants: string, location: string }>>([

])

const evidenceList = ref<Array<{ id: string, name: string, type: string, uploadTime: string, uploader: string, category: string, status: string }>>([

])

const investigationFindings = ref<Array<{ id: string, description: string, severity: string, date: string, finder: string, relatedPersons: string, status: string }>>([

])

const violationRecords = ref([
  {
    type: '数据泄露',
    description: '未经授权下载客户数据',
    basis: '公司数据安全政策第3.2条',
    responsible: '王伟',
    suggestion: '纪律处分，数据安全培训',
  },
])

const recommendations = ref([
  {
    type: '技术措施',
    description: '加强数据访问监控系统',
    department: 'IT安全部',
    priority: '高',
    effect: '实时监控异常数据访问',
  },
  {
    type: '管理措施',
    description: '修订数据访问审批流程',
    department: '合规部',
    priority: '中',
    effect: '规范数据访问权限管理',
  },
])

const operationLogs = ref([
  {
    time: '2023-04-25 09:30',
    operator: '张明远',
    type: '创建调查',
    content: '创建了调查任务"员工数据泄露事件调查"',
    ip: '*************',
  },
  {
    time: '2023-04-25 14:15',
    operator: '李思颖',
    type: '上传证据',
    content: '上传了证据"访问日志_0420"',
    ip: '*************',
  },
  {
    time: '2023-04-26 10:20',
    operator: '张明远',
    type: '添加记录',
    content: '添加了调查活动"数据分析"',
    ip: '*************',
  },
])

const changeRecords = ref([
  {
    time: '2023-04-25 09:30',
    changer: '张明远',
    field: '状态',
    before: '',
    after: '未开始',
    reason: '创建调查',
  },
  {
    time: '2023-04-25 14:15',
    changer: '李思颖',
    field: '证据',
    before: '0',
    after: '1',
    reason: '上传证据',
  },
])

const relatedPersons = ref([
  { name: '张明远', role: '负责人', department: '合规部' },
  { name: '李思颖', role: '协调人', department: 'IT安全部' },
  { name: '王伟', role: '被调查', department: '市场部' },
  { name: '陈明', role: '技术支持', department: 'IT部' },
])

const relatedSurveys = ref([
  { id: 'INV-2023-0320', title: '市场部数据使用合规审查', status: '已完成', relation: '相关调查' },
  { id: 'INV-2023-0410', title: 'IT系统安全漏洞检查', status: '进行中', relation: '相关调查' },
])

const filteredEvidence = computed(() => {
  return evidenceList.value.filter((item) => {
    const typeMatch = !evidenceFilter.value.type || item.type === evidenceFilter.value.type
    const statusMatch = !evidenceFilter.value.status || item.status === evidenceFilter.value.status
    const keywordMatch
        = !evidenceFilter.value.keyword
        || item.name.toLowerCase().includes(evidenceFilter.value.keyword.toLowerCase())
        || item.id.toLowerCase().includes(evidenceFilter.value.keyword.toLowerCase())
    return typeMatch && statusMatch && keywordMatch
  })
})

function getActivityTagType(type: string) {
  const map: Record<string, string> = {
    文件审阅: 'info',
    人员访谈: 'success',
    现场调查: 'warning',
    数据分析: 'info',
    专家咨询: 'danger',
  }
  return map[type] || 'info'
}

function getEvidenceStatusTagType(status: string) {
  const map: Record<string, string> = {
    pending: 'warning',
    verified: 'success',
    rejected: 'danger',
  }
  return map[status] || 'info'
}

function getSeverityTagType(severity: string) {
  const map: Record<string, string> = {
    高: 'danger',
    中: 'warning',
    低: 'info',
  }
  return map[severity] || 'info'
}

function getFindingStatusTagType(status: string) {
  const map: Record<string, string> = {
    初步发现: 'info',
    已确认: 'success',
    已驳回: 'danger',
  }
  return map[status] || ''
}

function getStatusTagType(status: string) {
  const map: Record<string, string> = {
    NO_START: 'info',
    PROGRESSING: 'warning',
    FINISHED: 'success',
    PAUSED: 'info',
    CANCELED: 'danger',
  }
  return map[status] || 'info'
}

function getStatusText(status: string) {
  const map: Record<string, string> = {
    NO_START: '未开始',
    PROGRESSING: '进行中',
    FINISHED: '已完成',
    PAUSED: '已暂停',
    CANCELED: '已取消',
  }
  return map[status] || '未知状态'
}

function getLevelTagType(level: string) {
  const map: Record<string, string> = {
    HIGH: 'danger',
    MIDDLE: 'warning',
    LOW: 'info',
  }
  return map[level] || 'info'
}

function getLevelText(level: string) {
  const map: Record<string, string> = {
    HIGH: '高',
    MIDDLE: '中',
    LOW: '低',
  }
  return map[level] || '未知'
}

function getInvestigateTypeText(type: string) {
  const map: Record<string, string> = {
    ADVERTISING_COMPLIANCE: '广告合规',
    SUPPLIER_MANAGEMENT: '供应商管理',
    EMPLOYEE_TRAINING: '员工培训',
    FINANCIAL_AUDITING: '财务审计',
  }
  return map[type] || '未知类型'
}

function getInvestigateSourceText(source: string) {
  const option = investigateSourceOptions.value.find(item => item.value === source)
  return option ? option.label : '未知来源'
}

function getRoleText(role: string) {
  const map: Record<string, string> = {
    PRIMARY_RESPONSIBLE: '主要责任人',
    SECONDARY_RESPONSIBLE: '次要责任人',
    WITNESS: '证人',
    INFORMANT: '举报人',
  }
  return map[role] || '未知角色'
}

// 报告类型文本转换
function getReportTypeText(type: string) {
  const map: Record<string, string> = {
    SPECIAL_INVESTIGE: '专项调查',
    ROUTINE_INSPECTION: '例行检查',
    COMPLAINT_INVESTIGATION: '投诉调查',
    AUDIT_INVESTIGATION: '审计调查',
  }
  return map[type] || '未知类型'
}

// 报告状态文本转换
function getReportStatusText(status: string) {
  const map: Record<string, string> = {
    DRAFT: '草稿',
    MODIFY: '待修改',
    PENDING: '待审核',
    APPROVED: '已通过',
    REJECTED: '已驳回',
  }
  return map[status] || '未知状态'
}

// 报告状态标签类型
function getReportStatusTagType(status: string) {
  const map: Record<string, string> = {
    DRAFT: 'info',
    MODIFY: 'warning',
    PENDING: 'warning',
    APPROVED: 'success',
    REJECTED: 'danger',
  }
  return map[status] || 'info'
}

// 记录类型文本转换
function getRecordTypeText(type: string) {
  const map: Record<string, string> = {
    PERSONNEL_INTERVIEW: '人员访谈',
    DOCUMENT_REVIEW: '文件审阅',
    SITE_INVESTIGATION: '现场调查',
    DATA_ANALYSIS: '数据分析',
    EXPERT_CONSULTATION: '专家咨询',
  }
  return map[type] || '未知类型'
}

// 查看报告
function viewReport(reportId: number) {
  router.push({
    path: '/respond/violationIssues/taskManagement/taskReport',
    query: {
      id: reportId,
      mode: 'view',
    },
  })
}

// 编辑报告
function editReport(reportId: number) {
  router.push({
    path: '/respond/violationIssues/taskManagement/taskReport',
    query: {
      id: reportId,
      mode: 'edit',
    },
  })
}

// 编辑任务
function handleEditTask() {
  router.push({
    path: '/respond/violationIssues/taskManagement/addEdit',
    query: {
      id: taskId,
      mode: 'edit',
    },
  })
}

function handleBack() {
  router.back()
}

// 下载报告
function downloadReport(_reportId: number) {
  // TODO: 实现报告下载功能
  ElMessage.success('报告下载功能开发中')
}

// 查看记录
function viewRecord(recordId: number) {
  router.push({
    path: '/respond/violationIssues/taskManagement/taskReview',
    query: {
      id: recordId,
      mode: 'view',
    },
  })
}

// 编辑记录
function editRecord(recordId: number) {
  router.push({
    path: '/respond/violationIssues/taskManagement/taskReview',
    query: {
      id: recordId,
      mode: 'edit',
    },
  })
}

// 在组件挂载时获取任务详情
onMounted(() => {
  fetchTaskDetail()
})
</script>

<template>
  <div v-loading="loading" class="absolute-container" element-loading-text="加载中...">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{ taskDetail?.title || '加载中...' }}
            </h1>
            <el-tag :type="(getStatusTagType(taskDetail?.status) as any)" class="ml-4">
              {{ getStatusText(taskDetail?.status) }}
            </el-tag>
          </div>
          <div class="flex items-center space-x-4">
            <el-button :disabled="taskDetail?.reportList?.length > 0" type="primary" class="!rounded-button whitespace-nowrap" @click="handleAddReport">
              新增调查报告
            </el-button>
            <el-button :disabled="taskDetail?.recordList?.length > 0" type="primary" class="!rounded-button whitespace-nowrap" @click="handleAddReview">
              新增调查记录
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="handleEditTask">
              编辑
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="handleBack">
              返回
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="24">
            <el-card class="">
              <!-- 基本信息区 -->
              <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                <div class="grid grid-cols-2 gap-6">
                  <div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        调查编号
                      </h3>
                      <p class="mt-1">
                        {{ taskDetail?.investigateCode || '加载中...' }}
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        调查标题
                      </h3>
                      <p class="mt-1">
                        {{ taskDetail?.title || '加载中...' }}
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        调查类型
                      </h3>
                      <p class="mt-1">
                        {{ getInvestigateTypeText(taskDetail?.investigateType) }}
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        优先级
                      </h3>
                      <el-tag :type="(getLevelTagType(taskDetail?.level) as any)" class="mt-1">
                        {{ getLevelText(taskDetail?.level) }}
                      </el-tag>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        调查来源
                      </h3>
                      <p class="mt-1">
                        {{ getInvestigateSourceText(taskDetail?.investigateSource) }}
                      </p>
                    </div>
                  </div>
                  <div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        开始日期
                      </h3>
                      <p class="mt-1">
                        {{ taskDetail?.startDate || '暂无' }}
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        计划完成日期
                      </h3>
                      <p class="mt-1">
                        {{ taskDetail?.finishDate || '暂无' }}
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        负责人
                      </h3>
                      <p class="mt-1">
                        {{ taskDetail?.dutyEmployeeName || '暂无' }}
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        协调人
                      </h3>
                      <p class="mt-1">
                        {{ taskDetail?.coordinateEmployeeName || '暂无' }}
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        状态
                      </h3>
                      <el-tag :type="(getStatusTagType(taskDetail?.status) as any)" class="mt-1">
                        {{ getStatusText(taskDetail?.status) }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                <h2 class="mb-4 text-lg font-bold">
                  调查概述
                </h2>
                <div class="mb-6">
                  <h3 class="text-sm text-gray-500 font-medium">
                    调查背景
                  </h3>
                  <p class="mt-2 text-gray-700">
                    {{ taskDetail?.investigateBackground || '暂无调查背景信息' }}
                  </p>
                </div>
                <div class="mb-6">
                  <h3 class="text-sm text-gray-500 font-medium">
                    调查目标
                  </h3>
                  <p class="mt-2 text-gray-700">
                    {{ taskDetail?.investigateTarget || '暂无调查目标信息' }}
                  </p>
                </div>
                <div class="mb-6">
                  <h3 class="text-sm text-gray-500 font-medium">
                    调查范围
                  </h3>
                  <p class="mt-2 text-gray-700">
                    {{ taskDetail?.investigateRange || '暂无调查范围信息' }}
                  </p>
                </div>
                <div v-if="false">
                  <h3 class="text-sm text-gray-500 font-medium">
                    涉及人员/部门
                  </h3>
                  <template v-if="taskDetail?.involveList && taskDetail.involveList.length > 0">
                    <el-table :data="taskDetail.involveList" class="mt-2" border>
                      <el-table-column prop="name" label="姓名" width="120" />
                      <el-table-column prop="department" label="部门" width="120" />
                      <el-table-column prop="position" label="职位" width="120" />
                      <el-table-column label="角色" width="120">
                        <template #default="{ row }">
                          {{ getRoleText(row.role) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="状态" width="100">
                        <template #default="{ row }">
                          <el-tag :type="row.role === 'PRIMARY_RESPONSIBLE' ? 'danger' : 'info'">
                            {{ row.role === 'PRIMARY_RESPONSIBLE' ? '被调查' : '协助' }}
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                  </template>
                  <div v-else class="mt-2 rounded-lg bg-white p-4 text-center text-gray-500">
                    暂无涉及人员信息
                  </div>
                </div>
              </div>
            </el-card>
            <!-- 调查报告和记录 -->
            <el-card class="mt-20">
              <template #header>
                <div class="text-lg font-bold">
                  调查报告与记录
                </div>
              </template>
              <el-tabs v-model="activeTab">
                <el-tab-pane label="调查报告" name="reports">
                  <div class="mb-4">
                    <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleAddReport">
                      新增调查报告
                    </el-button>
                  </div>
                  <template v-if="taskDetail?.reportList && taskDetail.reportList.length > 0">
                    <div class="grid grid-cols-1 gap-4">
                      <el-card v-for="report in taskDetail.reportList" :key="report.id" class="shadow-sm">
                        <div class="grid grid-cols-2 gap-6">
                          <div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                报告编号
                              </h4>
                              <p class="text-gray-900">
                                {{ report.reportCode }}
                              </p>
                            </div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                报告标题
                              </h4>
                              <p class="text-gray-900">
                                {{ report.title }}
                              </p>
                            </div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                报告类型
                              </h4>
                              <el-tag type="info">
                                {{ getReportTypeText(report.reportType) }}
                              </el-tag>
                            </div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                编制日期
                              </h4>
                              <p class="text-gray-900">
                                {{ report.establishDate }}
                              </p>
                            </div>
                          </div>
                          <div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                优先级
                              </h4>
                              <el-tag :type="(getLevelTagType(report.level) as any)">
                                {{ getLevelText(report.level) }}
                              </el-tag>
                            </div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                状态
                              </h4>
                              <el-tag :type="(getReportStatusTagType(report.status) as any)">
                                {{ getReportStatusText(report.status) }}
                              </el-tag>
                            </div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                创建人
                              </h4>
                              <p class="text-gray-900">
                                {{ report.createdBy }}
                              </p>
                            </div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                创建时间
                              </h4>
                              <p class="text-gray-900">
                                {{ report.createdAt }}
                              </p>
                            </div>
                          </div>
                        </div>
                        <!-- 新增字段 -->
                        <div class="mt-6 border-t border-gray-200 pt-6">
                          <div class="grid grid-cols-1 gap-6">
                            <div v-if="report.investigateBackground">
                              <h4 class="mb-2 text-sm text-gray-500 font-medium">
                                调查背景
                              </h4>
                              <p class="text-gray-900 leading-relaxed">
                                {{ report.investigateBackground }}
                              </p>
                            </div>
                            <div v-if="report.investigateMethod">
                              <h4 class="mb-2 text-sm text-gray-500 font-medium">
                                调查方法
                              </h4>
                              <p class="text-gray-900 leading-relaxed">
                                {{ report.investigateMethod }}
                              </p>
                            </div>
                            <div v-if="report.investigateProcess">
                              <h4 class="mb-2 text-sm text-gray-500 font-medium">
                                调查过程
                              </h4>
                              <p class="text-gray-900 leading-relaxed">
                                {{ report.investigateProcess }}
                              </p>
                            </div>
                            <div v-if="report.investigateFound">
                              <h4 class="mb-2 text-sm text-gray-500 font-medium">
                                调查发现
                              </h4>
                              <p class="text-gray-900 leading-relaxed">
                                {{ report.investigateFound }}
                              </p>
                            </div>
                            <div v-if="report.investigateConclusion">
                              <h4 class="mb-2 text-sm text-gray-500 font-medium">
                                调查结论
                              </h4>
                              <p class="text-gray-900 leading-relaxed">
                                {{ report.investigateConclusion }}
                              </p>
                            </div>
                            <div v-if="report.recommendMeasure">
                              <h4 class="mb-2 text-sm text-gray-500 font-medium">
                                建议措施
                              </h4>
                              <p class="text-gray-900 leading-relaxed">
                                {{ report.recommendMeasure }}
                              </p>
                            </div>
                          </div>
                        </div>
                        <!-- <div class="mt-4 border-t border-gray-200 pt-4">
                          <div class="mb-2">
                            <h4 class="text-sm text-gray-500 font-medium">
                              附件内容
                            </h4>
                          </div>
                          <UploadMbb
                            :model-value="report.attachmentList || []"
                            :readonly="true"
                            :max="10"
                            :size="20"
                            service-name="whiskerguardregulatoryservice"
                            category-name="investigation-report"
                            :use-file-path="true"
                            tip-text="附件展示"
                          />
                        </div> -->
                      </el-card>
                    </div>
                  </template>
                  <div v-else class="py-8 text-center text-gray-500">
                    暂无调查报告
                  </div>
                </el-tab-pane>

                <el-tab-pane label="调查记录" name="records">
                  <div class="mb-4">
                    <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleAddReview">
                      新增调查记录
                    </el-button>
                  </div>
                  <template v-if="taskDetail?.recordList && taskDetail.recordList.length > 0">
                    <div class="grid grid-cols-1 gap-4">
                      <el-card v-for="record in taskDetail.recordList" :key="record.id" class="shadow-sm">
                        <div class="grid grid-cols-2 gap-6">
                          <div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                记录编号
                              </h4>
                              <p class="text-gray-900">
                                {{ record.recordCode }}
                              </p>
                            </div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                记录类型
                              </h4>
                              <el-tag type="info">
                                {{ getRecordTypeText(record.recordType) }}
                              </el-tag>
                            </div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                地点/渠道
                              </h4>
                              <p class="text-gray-900">
                                {{ record.location }}
                              </p>
                            </div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                记录内容
                              </h4>
                              <p class="text-gray-900" :title="record.content">
                                {{ record.content }}
                              </p>
                            </div>
                          </div>
                          <div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                问题发现
                              </h4>
                              <p class="text-gray-900" :title="record.discover">
                                {{ record.discover }}
                              </p>
                            </div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                创建人
                              </h4>
                              <p class="text-gray-900">
                                {{ record.updatedBy }}
                              </p>
                            </div>
                            <div class="mb-4">
                              <h4 class="mb-1 text-sm text-gray-500 font-medium">
                                创建时间
                              </h4>
                              <p class="text-gray-900">
                                {{ record.createdAt }}
                              </p>
                            </div>
                          </div>
                        </div>
                        <!-- <div class="mt-4 border-t border-gray-200 pt-4">
                          <div class="mb-2">
                            <h4 class="text-sm text-gray-500 font-medium">
                              附件内容
                            </h4>
                          </div>
                          <UploadMbb
                            :model-value="record.attachmentList || []"
                            :readonly="true"
                            :max="10"
                            :size="20"
                            service-name="whiskerguardviolationservice"
                            category-name="investigate-record"
                            :use-file-path="true"
                            tip-text="附件展示"
                          />
                        </div> -->
                      </el-card>
                    </div>
                  </template>
                  <div v-else class="py-8 text-center text-gray-500">
                    暂无调查记录
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
            <el-card v-if="false" class="mt-20">
              <el-tabs v-model="activeTab">
                <el-tab-pane label="调查记录" name="records">
                  <div class="mb-6">
                    <h3 class="text-lg font-bold">
                      调查计划
                    </h3>
                    <p class="mt-2 text-gray-700">
                      1. 第一阶段（4月25日-4月27日）：收集日志证据，分析数据流向<br>
                      2. 第二阶段（4月28日-5月2日）：访谈相关人员，确认操作动机<br>
                      3. 第三阶段（5月3日-5月7日）：评估影响范围，制定补救措施<br>
                      4. 第四阶段（5月8日-5月10日）：完成报告，提出改进建议
                    </p>
                    <el-button type="primary" class="!rounded-button mt-4 whitespace-nowrap">
                      编辑计划
                    </el-button>
                  </div>

                  <div class="mb-6">
                    <h3 class="text-lg font-bold">
                      快速添加记录
                    </h3>
                    <div class="mt-4 flex space-x-2">
                      <el-button class="!rounded-button whitespace-nowrap">
                        <el-icon>
                          <Document />
                        </el-icon> 文件审阅
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        <el-icon>
                          <User />
                        </el-icon> 人员访谈
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        <el-icon>
                          <Location />
                        </el-icon> 现场调查
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        <el-icon>
                          <DataAnalysis />
                        </el-icon> 数据分析
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        <el-icon>
                          <ChatRound />
                        </el-icon> 专家咨询
                      </el-button>
                    </div>
                    <div class="grid grid-cols-2 mt-4 gap-4">
                      <el-input v-model="recordSummary" placeholder="记录摘要" />
                      <el-date-picker v-model="recordDate" type="datetime" placeholder="选择日期时间" />
                    </div>
                    <el-select v-model="participants" multiple placeholder="选择参与人员" class="mt-4 w-full">
                      <el-option
                        v-for="person in involvedPersons" :key="person.name" :label="person.name"
                        :value="person.name"
                      />
                    </el-select>
                    <el-button type="text" class="mt-2">
                      添加详细记录
                    </el-button>
                  </div>

                  <div>
                    <div class="flex items-center justify-between">
                      <h3 class="text-lg font-bold">
                        调查活动
                      </h3>
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        新增活动记录
                      </el-button>
                    </div>
                    <el-table :data="investigationActivities" class="mt-4" border>
                      <el-table-column prop="date" label="活动日期" width="150" />
                      <el-table-column prop="type" label="活动类型" width="120">
                        <template #default="{ row }">
                          <el-tag :type="(getActivityTagType(row.type) as any)">
                            {{ row.type }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="description" label="活动描述" />
                      <el-table-column prop="participants" label="参与人员" width="150" />
                      <el-table-column prop="location" label="地点" width="120" />
                      <el-table-column label="操作" width="180">
                        <template #default>
                          <el-button type="text">
                            查看详情
                          </el-button>
                          <el-button type="text">
                            编辑
                          </el-button>
                          <el-button type="text">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="证据材料" name="evidence">
                  <div class="mb-6 flex items-center space-x-4">
                    <el-select v-model="evidenceFilter.type" placeholder="证据类型" clearable>
                      <el-option label="文档" value="document" />
                      <el-option label="图片" value="image" />
                      <el-option label="音视频" value="media" />
                      <el-option label="日志" value="log" />
                    </el-select>
                    <el-select v-model="evidenceFilter.status" placeholder="证据状态" clearable>
                      <el-option label="待验证" value="pending" />
                      <el-option label="已验证" value="verified" />
                      <el-option label="已驳回" value="rejected" />
                    </el-select>
                    <el-input v-model="evidenceFilter.keyword" placeholder="搜索证据" suffix-icon="Search" />
                    <el-button type="primary" class="!rounded-button whitespace-nowrap">
                      上传证据
                    </el-button>
                  </div>

                  <el-table :data="filteredEvidence" border>
                    <el-table-column prop="id" label="证据ID" width="120" />
                    <el-table-column prop="name" label="证据名称" width="180" />
                    <el-table-column prop="type" label="证据类型" width="120">
                      <template #default="{ row }">
                        <el-tag :type="row.type === 'document' ? 'primary' : 'info'">
                          {{ row.type }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="uploadTime" label="上传时间" width="150" />
                    <el-table-column prop="uploader" label="上传人" width="120" />
                    <el-table-column prop="category" label="证据分类" width="120" />
                    <el-table-column prop="status" label="状态" width="120">
                      <template #default="{ row }">
                        <el-tag :type="(getEvidenceStatusTagType(row.status) as any)">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="180">
                      <template #default>
                        <el-button type="text">
                          查看
                        </el-button>
                        <el-button type="text">
                          验证
                        </el-button>
                        <el-button type="text">
                          下载
                        </el-button>
                        <el-button type="text">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>

                <el-tab-pane label="调查发现" name="findings">
                  <div class="mb-6">
                    <el-button type="primary" class="!rounded-button whitespace-nowrap">
                      新增发现
                    </el-button>
                  </div>
                  <el-table :data="investigationFindings" border>
                    <el-table-column prop="id" label="发现ID" width="120" />
                    <el-table-column prop="description" label="发现描述" width="300" />
                    <el-table-column prop="severity" label="严重程度" width="120">
                      <template #default="{ row }">
                        <el-tag :type="(getSeverityTagType(row.severity) as any)">
                          {{ row.severity }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="date" label="发现日期" width="120" />
                    <el-table-column prop="finder" label="发现人" width="120" />
                    <el-table-column prop="relatedPersons" label="涉及人员" width="150" />
                    <el-table-column prop="status" label="状态" width="120">
                      <template #default="{ row }">
                        <el-tag :type="(getFindingStatusTagType(row.status) as any)">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="180">
                      <template #default>
                        <el-button type="text">
                          查看详情
                        </el-button>
                        <el-button type="text">
                          编辑
                        </el-button>
                        <el-button type="text">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>

                <el-tab-pane label="调查结论" name="conclusion">
                  <div class="mb-6">
                    <h3 class="text-lg font-bold">
                      调查结论
                    </h3>
                    <el-select v-model="conclusionStatus" class="mt-2" placeholder="结论状态">
                      <el-option label="初步结论" value="draft" />
                      <el-option label="最终结论" value="final" />
                    </el-select>
                    <el-input
                      v-model="conclusionContent" type="textarea" :rows="4" placeholder="请输入调查结论"
                      class="mt-4"
                    />
                    <div class="mt-4 flex items-center justify-between">
                      <div>
                        <span class="text-sm text-gray-500">结论日期: 2023-05-08</span>
                        <span class="ml-4 text-sm text-gray-500">结论人: 张明远</span>
                      </div>
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        编辑结论
                      </el-button>
                    </div>
                  </div>

                  <div class="mb-6">
                    <h3 class="text-lg font-bold">
                      结论生成助手
                    </h3>
                    <div class="mt-4 flex space-x-4">
                      <el-button class="!rounded-button whitespace-nowrap">
                        AI生成结论
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        结论优化
                      </el-button>
                    </div>
                    <div class="mt-4 border border-gray-200 rounded p-4">
                      <p class="text-gray-700">
                        基于当前调查发现，AI生成的初步结论将显示在这里...
                      </p>
                    </div>
                  </div>

                  <div class="mb-6">
                    <div class="flex items-center justify-between">
                      <h3 class="text-lg font-bold">
                        违规认定
                      </h3>
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        新增违规认定
                      </el-button>
                    </div>
                    <el-table :data="violationRecords" class="mt-4" border>
                      <el-table-column prop="type" label="违规类型" width="120" />
                      <el-table-column prop="description" label="违规描述" width="300" />
                      <el-table-column prop="basis" label="违规依据" width="200" />
                      <el-table-column prop="responsible" label="责任人/部门" width="150" />
                      <el-table-column prop="suggestion" label="建议处理措施" />
                      <el-table-column label="操作" width="120">
                        <template #default>
                          <el-button type="text">
                            编辑
                          </el-button>
                          <el-button type="text">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <div>
                    <div class="flex items-center justify-between">
                      <h3 class="text-lg font-bold">
                        建议措施
                      </h3>
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        新增建议措施
                      </el-button>
                    </div>
                    <el-table :data="recommendations" class="mt-4" border>
                      <el-table-column prop="type" label="措施类型" width="120" />
                      <el-table-column prop="description" label="措施描述" width="300" />
                      <el-table-column prop="department" label="建议部门" width="150" />
                      <el-table-column prop="priority" label="优先级" width="100">
                        <template #default="{ row }">
                          <el-tag :type="row.priority === '高' ? 'danger' : row.priority === '中' ? 'warning' : 'info'">
                            {{ row.priority }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="effect" label="预期效果" />
                      <el-table-column label="操作" width="120">
                        <template #default>
                          <el-button type="text">
                            编辑
                          </el-button>
                          <el-button type="text">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="操作历史" name="history">
                  <div class="mb-6">
                    <h3 class="text-lg font-bold">
                      操作日志
                    </h3>
                    <div class="mt-4">
                      <el-timeline>
                        <el-timeline-item
                          v-for="(log, index) in operationLogs" :key="index" :timestamp="log.time"
                          placement="top"
                        >
                          <el-card>
                            <div class="flex items-center justify-between">
                              <div>
                                <span class="font-medium">{{ log.operator }}</span>
                                <span class="ml-2 text-gray-500">{{ log.type }}</span>
                              </div>
                              <span class="text-sm text-gray-500">{{ log.ip }}</span>
                            </div>
                            <p class="mt-2 text-gray-700">
                              {{ log.content }}
                            </p>
                          </el-card>
                        </el-timeline-item>
                      </el-timeline>
                    </div>
                  </div>

                  <div>
                    <h3 class="text-lg font-bold">
                      变更记录
                    </h3>
                    <el-table :data="changeRecords" class="mt-4" border>
                      <el-table-column prop="time" label="变更时间" width="150" />
                      <el-table-column prop="changer" label="变更人" width="120" />
                      <el-table-column prop="field" label="变更字段" width="150" />
                      <el-table-column prop="before" label="变更前值" width="200" />
                      <el-table-column prop="after" label="变更后值" width="200" />
                      <el-table-column prop="reason" label="变更原因" />
                    </el-table>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col v-if="false" :span="6">
            <el-card class="">
              <template #header>
                <div class="f-16 fw-600">
                  任务时间线
                </div>
              </template>
              <el-timeline class="mt-4">
                <el-timeline-item timestamp="2023-04-20" placement="top">
                  事件发现
                </el-timeline-item>
                <el-timeline-item timestamp="2023-04-25" placement="top">
                  调查启动
                </el-timeline-item>
                <el-timeline-item timestamp="2023-04-28" placement="top">
                  初步分析
                </el-timeline-item>
                <el-timeline-item timestamp="2023-05-03" placement="top" type="primary" color="#409EFF">
                  当前阶段
                </el-timeline-item>
                <el-timeline-item timestamp="2023-05-10" placement="top">
                  计划完成
                </el-timeline-item>
              </el-timeline>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    相关人员
                  </div>
                  <el-button type="text">
                    添加人员
                  </el-button>
                </div>
              </template>
              <div class="mt-4 space-y-4">
                <div v-for="person in relatedPersons" :key="person.name" class="flex items-center">
                  <div class="mr-3 h-10 w-10 rounded-full bg-gray-200" />
                  <div>
                    <p class="font-medium">
                      {{ person.name }}
                    </p>
                    <p class="text-sm text-gray-500">
                      {{ person.role }} · {{ person.department }}
                    </p>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关调查
                </div>
              </template>
              <div class="mt-4 space-y-4">
                <div
                  v-for="survey in relatedSurveys" :key="survey.id"
                  class="cursor-pointer border border-gray-200 rounded p-3 hover:bg-gray-50"
                >
                  <p class="font-medium">
                    {{ survey.title }}
                  </p>
                  <div class="mt-1 flex items-center justify-between">
                    <el-tag :type="survey.status === '已完成' ? 'success' : 'warning'" size="small">
                      {{ survey.status }}
                    </el-tag>
                    <span class="text-sm text-gray-500">{{ survey.relation }}</span>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="mt-4 space-y-3">
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <Notebook />
                  </el-icon> 添加调查记录
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <Upload />
                  </el-icon> 上传证据
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <Search />
                  </el-icon> 添加调查发现
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <Document />
                  </el-icon> 生成调查报告
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <VideoCamera />
                  </el-icon> 发起会议
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <List />
                  </el-icon> 创建处理措施
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .container {
    max-width: 1440px;
  }
</style>
