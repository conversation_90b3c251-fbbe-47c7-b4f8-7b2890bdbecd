<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import { Delete, Download, Search, View } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import curriculumApi from '@/api/curriculum'

// 统计卡片数据
const stats = ref([
  { label: '总课程数', value: 0, unit: '门课程' },
  { label: '总学习时长', value: 0, unit: '小时' },
  { label: '总参与人次', value: 0, unit: '人次' },
  { label: '平均完成率', value: 0, unit: '%', progress: 0 },
])

// 接口返回的数据
const reportData = ref<any>({})
const loading = ref(false)

// 图表数据
const departmentNames = ref<string[]>([])

// 获取报告数据
function fetchReportData() {
  loading.value = true
  const params = {
    // startDate: null,
    // endDate: null,
    // departmentIds: [],
    // courseTypes: [],
    // trainingPlanIds: [],
    // includeSubDepartments: true,
    // trendMonths: 12,
  }

  curriculumApi.learning.getReportStatistics(params).then((response: any) => {
    reportData.value = response
    updateStats(response)
    updateCharts(response)
  }).catch((error: any) => {
    console.error('获取报告数据失败:', error)
  }).finally(() => {
    loading.value = false
  })
}

// 更新统计卡片数据
function updateStats(data: any) {
  if (!data) {
    return
  }

  stats.value = [
    { label: '总课程数', value: data.totalCourses || 0, unit: '门课程' },
    { label: '总学习时长', value: data.totalLearningHours || 0, unit: '小时' },
    { label: '总参与人次', value: data.totalParticipants || 0, unit: '人次' },
    { label: '平均完成率', value: data.averageCompletionRate || 0, unit: '%', progress: data.averageCompletionRate || 0 },
  ]
}

// 图表引用
const pieChart = ref<HTMLElement>()
const lineChart = ref<HTMLElement>()
const barChart = ref<HTMLElement>()

// 更新图表数据
function updateCharts(data: any) {
  if (!data) {
    return
  }

  // 饼图 - 课程类型分布
  const pieInstance = echarts.init(pieChart.value)
  const pieData = data.courseTypeDistribution?.map((item: any) => ({
    value: item.courseCount,
    name: item.courseTypeName,
    itemStyle: {
      color: item.color,
    },
  })) || []

  pieInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '课程类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: pieData,
      },
    ],
  })

  // 折线图 - 月度学习趋势
  const lineInstance = echarts.init(lineChart.value)
  const monthLabels = data.monthlyLearningTrend?.map((item: any) => item.monthLabel) || []
  const participantData = data.monthlyLearningTrend?.map((item: any) => item.participantCount) || []
  const completedData = data.monthlyLearningTrend?.map((item: any) => item.completedCount) || []

  lineInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['参与人数', '完成人数'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: monthLabels,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '参与人数',
        type: 'line',
        data: participantData,
        smooth: true,
      },
      {
        name: '完成人数',
        type: 'line',
        data: completedData,
        smooth: true,
      },
    ],
  })

  // 柱状图 - 部门完成率对比
  const barInstance = echarts.init(barChart.value)
  departmentNames.value = data.departmentCompletionRates?.map((item: any) => item.departmentName) || []
  const completionRates = data.departmentCompletionRates?.map((item: any) => item.completionRate) || []

  barInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter(params: any) {
        const dataIndex = params[0].dataIndex
        const dept = data.departmentCompletionRates[dataIndex]
        return `${dept.departmentName}<br/>完成率: ${dept.completionRate}%<br/>参与人数: ${dept.participantCount}<br/>完成人数: ${dept.completedCount}`
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      max: 100,
    },
    yAxis: {
      type: 'category',
      data: departmentNames.value,
    },
    series: [
      {
        name: '完成率',
        type: 'bar',
        data: completionRates,
        itemStyle: {
          color: '#1E88E5',
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%',
        },
      },
    ],
  })

  // 响应式调整
  window.addEventListener('resize', () => {
    pieInstance.resize()
    lineInstance.resize()
    barInstance.resize()
  })
}

// 初始化图表
function initCharts() {
  // 初始化空图表
  updateCharts(reportData.value)
}

onMounted(() => {
  nextTick(() => {
    initCharts()
    fetchReportData()
  })
})

// function goAddEdit(item: any) {
//   if (item?.id) {
//     // 编辑考核
//     router.push({
//       name: '/training/report/edit',
//       query: { id: item.id },
//     })
//   }
//   else {
//     // 新增考核
//     router.push({
//       name: '/training/report/edit',
//     })
//   }
// }
// 查看报告详情
</script>
<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="min-h-screen bg-gray-50 p-2">
    <!-- 报告概览区 -->
    <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
      <h2 class="mb-4 text-lg text-gray-800 font-semibold">
        报告概览
      </h2>

      <!-- 统计卡片组 -->
      <div class="grid grid-cols-1 mb-6 gap-4 lg:grid-cols-4 md:grid-cols-2">
        <div
          v-for="(stat, index) in stats"
          :key="index"
          class="border border-gray-100 rounded-lg bg-gray-50 p-4"
        >
          <div class="mb-1 text-sm text-gray-500">
            {{ stat.label }}
          </div>
          <div class="flex items-end justify-between">
            <div class="text-2xl text-gray-800 font-bold">
              {{ stat.value }}
            </div>
            <div class="text-sm text-gray-500">
              {{ stat.unit }}
            </div>
          </div>
          <div v-if="stat.progress" class="mt-2">
            <el-progress :percentage="stat.progress" :show-text="false" />
          </div>
        </div>
      </div>

      <!-- 统计图表区 -->
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
        <!-- 课程类型分布饼图 -->
        <div class="border border-gray-100 rounded-lg bg-gray-50 p-4">
          <h3 class="mb-2 text-sm text-gray-700 font-medium">
            课程类型分布
          </h3>
          <div ref="pieChart" class="h-64" />
        </div>

        <!-- 月度学习趋势折线图 -->
        <div class="border border-gray-100 rounded-lg bg-gray-50 p-4">
          <h3 class="mb-2 text-sm text-gray-700 font-medium">
            月度学习趋势
          </h3>
          <div ref="lineChart" class="h-64" />
        </div>

        <!-- 部门完成率对比柱状图 -->
        <div class="border border-gray-100 rounded-lg bg-gray-50 p-4 lg:col-span-2">
          <h3 class="mb-2 text-sm text-gray-700 font-medium">
            部门完成率对比
          </h3>
          <div v-show="departmentNames.length > 0" ref="barChart" class="h-64" />
          <div v-show="departmentNames.length === 0" class="text-center text-gray-500">
            暂无数据
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.el-table {
  --el-table-border-color: #f0f0f0;
  --el-table-header-bg-color: #f8f9fa;
}

.el-table :deep(.el-table__row) {
  --el-table-row-hover-bg-color: #f5f7fa;
}

.el-table :deep(.el-table__row--striped) {
  --el-table-tr-bg-color: #fafafa;
}

.el-progress {
  --el-progress-text-color: #666;
}
</style>
