<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowDown, Bell, Bottom, Check, Close,
  Delete, Edit, Link, List, Search,
  Top, User,
} from '@element-plus/icons-vue'
import planApi from '@/api/complianceApi/prevention/plan'
import trainingCurriculum from '@/api/complianceApi/prevention/trainingCurriculum'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import DepartPerson from '@/components/departPerson/index.vue'
import { disablePastDates } from '@/utils/dateUtils'

// 路由实例
const router = useRouter()
const route = useRoute()

// 获取路由参数中的id，判断是新增还是编辑
const planId = ref(route.query.id as string)
const isEdit = ref(!!planId.value)

// 页面加载状态
const pageLoading = ref(false)

// 表单数据
const formData = reactive({
  planName: '', // 计划名称
  planType: '', // 计划类型
  planStatus: 'PENDING', // 计划状态，默认草稿
  startDate: '', // 开始日期
  endDate: '', // 结束日期
  trainingTarget: [], // 培训对象
  responsiblePerson: '', // 负责人
  trainingObjective: '', // 培训目标
  planDescription: '', // 计划描述
  implementationPlan: '', // 实施方案
  resourceRequirements: '', // 资源需求
  riskManagement: '', // 风险管理
  priority: '', // 优先级
  targetCompletionRate: 80, // 目标完成率
  trainingPlanCourses: [], // 培训计划课程列表
})

// 获取培训计划详情（编辑时使用）
async function getTrainingPlanDetail() {
  if (!planId.value) { return }

  try {
    pageLoading.value = true
    const response = await planApi.getTrainingPlanDetail(planId.value)

    if (response) {
      // 回填基本信息
      Object.assign(formData, response)

      // 处理日期
      if (response.startDate) {
        formData.startDate = response.startDate.seconds
          ? new Date(response.startDate.seconds * 1000)
          : new Date(response.startDate)
      }
      if (response.endDate) {
        formData.endDate = response.endDate.seconds
          ? new Date(response.endDate.seconds * 1000)
          : new Date(response.endDate)
      }

      // 设置日期范围
      if (formData.startDate && formData.endDate) {
        dateRange.value = [formData.startDate, formData.endDate]
      }

      // 处理培训对象（字符串转数组，并转换为数字类型）
      if (response.trainingTarget) {
        console.log('原始培训对象数据:', response.trainingTarget, typeof response.trainingTarget)

        if (typeof response.trainingTarget === 'string') {
          // 将逗号分隔的字符串转换为数字数组
          formData.trainingTarget = response.trainingTarget
            .split(',')
            .map(id => Number.parseInt(id.trim(), 10))
            .filter(id => !isNaN(id)) // 过滤掉无效的ID
        }
        else if (Array.isArray(response.trainingTarget)) {
          // 如果已经是数组，确保每个元素都是数字
          formData.trainingTarget = response.trainingTarget
            .map(id => typeof id === 'string' ? Number.parseInt(id, 10) : id)
            .filter(id => !isNaN(id))
        }
        else {
          formData.trainingTarget = []
        }

        console.log('处理后的培训对象数据:', formData.trainingTarget)
      }

      // 处理课程列表
      if (response.trainingPlanCourses && Array.isArray(response.trainingPlanCourses)) {
        formData.trainingPlanCourses = response.trainingPlanCourses.map(course => ({
          id: course.id,
          courseId: course.courseId,
          courseName: course.courseName,
          courseType: course.courseType || '未分类',
          courseDurationMinutes: course.courseDurationMinutes || 0,
          courseOrder: course.courseOrder || 1,
          isRequired: course.isRequired !== false, // 默认必修
          estimatedDays: course.estimatedDays || 0,
          startDate: course.startDate ? (course.startDate.seconds ? new Date(course.startDate.seconds * 1000) : new Date(course.startDate)) : null,
          endDate: course.endDate ? (course.endDate.seconds ? new Date(course.endDate.seconds * 1000) : new Date(course.endDate)) : null,
          assessmentRequirement: course.assessmentRequirement || '',
          metadata: course.metadata || '',
          version: course.version || 0,
        }))
      }
    }
  }
  catch (error) {
    console.error('获取培训计划详情失败:', error)
    ElMessage.error('获取培训计划详情失败')
  }
  finally {
    pageLoading.value = false
  }
}

// 日期范围
const dateRange = ref([])

// 课程选择弹窗相关
const courseDialogVisible = ref(false)
const courseLoading = ref(false)
const coursePage = ref(1) // 前端分页从1开始
const coursePageSize = ref(10)
const courseTotal = ref(0)
const courseList = ref([])
const selectedCourses = ref([])
const courseSearchParams = reactive({
  courseName: '',
  courseType: '',
  status: '',
  instructor: '',
})

// 课程选择表格的多选
const courseTableRef = ref()

// 获取课程列表
async function getCourseList() {
  try {
    courseLoading.value = true
    const queryParams = {
      courseName: courseSearchParams.courseName || null,
      courseType: courseSearchParams.courseType || null,
      status: courseSearchParams.status || null,
      instructor: courseSearchParams.instructor || null,
    }

    const pagingParams = {
      page: coursePage.value - 1, // 后端分页从0开始
      size: coursePageSize.value,
    }

    const res = await trainingCurriculum.system(pagingParams, queryParams)

    if (res) {
      let courseData = []
      if (res.content && Array.isArray(res.content)) {
        courseData = res.content
        courseTotal.value = res.totalElements || res.total || 0
      }
      else if (Array.isArray(res)) {
        courseData = res
        courseTotal.value = res.length
      }
      else if (res.data) {
        if (res.data.content && Array.isArray(res.data.content)) {
          courseData = res.data.content
          courseTotal.value = res.data.totalElements || res.data.total || 0
        }
        else if (Array.isArray(res.data)) {
          courseData = res.data
          courseTotal.value = res.data.length
        }
      }

      courseList.value = courseData
    }
    else {
      courseList.value = []
      courseTotal.value = 0
    }
  }
  catch (error) {
    console.error('获取课程列表失败:', error)
    ElMessage.error('获取课程列表失败')
    courseList.value = []
    courseTotal.value = 0
  }
  finally {
    courseLoading.value = false
  }
}

// 打开课程选择弹窗
function openCourseDialog() {
  courseDialogVisible.value = true
  coursePage.value = 1
  getCourseList()
}

// 课程搜索
function searchCourses() {
  coursePage.value = 1
  getCourseList()
}

// 重置课程搜索
function resetCourseSearch() {
  courseSearchParams.courseName = ''
  courseSearchParams.courseType = ''
  courseSearchParams.status = ''
  courseSearchParams.instructor = ''
  coursePage.value = 1
  getCourseList()
}

// 课程分页变化
function handleCoursePageChange() {
  getCourseList()
}

function handleCourseSizeChange() {
  coursePage.value = 0
  getCourseList()
}

// 课程多选变化
function handleCourseSelectionChange(selection) {
  selectedCourses.value = selection
}

// 确认选择课程
function confirmSelectCourses() {
  if (selectedCourses.value.length === 0) {
    ElMessage.warning('请选择至少一门课程')
    return
  }

  // 将选中的课程添加到培训计划课程列表中，按照API文档字段结构
  const newCourses = selectedCourses.value.map((course, index) => ({
    id: null, // 新增时没有id
    courseId: course.id,
    courseName: course.courseName,
    courseType: course.courseType || '未分类',
    courseDurationMinutes: course.durationMinutes || 0,
    courseOrder: formData.trainingPlanCourses.length + index + 1, // 设置课程顺序
    isRequired: true, // 默认必修
    estimatedDays: 0,
    startDate: null,
    endDate: null,
    assessmentRequirement: '', // 考核要求
    metadata: '',
    version: 0,
  }))

  // 过滤掉已存在的课程
  const existingCourseIds = formData.trainingPlanCourses.map(c => c.courseId)
  const filteredNewCourses = newCourses.filter(course => !existingCourseIds.includes(course.courseId))

  formData.trainingPlanCourses.push(...filteredNewCourses)

  // 关闭弹窗并清空选择
  courseDialogVisible.value = false
  selectedCourses.value = []

  ElMessage.success(`成功添加 ${filteredNewCourses.length} 门课程`)
}

// 删除课程
function removeCourse(index) {
  formData.trainingPlanCourses.splice(index, 1)
  // 重新设置课程顺序
  formData.trainingPlanCourses.forEach((course, idx) => {
    course.courseOrder = idx + 1
  })
  ElMessage.success('课程删除成功')
}

// 上移课程
function moveCourseUp(index) {
  if (index > 0) {
    const temp = formData.trainingPlanCourses[index]
    formData.trainingPlanCourses[index] = formData.trainingPlanCourses[index - 1]
    formData.trainingPlanCourses[index - 1] = temp
    // 重新设置课程顺序
    formData.trainingPlanCourses.forEach((course, idx) => {
      course.courseOrder = idx + 1
    })
  }
}

// 下移课程
function moveCourseDown(index) {
  if (index < formData.trainingPlanCourses.length - 1) {
    const temp = formData.trainingPlanCourses[index]
    formData.trainingPlanCourses[index] = formData.trainingPlanCourses[index + 1]
    formData.trainingPlanCourses[index + 1] = temp
    // 重新设置课程顺序
    formData.trainingPlanCourses.forEach((course, idx) => {
      course.courseOrder = idx + 1
    })
  }
}

// 自动生成计划编号
function generatePlanCode() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  formData.planCode = `PLAN${year}${month}${day}${random}`
}

// 处理日期范围变化
function handleDateRangeChange(dates: any) {
  if (dates && dates.length === 2) {
    formData.startDate = dates[0]
    formData.endDate = dates[1]
  }
  else {
    formData.startDate = ''
    formData.endDate = ''
  }
}

// 处理负责人变化
function handleResponsiblePersonChange(employee: any) {
  if (employee) {
    console.log('选中的负责人:', employee)
    // 可以在这里保存更多员工信息，比如ID、职位等
    // formData.responsiblePersonId = employee.id
    // formData.responsiblePersonPosition = employee.position
  }
}

// 保存培训计划
async function saveTrainingPlan(isDraft = false) {
  try {
    // 基本验证
    if (!formData.planName.trim()) {
      ElMessage.error('请输入计划名称')
      return
    }
    if (!formData.planType) {
      ElMessage.error('请选择计划类型')
      return
    }
    if (!formData.startDate || !formData.endDate) {
      ElMessage.error('请选择计划周期')
      return
    }
    if (!formData.trainingTarget || formData.trainingTarget.length === 0) {
      ElMessage.error('请选择培训对象')
      return
    }
    if (!formData.responsiblePerson.trim()) {
      ElMessage.error('请输入负责人')
      return
    }
    if (!formData.trainingObjective.trim()) {
      ElMessage.error('请输入培训目标')
      return
    }

    // 构建请求数据
    const requestData = {
      ...formData,
      trainingTarget: Array.isArray(formData.trainingTarget) ? formData.trainingTarget.join(',') : formData.trainingTarget,
      // 根据是否为草稿设置不同的状态
    }

    // 如果是编辑模式，添加id
    if (isEdit.value && planId.value) {
      requestData.id = planId.value
    }

    let response
    if (isEdit.value) {
      // 编辑模式：调用更新接口
      response = await planApi.updateTrainingPlan(requestData.id, requestData)
    }
    else {
      // 新增模式：调用新增接口
      response = await planApi.createTrainingPlan(requestData)
    }

    if (response) {
      ElMessage.success(isDraft ? '保存草稿成功' : (isEdit.value ? '更新成功' : '保存成功'))
      // 返回上一个页面，与取消按钮行为一致
      router.back()
    }
  }
  catch (error) {
    console.error('保存失败:', error)
    ElMessage.error(isEdit.value ? '更新失败，请重试' : '保存失败，请重试')
  }
}

// 取消操作
function handleCancel() {
  router.back()
}

// 预览功能（暂时只是提示）
function handlePreview() {
  ElMessage.info('预览功能开发中')
}

// 页面初始化
onMounted(async () => {
  // 如果是编辑模式，获取详情数据
  if (isEdit.value) {
    await getTrainingPlanDetail()
  }

  // 这里不需要预加载课程列表，因为课程列表只在打开弹窗时才需要
  // 如果需要预加载，可以调用 getCourseList()
})
</script>

<template>
  <div v-loading="pageLoading" class="mb-10" element-loading-text="加载中...">
    <div>
      <div class="min-h-screen flex">
        <!-- 主内容区 -->
        <div class="flex">
          <!-- 内容区 -->
          <div class="flex-1 bg-gray-100 p-6">
            <page-header>
              <template #content>
                <div class="aic jcsb flex">
                  <div class="f-20 c-['#000000']">
                    <h2 class="text-xl text-gray-800 font-semibold">
                      {{ isEdit ? '编辑培训计划' : '新增培训计划' }}
                    </h2>
                  </div>
                  <div class="aic flex">
                    <el-space>
                      <el-button
                        type="primary"
                        @click="saveTrainingPlan(false)"
                      >
                        保存
                      </el-button>
                      <!-- <el-button
                        type="primary"
                        plain
                        @click="saveTrainingPlan(true)"
                      >
                        保存为草稿
                      </el-button> -->
                      <el-button
                        @click="handleCancel"
                      >
                        取消
                      </el-button>
                    </el-space>
                  </div>
                </div>
              </template>
            </page-header>
            <!-- 表单卡片 -->
            <PageMain style="background-color: transparent;">
              <el-card class="mb-6">
                <template #header>
                  <h2 class="text-lg font-bold">
                    基本信息
                  </h2>
                </template>
                <el-form :model="formData" label-width="120px" label-position="right">
                  <el-row :gutter="24">
                    <el-col :span="12">
                      <el-form-item label="计划名称" required>
                        <el-input
                          v-model="formData.planName"
                          placeholder="请输入计划名称"
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item label="计划类型" required>
                        <el-select
                          v-model="formData.planType"
                          placeholder="请选择计划类型"
                          style="width: 100%"
                        >
                          <el-option label="定期培训" value="定期培训" />
                          <el-option label="专项培训" value="专项培训" />
                          <el-option label="新员工培训" value="新员工培训" />
                          <el-option label="法规更新培训" value="法规更新培训" />
                          <el-option label="其他" value="其他" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="计划周期" required>
                        <el-date-picker
                          v-model="dateRange"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          style="width: 100%"
                          @change="handleDateRangeChange"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="培训对象" required>
                        <DepartmentTreeSelect
                          v-model="formData.trainingTarget"
                          placeholder="请选择培训对象部门"
                          :multiple="true"
                          :clearable="true"
                          width="100%"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="负责人" required>
                        <DepartPerson
                          v-model="formData.responsiblePerson"
                          placeholder="请选择负责人"
                          @change="handleResponsiblePersonChange"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="目标完成率">
                        <el-input-number
                          v-model="formData.targetCompletionRate"
                          :min="0"
                          :max="100"
                          style="width: 120px"
                        />
                        <span class="ml-2">%</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="优先级">
                        <el-select
                          v-model="formData.priority"
                          placeholder="请选择优先级"
                          style="width: 100%"
                        >
                          <el-option label="高" value="HIGH" />
                          <el-option label="中" value="MEDIUM" />
                          <el-option label="低" value="LOW" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <!-- <el-col :span="12">
                      <el-form-item label="计划状态">
                        <el-radio-group v-model="formData.planStatus">
                          <el-radio value="DRAFT">
                            草稿
                          </el-radio>
                          <el-radio value="PENDING">
                            待启动
                          </el-radio>
                          <el-radio value="IN_PROGRESS">
                            进行中
                          </el-radio>
                          <el-radio value="COMPLETED">
                            已完成
                          </el-radio>
                          <el-radio value="CANCELLED">
                            已取消
                          </el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col> -->
                  </el-row>
                </el-form>
              </el-card>
              <!-- 课程设置 -->
              <el-card class="mb-6">
                <template #header>
                  <div class="flex items-center justify-between">
                    <h2 class="text-lg font-bold">
                      课程设置
                    </h2>
                    <el-button type="primary" @click="openCourseDialog">
                      添加课程
                    </el-button>
                  </div>
                </template>
                <el-table :data="formData.trainingPlanCourses" class="mb-4">
                  <el-table-column prop="courseName" label="课程名称" width="180" />
                  <el-table-column prop="courseType" label="课程类型" width="120" />
                  <el-table-column label="课程时长" width="100">
                    <template #default="{ row }">
                      {{ row.courseDurationMinutes }}分钟
                    </template>
                  </el-table-column>
                  <el-table-column prop="isRequired" label="必修标识" width="80">
                    <template #default="{ row }">
                      <el-switch v-model="row.isRequired" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="startDate" label="开始日期" width="150">
                    <template #default="{ row }">
                      <el-date-picker
                        v-model="row.startDate"
                        type="date"
                        placeholder="选择开始日期"
                        :disabled-date="disablePastDates"
                        size="small"
                        style="width: 100%"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="endDate" label="截止日期" width="150">
                    <template #default="{ row }">
                      <el-date-picker
                        v-model="row.endDate"
                        type="date"
                        :disabled-date="disablePastDates"
                        placeholder="选择截止日期"
                        size="small"
                        style="width: 100%"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="assessmentRequirement" label="考核要求" min-width="150">
                    <template #default="{ row }">
                      <el-input
                        v-model="row.assessmentRequirement"
                        placeholder="请输入考核要求"
                        size="small"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="180">
                    <template #default="{ row, $index }">
                      <el-space>
                        <el-button
                          type="primary"
                          :icon="Top"
                          circle
                          size="small"
                          :disabled="$index === 0"
                          @click="moveCourseUp($index)"
                        />
                        <el-button
                          type="primary"
                          :icon="Bottom"
                          circle
                          size="small"
                          :disabled="$index === formData.trainingPlanCourses.length - 1"
                          @click="moveCourseDown($index)"
                        />
                        <el-button
                          type="danger"
                          :icon="Delete"
                          circle
                          size="small"
                          @click="removeCourse($index)"
                        />
                      </el-space>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
              <!-- 计划概述 -->
              <el-card class="mb-6">
                <template #header>
                  <h2 class="text-lg font-bold">
                    计划概述
                  </h2>
                </template>
                <el-form :model="formData" label-width="120px" label-position="top">
                  <el-row :gutter="24">
                    <el-col :span="24">
                      <el-form-item label="培训目标" required>
                        <el-input
                          v-model="formData.trainingObjective"
                          type="textarea"
                          :rows="3"
                          placeholder="请输入培训目标"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="计划描述">
                        <el-input
                          v-model="formData.planDescription"
                          type="textarea"
                          :rows="3"
                          placeholder="请输入计划描述"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="实施方案">
                        <el-input
                          v-model="formData.implementationPlan"
                          type="textarea"
                          :rows="5"
                          placeholder="请输入实施方案"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="资源需求">
                        <el-input
                          v-model="formData.resourceRequirements"
                          type="textarea"
                          :rows="3"
                          placeholder="请输入资源需求"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="风险管理">
                        <el-input
                          v-model="formData.riskManagement"
                          type="textarea"
                          :rows="3"
                          placeholder="请输入风险管理措施"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-card>
            </PageMain>
          </div>
        </div>
      </div>
      <!-- 课程选择弹窗 -->
      <el-dialog
        v-model="courseDialogVisible"
        title="选择课程"
        width="80%"
        :close-on-click-modal="false"
      >
        <!-- 搜索区域 -->
        <div class="mb-4">
          <el-form :inline="true" :model="courseSearchParams">
            <el-form-item label="课程名称">
              <el-input
                v-model="courseSearchParams.courseName"
                placeholder="请输入课程名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="课程类型">
              <el-select
                v-model="courseSearchParams.courseType"
                placeholder="请选择课程类型"
                clearable
                style="width: 150px"
              >
                <el-option label="法律法规" value="法律法规" />
                <el-option label="合规知识" value="合规知识" />
                <el-option label="案例分析" value="案例分析" />
                <el-option label="技能培训" value="技能培训" />
              </el-select>
            </el-form-item>
            <el-form-item label="讲师">
              <el-input
                v-model="courseSearchParams.instructor"
                placeholder="请输入讲师姓名"
                clearable
                style="width: 150px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchCourses">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="resetCourseSearch">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 课程列表表格 -->
        <el-table
          ref="courseTableRef"
          v-loading="courseLoading"
          :data="courseList"
          @selection-change="handleCourseSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="courseName" label="课程名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="instructor" label="讲师" min-width="120" />
          <el-table-column prop="durationMinutes" label="课程时长" width="120">
            <template #default="{ row }">
              {{ row.durationMinutes || 0 }}分钟
            </template>
          </el-table-column>
          <el-table-column prop="courseType" label="课程类型" width="120" />
          <el-table-column prop="instructor" label="讲师" width="120" />
        </el-table>

        <!-- 分页 -->
        <div class="mt-4 flex justify-end">
          <el-pagination
            v-model:current-page="coursePage"
            v-model:page-size="coursePageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="courseTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handleCoursePageChange"
            @size-change="handleCourseSizeChange"
          />
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="courseDialogVisible = false">
              取消
            </el-button>
            <el-button type="primary" @click="confirmSelectCourses">
              确定选择 ({{ selectedCourses.length }})
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

  <style lang="scss" scoped>
  @use "@/styles/toolsCss";
  /* 自定义样式 */
  :deep(.el-table) {
  --el-table-border-color: #e0e0e0;
  --el-table-header-bg-color: #f5f7fa;
  }
  :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #e0e0e0 inset;
  }
  :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
  }
  :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #1E88E5 inset;
  }
  :deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #1E88E5;
  border-color: #1E88E5;
  }
  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #1E88E5;
  border-color: #1E88E5;
  }
  :deep(.el-switch.is-checked .el-switch__core) {
  background-color: #1E88E5;
  border-color: #1E88E5;
  }
  </style>
