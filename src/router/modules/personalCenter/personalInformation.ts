import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/personalCenter/personalInformation',
  component: Layout,
  // redirect: '/one/1',
  name: '/personalCenter/personalInformation',
  meta: {
    title: '个人信息',
    icon: 'i-heroicons-solid:menu-alt-3',
    // sidebar: false,
  },
  children: [
    {
      path: '/personalCenter/personalInformation/basicInformation',
      name: '/personalCenter/personalInformation/basicInformation',
      component: () => import('@/views/hegui/personalCenter/personalInformation/basicInformation/index.vue'),
      meta: {
        title: '基本资料',
        // sidebar: false,
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/personalCenter/personalInformation/basicInformation/detail',
          name: '/personalCenter/personalInformation/basicInformation/detail',
          component: () => import('@/views/hegui/personalCenter/personalInformation/basicInformation/detail.vue'),
          meta: {
            title: '基本资料详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/personalCenter/personalInformation/passwordModification',
      name: '/personalCenter/personalInformation/passwordModification',
      component: () => import('@/views/hegui/personalCenter/personalInformation/passwordModification/index.vue'),
      meta: {
        title: '密码修改',
      },
    },
    {
      path: '/personalCenter/personalInformation/securitySetting',
      name: '/personalCenter/personalInformation/securitySetting',
      // redirect: '/one/3',
      component: () => import('@/views/hegui/personalCenter/personalInformation/securitySetting/index.vue'),
      meta: {
        title: '安全设置',
      },
      // children: [
      //   {
      //     path: '/personalCenter/personalInformation/3/detail',
      //     name: '/personalCenter/personalInformation/3/detail',
      //     component: () => import('@/views/hegui/personalCenter/personalInformation/earlyWarning/detail.vue'),
      //     meta: {
      //       title: '实时监控详情',
      //       sidebar: false,
      //       breadcrumb: false,
      //     },
      //   },
      // ]
    },
  ],
}

export default routes
