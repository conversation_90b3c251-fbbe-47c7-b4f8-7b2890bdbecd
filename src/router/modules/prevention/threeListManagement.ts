import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/threeListManagement',
  component: Layout,
  // redirect: '/one/1',
  name: '/threeListManagement',
  meta: {
    title: '三张清单管理',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/threeListManagement/complianceRisk',
      name: '/threeListManagement/complianceRisk',
      component: () => import('@/views/hegui/prevention/threeListManagement/complianceRisk.vue'),
      meta: {
        title: '合规风险识别清单',
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/threeListManagement/complianceRisk/edit',
          name: '/threeListManagement/complianceRisk/edit',
          component: () => import('@/views/hegui/prevention/threeListManagement/complianceRisk_edit.vue'),
          meta: {
            title: '新增合规清单',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/threeListManagement/complianceRisk/detail',
          name: '/threeListManagement/complianceRisk/detail',
          component: () => import('@/views/hegui/prevention/threeListManagement/complianceRisk_detail.vue'),
          meta: {
            title: '合规清单详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/threeListManagement/jobSpecifications',
      name: 'threeListManagement/jobSpecifications',
      component: () => import('@/views/hegui/prevention/threeListManagement/jobSpecifications.vue'),
      meta: {
        title: '重点岗位合规职责清单',
      },
      children: [
        {
          path: '/threeListManagement/jobSpecifications/edit',
          name: '/threeListManagement/jobSpecifications/edit',
          component: () => import('@/views/hegui/prevention/threeListManagement/jobSpecifications_edit.vue'),
          meta: {
            title: '新增重点岗位合规职责清单',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/threeListManagement/jobSpecifications/detail',
          name: '/threeListManagement/jobSpecifications/detail',
          component: () => import('@/views/hegui/prevention/threeListManagement/jobSpecifications_detail.vue'),
          meta: {
            title: '重点岗位合规职责清单详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/threeListManagement/operationFlow',
      name: 'threeListManagement/operationFlow',
      // redirect: '/one/3',
      component: () => import('@/views/hegui/prevention/threeListManagement/operationFlow.vue'),
      meta: {
        title: '关键业务流程管控清单',
      },
      children: [
        {
          path: '/threeListManagement/operationFlow/detail',
          name: '/threeListManagement/operationFlow/detail',
          component: () => import('@/views/hegui/prevention/threeListManagement/operationFlow_detail.vue'),
          meta: {
            title: '关键业务流程管控清单详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/threeListManagement/operationFlow/edit',
          name: '/threeListManagement/operationFlow/edit',
          component: () => import('@/views/hegui/prevention/threeListManagement/operationFlow_edit.vue'),
          meta: {
            title: '新增关键业务流程管控清单/',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
  ],
}

export default routes
