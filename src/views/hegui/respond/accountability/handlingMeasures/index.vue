<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ArrowDown as ElIconArrowDown,
  <PERSON>Up as ElIconArrowUp,
  Check as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Document as ElIconDocument,
  Money as <PERSON><PERSON><PERSON>M<PERSON>,
  Plus as ElIconPlus,
  Search as ElIconSearch,
  Setting as ElIconSetting,
  // Time as ElIconTime,
  User as ElIconUser,
  Warning as ElIconWarning,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import problemTaskApi from '@/api/problemTask/handlingMeasures'

const router = useRouter()

// 接口数据类型定义
interface ResponsibilityInvestigateDealDTO {
  id: number
  tenantId: number
  title: string
  dealCode: string
  dealType: any
  level: string
  investigateId: number
  dutyEmployeeId: number
  dutyEmployeeOrgId: number
  dealEmployeeId: number
  startDate: string
  finishDate: string
  status: string
  violationDesc: string
  violationImpact: string
  responsibility: string
  metadata: string
  version: number
  createdBy: string
  createdAt: {
    seconds: number
    nanos: number
    epochSecond: number
    nano: number
  }
  updatedBy: string
  updatedAt: {
    seconds: number
    nanos: number
    epochSecond: number
    nano: number
  }
  isDeleted: boolean
  measureList: any[]
}

interface _PageResponse {
  content: ResponsibilityInvestigateDealDTO[]
  pageable: any
  total: number
  empty: boolean
  number: number
  size: number
  numberOfElements: number
  sort: any[]
  first: boolean
  last: boolean
  totalPages: number
  totalElements: number
}

// 数据管理
const loading = ref(false)
const tableData = ref<ResponsibilityInvestigateDealDTO[]>([])
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
})

// 筛选参数
const searchParams = ref({
  title: null,
  dealCode: null,
  dealType: null,
  level: null,
  investigateId: null,
  dutyEmployeeId: null,
  dutyEmployeeOrgId: null,
  dealEmployeeId: null,
  startDate: null,
  finishDate: null,
  status: null,
  createdAtStart: null,
  createdAtEnd: null,
})

// 日期范围
const dateRange = ref<[Date, Date] | []>([])

// 处理日期变化
function handleDateChange(dates: [Date, Date] | []) {
  if (dates && dates.length === 2) {
    searchParams.value.startDate = dates[0].toISOString().split('T')[0]
    searchParams.value.finishDate = dates[1].toISOString().split('T')[0]
  }
  else {
    searchParams.value.startDate = ''
    searchParams.value.finishDate = ''
  }
  handleSearch()
}

// 获取列表数据
async function getHandlingMeasuresList() {
  try {
    loading.value = true
    const params = {
      ...searchParams.value,
      page: pagination.value.page - 1, // 后端页码从0开始
      size: pagination.value.size,
    }
    const response = await problemTaskApi.getHandlingMeasuresList(params)
    if (response && response.content) {
      tableData.value = response.content
      pagination.value.total = response.totalElements || 0
      // 更新统计数据
      updateStatistics(response.content)
    }
  }
  catch (error) {
    console.error('获取处理措施列表失败:', error)
    ElMessage.error('获取数据失败，请稍后重试')
  }
  finally {
    loading.value = false
  }
}

// 更新统计数据
function updateStatistics(_data: ResponsibilityInvestigateDealDTO[]) {
  // 这里可以根据实际数据更新页面顶部的统计卡片
  // TODO: 实现统计数据更新逻辑
}

// 处理分页变化
function handlePageChange(page: number) {
  pagination.value.page = page
  getHandlingMeasuresList()
}

// 处理每页大小变化
function handleSizeChange(size: number) {
  pagination.value.size = size
  pagination.value.page = 1
  getHandlingMeasuresList()
}

// 搜索处理
function handleSearch() {
  pagination.value.page = 1
  getHandlingMeasuresList()
}

// 重置搜索
function handleReset() {
  searchParams.value = {
    tenantId: 0,
    title: null,
    dealCode: null,
    dealType: null,
    level: null,
    investigateId: null,
    dutyEmployeeId: null,
    dutyEmployeeOrgId: null,
    dealEmployeeId: null,
    startDate: null,
    finishDate: null,
    status: null,
    createdAtStart: null,
    createdAtEnd: null,
  }
  dateRange.value = []
  pagination.value.page = 1
  getHandlingMeasuresList()
}

// 格式化日期
function _formatDate(dateObj: any): string {
  if (!dateObj || !dateObj.seconds) {
    return ''
  }
  return new Date(dateObj.seconds * 1000).toLocaleDateString('zh-CN')
}

// 获取状态标签类型
function getStatusTagType(status: string): string {
  const statusMap: Record<string, string> = {
    NO_START: 'info',
    PROGRESSING: 'warning',
    FINISHED: 'success',
    PAUSED: 'warning',
    CANCELED: 'danger',
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    NO_START: '待处理',
    PROGRESSING: '处理中',
    FINISHED: '已完成',
    PAUSED: '已暂停',
    CANCELED: '已取消',
  }
  return statusMap[status] || status
}

// 获取处理类型文本
function getDealTypeText(dealType: string): string {
  const typeMap: Record<string, string> = {
    FINANCIAL_VIOLATION: '财务违规',
    INFORMATION_SECURITY: '信息安全',
    DISCIPLINARY_ACTION: '纪律处分',
  }
  return typeMap[dealType] || dealType
}

// 获取级别标签类型
function getLevelTagType(level: string): string {
  const levelMap: Record<string, string> = {
    LOW: 'success',
    MIDDLE: 'warning',
    HIGH: 'danger',
  }
  return levelMap[level] || 'info'
}

// 获取级别文本
function getLevelText(level: string): string {
  const levelMap: Record<string, string> = {
    LOW: '低',
    MIDDLE: '中',
    HIGH: '高',
  }
  return levelMap[level] || level
}

// 处理表格选择变化
function handleSelectionChange(_selection: ResponsibilityInvestigateDealDTO[]) {
  // TODO: 处理选中的数据
}

// 处理查看操作
function handleView(_row: ResponsibilityInvestigateDealDTO) {
  router.push({
    path: '/respond/accountability/handlingMeasures/detail',
    query: {
      id: _row.id,
    },
  })
  // TODO: 实现查看逻辑
}

// 处理编辑操作
function handleEdit(_row: ResponsibilityInvestigateDealDTO) {
  // TODO: 实现编辑逻辑
  router.push({
    path: '/respond/accountability/handlingMeasures/addEdit',
    query: {
      id: _row.id,
    },
  })
}

function handleAdd() {
  router.push({
    path: '/respond/accountability/handlingMeasures/addEdit',
  })
}

// 组件挂载时获取数据
onMounted(() => {
  getHandlingMeasuresList()
})

const showAdvancedFilter = ref(false)
const filterTypes = ref({
  warning: false,
  training: false,
  salaryReduction: false,
  demotion: false,
  dismissal: false,
  fine: false,
  other: false,
})

const templates = ref([
  {
    id: 1,
    name: '轻微违规警告模板',
    scenario: '适用于首次轻微违规行为',
  },
  {
    id: 2,
    name: '财务违规处理模板',
    scenario: '适用于报销、费用违规情况',
  },
  {
    id: 3,
    name: '数据安全违规模板',
    scenario: '适用于数据泄露、违规访问等情况',
  },
])

const recommendations = ref([
  {
    id: 1,
    title: '数据泄露事件',
    reason: '高严重程度，截止日期临近',
  },
  {
    id: 2,
    title: '财务报销违规',
    reason: '涉及金额较大，影响范围广',
  },
  {
    id: 3,
    title: '员工行为不当',
    reason: '多次违规，需要及时处理',
  },
])

const aiSuggestion = ref('')

function toggleAllTypes() {
  const allSelected = Object.values(filterTypes.value).every(Boolean)
  for (const key in filterTypes.value) {
    filterTypes.value[key as keyof typeof filterTypes.value] = !allSelected
  }
}
const _viewType = ref('table')
const _cardItems = ref([
  {
    id: 1,
    title: '违规使用公司资源',
    type: 'warning',
    typeLabel: '警告',
    severity: 'minor',
    severityLabel: '轻微',
    responsible: '李四/技术部',
    progress: 0,
    daysLeft: 10,
    dueDate: '2023-12-15',
    status: 'pending',
  },
  {
    id: 2,
    title: '财务报销违规',
    type: 'fine',
    typeLabel: '经济处罚',
    severity: 'medium',
    severityLabel: '中等',
    responsible: '王五/财务部',
    progress: 45,
    daysLeft: 5,
    dueDate: '2023-12-10',
    status: 'processing',
  },
  {
    id: 3,
    title: '数据泄露事件',
    type: 'dismissal',
    typeLabel: '解聘',
    severity: 'serious',
    severityLabel: '严重',
    responsible: '赵六/技术部',
    progress: 100,
    daysLeft: -5,
    dueDate: '2023-11-20',
    status: 'overdue',
  },
  {
    id: 4,
    title: '员工行为不当',
    type: 'training',
    typeLabel: '培训教育',
    severity: 'minor',
    severityLabel: '轻微',
    responsible: '张三/人力资源部',
    progress: 100,
    daysLeft: 0,
    dueDate: '2023-12-05',
    status: 'completed',
  },
])
const _ganttItems = ref([
  {
    id: 1,
    title: '违规使用公司资源',
    responsible: '李四',
    startDate: '2023-11-15',
    endDate: '2023-12-15',
    startPos: 100,
    duration: 300,
    progress: 0,
    status: 'pending',
  },
  {
    id: 2,
    title: '财务报销违规',
    responsible: '王五',
    startDate: '2023-11-10',
    endDate: '2023-12-10',
    startPos: 50,
    duration: 350,
    progress: 45,
    status: 'processing',
  },
  {
    id: 3,
    title: '数据泄露事件',
    responsible: '赵六',
    startDate: '2023-10-20',
    endDate: '2023-11-20',
    startPos: 0,
    duration: 250,
    progress: 100,
    status: 'overdue',
  },
  {
    id: 4,
    title: '员工行为不当',
    responsible: '张三',
    startDate: '2023-11-05',
    endDate: '2023-12-05',
    startPos: 80,
    duration: 320,
    progress: 100,
    status: 'completed',
  },
])
const ganttDays = ref<number[]>([])
// Generate gantt days for demo
for (let i = 1; i <= 30; i++) {
  ganttDays.value.push(i)
}
const activeTab = ref('type')
const _chartOptions = {
  type: {
    legend: {
      data: ['警告', '培训教育', '经济处罚', '降级', '解聘', '其他'],
    },
    series: [
      {
        name: '处理类型分布',
        type: 'pie',
        radius: '70%',
        data: [
          { value: 356, name: '警告' },
          { value: 289, name: '培训教育' },
          { value: 215, name: '经济处罚' },
          { value: 102, name: '降级' },
          { value: 48, name: '解聘' },
          { value: 235, name: '其他' },
        ],
      },
    ],
  },
  trend: {
    xAxis: {
      type: 'category',
      data: ['第1周', '第2周', '第3周', '第4周', '第5周'],
    },
    yAxis: {
      type: 'value',
      name: '措施数量',
    },
    series: [
      {
        name: '待处理',
        type: 'line',
        data: [120, 132, 101, 134, 90],
      },
      {
        name: '处理中',
        type: 'line',
        data: [220, 182, 191, 234, 290],
      },
      {
        name: '已完成',
        type: 'line',
        data: [150, 232, 201, 154, 190],
      },
      {
        name: '已逾期',
        type: 'line',
        data: [20, 32, 41, 34, 10],
      },
    ],
  },
  department: {
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: ['人力资源部', '财务部', '技术部', '市场部'],
    },
    series: [
      {
        name: '待处理',
        type: 'bar',
        stack: 'total',
        data: [120, 132, 101, 134],
      },
      {
        name: '处理中',
        type: 'bar',
        stack: 'total',
        data: [220, 182, 191, 234],
      },
      {
        name: '已完成',
        type: 'bar',
        stack: 'total',
        data: [150, 232, 201, 154],
      },
      {
        name: '已逾期',
        type: 'bar',
        stack: 'total',
        data: [20, 32, 41, 34],
      },
    ],
  },
  timeliness: {
    xAxis: {
      type: 'value',
      max: 100,
    },
    yAxis: {
      type: 'category',
      data: ['人力资源部', '财务部', '技术部', '市场部', '总体'],
    },
    series: [
      {
        name: '目标及时率',
        type: 'bar',
        data: [90, 90, 90, 90, 90],
      },
      {
        name: '实际及时率',
        type: 'bar',
        data: [85, 78, 92, 88, 86],
      },
    ],
  },
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              处理措施管理
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'handlingMeasures/index/add'" type="primary" @click="handleAdd">
              <!-- <el-icon class="mr-1">
                <ElIconPlus />
              </el-icon> -->
              新增处理措施
            </el-button>
            <el-button
              v-auth="'accountability:handlingMeasures:import'"
              type="default"
            >
              批量导入
            </el-button>
            <el-button
              v-auth="'accountability:handlingMeasures:export'"
              type="default"
            >
              导出
            </el-button>
            <el-button
              v-auth="'accountability:handlingMeasures:analysis'"
              type="default"
            >
              统计分析
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <div v-if="false" class="flex">
          <el-card shadow="hover" class="mr-10 flex-1">
            <div class="flex justify-between">
              <div>
                <div class="text-sm text-gray-500">
                  总处理数
                </div>
                <div class="mt-1 text-2xl font-bold">
                  1,245
                </div>
              </div>
              <div class="h-10 w-10 flex items-center justify-center rounded-full bg-blue-100">
                <el-icon class="text-blue-600">
                  <ElIconDocument />
                </el-icon>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="ml-10 mr-10 flex-1">
            <div class="flex justify-between">
              <div>
                <div class="text-sm text-gray-500">
                  待处理数
                </div>
                <div class="mt-1 text-2xl font-bold">
                  86
                </div>
                <div class="mt-1 text-xs text-green-500">
                  ↑12%
                </div>
              </div>
              <div class="h-10 w-10 flex items-center justify-center rounded-full bg-orange-100">
                <el-icon class="text-orange-600">
                  <el-icon-time />
                </el-icon>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="ml-10 mr-10 flex-1">
            <div class="flex justify-between">
              <div>
                <div class="text-sm text-gray-500">
                  处理中数
                </div>
                <div class="mt-1 text-2xl font-bold">
                  342
                </div>
                <div class="mt-1 text-xs text-green-500">
                  ↑5%
                </div>
              </div>
              <div class="h-10 w-10 flex items-center justify-center rounded-full bg-purple-100">
                <el-icon class="text-purple-600">
                  <ElIconSetting />
                </el-icon>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="ml-10 mr-10 flex-1">
            <div class="flex justify-between">
              <div>
                <div class="text-sm text-gray-500">
                  已完成数
                </div>
                <div class="mt-1 text-2xl font-bold">
                  817
                </div>
                <div class="mt-1 text-xs text-red-500">
                  ↓3%
                </div>
              </div>
              <div class="h-10 w-10 flex items-center justify-center rounded-full bg-green-100">
                <el-icon class="text-green-600">
                  <ElIconCheck />
                </el-icon>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="ml-10 flex-1">
            <div class="flex justify-between">
              <div>
                <div class="text-sm text-gray-500">
                  已逾期数
                </div>
                <div class="mt-1 text-2xl text-red-600 font-bold">
                  42
                </div>
              </div>
              <div class="h-10 w-10 flex items-center justify-center rounded-full bg-red-100">
                <el-icon class="text-red-600">
                  <ElIconWarning />
                </el-icon>
              </div>
            </div>
          </el-card>
        </div>
        <div class="mt-10">
          <el-row :gutter="20" class="">
            <el-col :span="24">
              <div v-if="false">
                <el-row :gutter="20" class="">
                  <el-col :span="12">
                    <el-card shadow="hover" class="">
                      <template #header>
                        <div class="aic jcsb flex">
                          <div class="f-16 fw-600">
                            最近访问
                          </div>
                          <el-link type="primary">
                            查看更多
                          </el-link>
                        </div>
                      </template>
                      <div class="space-y-3">
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-blue-100">
                              <el-icon class="text-blue-600">
                                <ElIconWarning />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                违规使用公司资源
                              </div>
                              <div class="text-xs text-gray-500">
                                警告
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-gray-100 px-2 py-1 text-xs">待处理</span>
                        </div>
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-orange-100">
                              <el-icon class="text-orange-600">
                                <ElIconMoney />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                财务报销违规
                              </div>
                              <div class="text-xs text-gray-500">
                                经济处罚
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-600">处理中</span>
                        </div>
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-green-100">
                              <el-icon class="text-green-600">
                                <ElIconUser />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                员工行为不当
                              </div>
                              <div class="text-xs text-gray-500">
                                培训教育
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-green-100 px-2 py-1 text-xs text-green-600">已完成</span>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="12">
                    <el-card shadow="hover" class="">
                      <template #header>
                        <div class="aic jcsb flex">
                          <div class="f-16 fw-600">
                            我负责的
                          </div>
                          <el-link type="primary">
                            查看更多
                          </el-link>
                        </div>
                      </template>
                      <div class="space-y-3">
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-red-100">
                              <el-icon class="text-red-600">
                                <ElIconWarning />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                数据泄露事件
                              </div>
                              <div class="text-xs text-gray-500">
                                截止: 2023-12-15
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-red-100 px-2 py-1 text-xs text-red-600">高优先级</span>
                        </div>
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-yellow-100">
                              <el-icon class="text-yellow-600">
                                <ElIconDocument />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                合同审批延迟
                              </div>
                              <div class="text-xs text-gray-500">
                                截止: 2023-12-20
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-600">中优先级</span>
                        </div>
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-blue-100">
                              <el-icon class="text-blue-600">
                                <ElIconSetting />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                系统权限问题
                              </div>
                              <div class="text-xs text-gray-500">
                                截止: 2023-12-25
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-600">低优先级</span>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
              <el-card shadow="hover" class="mt-10">
                <!-- 筛选区 -->
                <div class="rounded-lg bg-white p-6 shadow">
                  <!-- 基础筛选行 -->
                  <div class="mb-4 flex flex-wrap items-center gap-4">
                    <div class="w-48">
                      <el-select v-model="searchParams.status" placeholder="处理状态" class="w-full" clearable>
                        <el-option label="待处理" value="NO_START" />
                        <el-option label="处理中" value="PROGRESSING" />
                        <el-option label="已完成" value="FINISHED" />
                        <el-option label="已暂停" value="PAUSED" />
                        <el-option label="已取消" value="CANCELED" />
                      </el-select>
                    </div>
                    <div class="w-48">
                      <el-select v-model="searchParams.dealType" placeholder="处理类型" class="w-full" clearable>
                        <el-option label="财务违规" value="FINANCIAL_VIOLATION" />
                        <el-option label="信息安全" value="INFORMATION_SECURITY" />
                        <el-option label="纪律处分" value="DISCIPLINARY_ACTION" />
                      </el-select>
                    </div>
                    <div class="w-48">
                      <el-select v-model="searchParams.level" placeholder="优先级" class="w-full" clearable>
                        <el-option label="低" value="LOW" />
                        <el-option label="中" value="MIDDLE" />
                        <el-option label="高" value="HIGH" />
                      </el-select>
                    </div>
                    <div class="w-80">
                      <el-date-picker
                        v-model="dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        class="w-full"
                        clearable
                      />
                    </div>
                  </div>

                  <!-- 搜索和操作行 -->
                  <div class="flex items-center gap-4">
                    <div class="max-w-md flex-1">
                      <el-input
                        v-model="searchParams.title"
                        placeholder="搜索处理措施标题"
                        clearable
                        @keyup.enter="handleSearch"
                        @clear="handleSearch"
                      >
                        <template #prefix>
                          <el-icon>
                            <ElIconSearch />
                          </el-icon>
                        </template>
                      </el-input>
                    </div>
                    <div class="flex items-center gap-2">
                      <el-button type="primary" @click="handleSearch">
                        查询
                      </el-button>
                      <el-button @click="handleReset">
                        重置
                      </el-button>
                      <!-- <el-button
                        type="text"
                        @click="showAdvancedFilter = !showAdvancedFilter"
                      >
                        {{ showAdvancedFilter ? '收起' : '高级筛选' }}
                        <el-icon class="ml-1">
                          <ElIconArrowDown v-if="!showAdvancedFilter" />
                          <ElIconArrowUp v-else />
                        </el-icon>
                      </el-button> -->
                    </div>
                  </div>

                  <!-- 高级筛选区域 -->
                  <el-collapse-transition>
                    <div v-show="showAdvancedFilter" class="mt-4 border-t border-gray-200 pt-4">
                      <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
                        <div>
                          <label class="mb-2 block text-sm text-gray-700 font-medium">责任人</label>
                          <el-input v-model="searchParams.dutyEmployeeId" placeholder="请输入责任人" clearable />
                        </div>
                        <div>
                          <label class="mb-2 block text-sm text-gray-700 font-medium">处理编号</label>
                          <el-input v-model="searchParams.dealCode" placeholder="请输入处理编号" clearable />
                        </div>
                        <div>
                          <label class="mb-2 block text-sm text-gray-700 font-medium">创建时间</label>
                          <el-date-picker
                            v-model="searchParams.createdAtStart"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="创建开始日期"
                            end-placeholder="创建结束日期"
                            class="w-full"
                            clearable
                          />
                        </div>
                      </div>
                    </div>
                  </el-collapse-transition>
                </div>
                <!-- 处理措施列表 -->
                <el-card shadow="hover">
                  <el-table
                    v-loading="loading"
                    :data="tableData"
                    stripe
                    border
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                  >
                    <el-table-column type="selection" width="55" />

                    <el-table-column prop="dealCode" label="处理编号" width="120" />

                    <el-table-column prop="title" label="处理标题" min-width="200" show-overflow-tooltip />

                    <el-table-column prop="dealType" label="处理类型" width="120">
                      <template #default="{ row }">
                        {{ getDealTypeText(row.dealType) }}
                      </template>
                    </el-table-column>

                    <el-table-column prop="level" label="严重程度" width="100">
                      <template #default="{ row }">
                        <el-tag :type="getLevelTagType(row.level) as any" size="small">
                          {{ getLevelText(row.level) }}
                        </el-tag>
                      </template>
                    </el-table-column>

                    <el-table-column prop="dutyEmployeeId" label="责任人/部门" width="120">
                      <template #default="{ row }">
                        {{ `${row.dutyEmployeeName}/${row.dutyEmployeeOrgName}` || '-' }}
                      </template>
                    </el-table-column>

                    <el-table-column prop="status" label="处理状态" width="100">
                      <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status) as any" size="small">
                          {{ getStatusText(row.status) }}
                        </el-tag>
                      </template>
                    </el-table-column>

                    <el-table-column prop="startDate" label="提出日期" width="120">
                      <template #default="{ row }">
                        {{ _formatDate(row.startDate) }}
                      </template>
                    </el-table-column>

                    <el-table-column prop="finishDate" label="计划完成日期" width="120">
                      <template #default="{ row }">
                        {{ _formatDate(row.finishDate) }}
                      </template>
                    </el-table-column>

                    <el-table-column label="操作" width="160" fixed="right">
                      <template #default="{ row }">
                        <el-button
                          v-auth="'handlingMeasures/index/view'"
                          type="primary" plain
                          class="!ml-0"
                          size="small"
                          @click="handleView(row)"
                        >
                          查看
                        </el-button>
                        <el-button
                          v-auth="'handlingMeasures/index/edit'"
                          type="warning" plain
                          size="small"
                          @click="handleEdit(row)"
                        >
                          编辑
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>

                  <!-- Element Plus 分页 -->
                  <div class="mt-4 flex justify-end">
                    <el-pagination
                      v-model:current-page="pagination.page"
                      v-model:page-size="pagination.size"
                      :page-sizes="[10, 20, 50, 100]"
                      :total="pagination.total"
                      layout="total, sizes, prev, pager, next, jumper"
                      @size-change="handleSizeChange"
                      @current-change="handlePageChange"
                    />
                  </div>
                </el-card>
              </el-card>
              <el-card v-if="false" shadow="hover" class="mt-20">
                <template #header>
                  <div class="aic jcsb flex">
                    <div class="f-16 fw-600">
                      处理措施分析
                    </div>
                    <!-- <el-link type="primary">查看更多</el-link> -->
                  </div>
                </template>
                <div class="border-b">
                  <div class="flex space-x-4">
                    <button
                      class="px-4 py-2 text-sm"
                      :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'type', 'text-gray-600 hover:text-blue-600': activeTab !== 'type' }"
                      @click="activeTab = 'type'"
                    >
                      类型分布
                    </button>
                    <button
                      class="px-4 py-2 text-sm"
                      :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'trend', 'text-gray-600 hover:text-blue-600': activeTab !== 'trend' }"
                      @click="activeTab = 'trend'"
                    >
                      状态趋势
                    </button>
                    <button
                      class="px-4 py-2 text-sm"
                      :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'department', 'text-gray-600 hover:text-blue-600': activeTab !== 'department' }"
                      @click="activeTab = 'department'"
                    >
                      部门分布
                    </button>
                    <button
                      class="px-4 py-2 text-sm"
                      :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'timeliness', 'text-gray-600 hover:text-blue-600': activeTab !== 'timeliness' }"
                      @click="activeTab = 'timeliness'"
                    >
                      及时率分析
                    </button>
                  </div>
                </div>
                <!-- 类型分布 -->
                <div v-if="activeTab === 'type'" class="grid grid-cols-3 mt-4 gap-4">
                  <div class="col-span-2">
                    <div class="h-64 flex items-center justify-center rounded bg-gray-100">
                      <div class="text-center">
                        <div class="mb-2 text-gray-500">
                          饼图展示不同类型处理措施的分布
                        </div>
                        <div class="flex justify-center text-xs space-x-4">
                          <div class="flex items-center">
                            <div class="mr-1 h-3 w-3 bg-blue-500" />
                            <span>警告 28.6%</span>
                          </div>
                          <div class="flex items-center">
                            <div class="mr-1 h-3 w-3 bg-green-500" />
                            <span>培训 23.2%</span>
                          </div>
                          <div class="flex items-center">
                            <div class="mr-1 h-3 w-3 bg-yellow-500" />
                            <span>经济处罚 17.3%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th class="px-4 py-2 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            处理类型
                          </th>
                          <th class="px-4 py-2 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            数量
                          </th>
                          <th class="px-4 py-2 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            占比
                          </th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            警告
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            356
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            28.6%
                          </td>
                        </tr>
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            培训教育
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            289
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            23.2%
                          </td>
                        </tr>
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            经济处罚
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            215
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            17.3%
                          </td>
                        </tr>
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            降级
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            102
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            8.2%
                          </td>
                        </tr>
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            解聘
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            48
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            3.9%
                          </td>
                        </tr>
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            其他
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            235
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            18.9%
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <!-- 状态趋势 -->
                <div v-if="activeTab === 'trend'" class="mt-4">
                  <div class="h-80 flex items-center justify-center rounded bg-gray-100">
                    <div class="text-center">
                      <div class="mb-2 text-gray-500">
                        折线图展示处理状态趋势
                      </div>
                      <div class="flex justify-center text-xs space-x-4">
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-blue-500" />
                          <span>待处理</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-green-500" />
                          <span>处理中</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-red-500" />
                          <span>已完成</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-gray-500" />
                          <span>已逾期</span>
                        </div>
                      </div>
                      <div class="mt-2 text-xs text-gray-400">
                        X轴: 时间 (按周) | Y轴: 措施数量
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 部门分布 -->
                <div v-if="activeTab === 'department'" class="mt-4">
                  <div class="h-80 flex items-center justify-center rounded bg-gray-100">
                    <div class="text-center">
                      <div class="mb-2 text-gray-500">
                        横向堆叠柱状图展示各部门的处理措施
                      </div>
                      <div class="flex justify-center text-xs space-x-4">
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-blue-500" />
                          <span>人力资源部</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-green-500" />
                          <span>财务部</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-yellow-500" />
                          <span>技术部</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-purple-500" />
                          <span>市场部</span>
                        </div>
                      </div>
                      <div class="mt-2 text-xs text-gray-400">
                        X轴: 部门 | Y轴: 措施数量 (堆叠表示不同状态)
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 及时率分析 -->
                <div v-if="activeTab === 'timeliness'" class="mt-4">
                  <div class="h-80 flex items-center justify-center rounded bg-gray-100">
                    <div class="text-center">
                      <div class="mb-2 text-gray-500">
                        条形图展示各部门/类型的处理及时率
                      </div>
                      <div class="flex justify-center text-xs space-x-4">
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-blue-500" />
                          <span>目标及时率</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-green-500" />
                          <span>实际及时率</span>
                        </div>
                      </div>
                      <div class="mt-2 text-xs text-gray-400">
                        部门/类型 vs 及时率对比
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- <el-card shadow="hover" class="">
                <template #header>
                  <div class="f-16 fw-600">基本信息</div>
                </template>
              </el-card> -->
            </el-col>
            <el-col v-if="false" :span="6">
              <el-card shadow="hover" class="">
                <template #header>
                  <div class="aic jcsb flex">
                    <div class="f-16 fw-600">
                      处理类型
                    </div>
                    <!-- <el-link type="primary">查看更多</el-link> -->
                  </div>
                  <div class="space-y-3">
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.warning" class="mr-2" />
                      <span>警告</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.training" class="mr-2" />
                      <span>培训教育</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.salaryReduction" class="mr-2" />
                      <span>减少薪酬</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.demotion" class="mr-2" />
                      <span>降级</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.dismissal" class="mr-2" />
                      <span>解聘</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.fine" class="mr-2" />
                      <span>经济处罚</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.other" class="mr-2" />
                      <span>其他</span>
                    </div>
                    <a href="#" class="mt-2 block text-sm text-blue-500" @click="toggleAllTypes">全选/取消全选</a>
                  </div>
                </template>
              </el-card>
              <el-card shadow="hover" class="mt-20">
                <template #header>
                  <div class="aic jcsb flex">
                    <div class="f-16 fw-600">
                      常用模板
                    </div>
                    <!-- <el-link type="primary">查看更多</el-link> -->
                  </div>
                </template>

                <div class="space-y-4">
                  <div v-for="template in templates" :key="template.id" class="border-b pb-4 last:border-b-0">
                    <div class="mb-1 font-medium">
                      {{ template.name }}
                    </div>
                    <div class="mb-2 text-sm text-gray-500">
                      {{ template.scenario }}
                    </div>
                    <el-button
                      v-auth="'accountability:handlingMeasures:applyTemplate'"
                      type="primary"
                      size="small"
                    >
                      应用模板
                    </el-button>
                  </div>
                  <a href="#" class="mt-2 block text-sm text-blue-500">管理模板</a>
                </div>
              </el-card>
              <el-card shadow="hover" class="mt-20">
                <template #header>
                  <div class="aic jcsb flex">
                    <div class="f-16 fw-600">
                      优先处理建议
                    </div>
                    <!-- <el-link type="primary">查看更多</el-link> -->
                  </div>
                </template>
                <div class="space-y-3">
                  <div
                    v-for="recommendation in recommendations" :key="recommendation.id"
                    class="border-b pb-3 last:border-b-0"
                  >
                    <div class="font-medium">
                      {{ recommendation.title }}
                    </div>
                    <div class="mb-1 text-sm text-gray-500">
                      {{ recommendation.reason }}
                    </div>
                    <a href="#" class="text-sm text-blue-500">查看详情</a>
                  </div>
                </div>
              </el-card>
              <el-card shadow="hover" class="mt-20">
                <template #header>
                  <div class="aic jcsb flex">
                    <div class="f-16 fw-600">
                      AI助手
                    </div>
                    <!-- <el-link type="primary">查看更多</el-link> -->
                  </div>
                </template>
                <div class="flex flex-col space-y-3">
                  <el-button v-auth="'accountability:handlingMeasures:analyzeEfficiency'" type="primary">
                    分析处理效率
                  </el-button>
                  <el-button v-auth="'accountability:handlingMeasures:generateSuggestion'" type="primary">
                    生成处理建议
                  </el-button>
                  <el-button v-auth="'accountability:handlingMeasures:predictResult'" type="primary">
                    预测处理结果
                  </el-button>
                </div>
                <div v-if="aiSuggestion" class="mt-4 rounded bg-gray-50 p-3 text-sm">
                  {{ aiSuggestion }}
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .min-h-screen {
    min-height: 1024px;
  }

  .h-screen {
    height: 1024px;
  }
</style>
