<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import organizationalApi from '@/api/organizational/index'

interface Props {
  modelValue?: any
  placeholder?: string
  width?: string
  multiple?: boolean
  clearable?: boolean
  disabled?: boolean
  autoLoad?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择部门',
  width: '100%',
  multiple: false,
  clearable: true,
  disabled: false,
  autoLoad: true,
})

const emit = defineEmits<Emits>()

// 部门树形数据
const departmentTree = ref([])
const loading = ref(false)

// 处理modelValue，确保数据类型正确
const processedModelValue = computed(() => {
  if (!props.modelValue) {
    return props.modelValue
  }

  if (props.multiple && Array.isArray(props.modelValue)) {
    // 多选模式：确保数组中的每个值都是数字类型
    return props.modelValue.map((val) => {
      if (typeof val === 'string' && !Number.isNaN(Number(val))) {
        return Number(val)
      }
      return val
    })
  }
  else {
    // 单选模式：确保值是数字类型
    if (typeof props.modelValue === 'string' && !Number.isNaN(Number(props.modelValue))) {
      return Number(props.modelValue)
    }
    return props.modelValue
  }
})

// 获取部门树形数据
async function fetchDepartmentTree() {
  try {
    loading.value = true
    const response = await organizationalApi.organizationalUnitTreeApi()
    departmentTree.value = response || []
  }
  catch (error) {
    console.error('获取部门树失败:', error)
    ElMessage.error('获取部门树失败')
  }
  finally {
    loading.value = false
  }
}

// 处理值更新
function handleUpdate(value: any) {
  // 如果传入的是字符串类型，尝试将其转换为数字类型
  if (typeof value === 'string' && !Number.isNaN(Number(value))) {
    value = Number(value)
  }
  emit('update:modelValue', value)
  emit('change', value)
}

// 暴露方法供外部调用
defineExpose({
  fetchDepartmentTree,
  departmentTree,
  loading,
})

// 监听 modelValue 变化，处理字符串转数字
watch(() => props.modelValue, (newValue) => {
  if (typeof newValue === 'string' && !Number.isNaN(Number(newValue))) {
    emit('update:modelValue', Number(newValue))
  }
}, { immediate: true })

// 组件挂载时自动加载数据
onMounted(() => {
  if (props.autoLoad) {
    fetchDepartmentTree()
  }
})
</script>

<template>
  <!-- disabled: (data: any) => data.type !== 'DEPARTMENT' -->
  <el-tree-select
    :model-value="processedModelValue"
    :data="departmentTree"
    :loading="loading"
    :placeholder="placeholder"
    :style="{ width }"
    :props="{
      value: 'id',
      label: 'name',
      children: 'children',
    }"
    :multiple="multiple"
    :clearable="clearable"
    :disabled="disabled"
    check-strictly
    :render-after-expand="false"
    :show-checkbox="multiple"
    node-key="id"
    @update:model-value="handleUpdate"
  >
    <template #default="{ node, data }">
      <span class="tree-node-label" :title="data.name">{{ data.name }}</span>
    </template>
  </el-tree-select>
</template>
