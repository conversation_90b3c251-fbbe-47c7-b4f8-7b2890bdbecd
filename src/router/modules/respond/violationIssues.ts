import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/respond/violationIssues',
  component: Layout,
  // redirect: '/one/1',
  name: '/respond/violationIssues',
  meta: {
    title: '违规问题调查',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/respond/violationIssues/taskManagement',
      name: '/respond/violationIssues/taskManagement',
      component: () => import('@/views/hegui/respond/violationIssues/taskManagement/index.vue'),
      meta: {
        title: '调查任务管理',
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/respond/violationIssues/taskManagement/detail',
          name: '/respond/violationIssues/taskManagement/detail',
          component: () => import('@/views/hegui/respond/violationIssues/taskManagement/detail.vue'),
          meta: {
            title: '调查任务管理详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/respond/violationIssues/taskManagement/addEdit',
          name: '/respond/violationIssues/taskManagement/addEdit',
          component: () => import('@/views/hegui/respond/violationIssues/taskManagement/addEdit.vue'),
          meta: {
            title: '调查任务管理',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/respond/violationIssues/taskManagement/taskReport',
          name: '/respond/violationIssues/taskManagement/taskReport',
          component: () => import('@/views/hegui/respond/violationIssues/taskManagement/taskReport.vue'),
          meta: {
            title: '调查报告',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/respond/violationIssues/taskManagement/taskReview',
          name: '/respond/violationIssues/taskManagement/taskReview',
          component: () => import('@/views/hegui/respond/violationIssues/taskManagement/taskReview.vue'),
          meta: {
            title: '调查记录',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/respond/violationIssues/reportManagement',
      name: '/respond/violationIssues/reportManagement',
      component: () => import('@/views/hegui/respond/violationIssues/reportManagement/index.vue'),
      meta: {
        title: '调查报告管理',
      },
      children: [
        {
          path: '/respond/violationIssues/reportManagement/addEdit',
          name: '/respond/violationIssues/reportManagement/addEdit',
          component: () => import('@/views/hegui/respond/violationIssues/reportManagement/addEdit.vue'),
          meta: {
            title: '调查报告管理-新增',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/respond/violationIssues/reportManagement/detail',
          name: '/respond/violationIssues/reportManagement/detail',
          component: () => import('@/views/hegui/respond/violationIssues/reportManagement/detail.vue'),
          meta: {
            title: '调查报告管理-详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
  ],
}

export default routes
