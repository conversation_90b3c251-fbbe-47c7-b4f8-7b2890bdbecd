<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Document, Download, View } from '@element-plus/icons-vue'
import problemTaskApi, { type ProblemInvestigateReportDTO } from '@/api/problemTask'
import dictApi from '@/api/modules/system/dict'
import UploadMbb from '@/components/uploadMbb/index.vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
// 报告详情数据
const reportData = reactive<ProblemInvestigateReportDTO>({
  id: undefined,
  title: '',
  reportCode: '',
  investigateId: 0,
  reportType: undefined,
  investigateSource: undefined,
  employeeId: 0,
  establishDate: '',
  level: undefined,
  summary: '',
  orgId: 0,
  investigateBackground: '',
  investigateMethod: '',
  investigateProcess: '',
  investigateFound: '',
  investigateConclusion: '',
  recommendMeasure: '',
  attachmentList: [],
})

// 字典选项
const reportTypeOptions = ref<Array<{ name: string, value: string }>>([])

// 获取报告详情
async function loadReportDetail() {
  try {
    loading.value = true
    const reportId = route.query.id as any
    if (!reportId) {
      ElMessage.error('报告ID不能为空')
      return
    }

    const response = await problemTaskApi.getReportDetail(reportId)
    if (response) {
      Object.assign(reportData, response)
    }
    else {
      ElMessage.error('获取报告详情失败')
    }
  }
  catch (error) {
    console.error('获取报告详情失败:', error)
    ElMessage.error('获取报告详情失败')
  }
  finally {
    loading.value = false
  }
}

// 获取报告类型名称
function getReportTypeName(value?: string) {
  if (!value) {
    return '-'
  }
  const option = reportTypeOptions.value.find(item => item.value === value)
  return option ? option.name : value
}

// 获取等级名称
function getLevelName(value?: string) {
  if (!value) {
    return '-'
  }
  // 这里可以根据实际字典数据进行转换
  const levelMap: Record<string, string> = {
    LOW: '低',
    MIDDLE: '中',
    HIGH: '高',
  }
  return levelMap[value] || value
}

// 获取调查来源名称
function getInvestigateSourceName(value?: string) {
  if (!value) {
    return '-'
  }
  // 这里可以根据实际字典数据进行转换
  const sourceMap: Record<string, string> = {
    INTERNAL_REPORT: '内部举报',
    EXTERNAL_REPORT: '外部举报',
    REGULATORY_REQUIREMENT: '监管要求',
    INTERNAL_AUDIT: '内部审计',
    RISK_IDENTIFICATION: '风险识别',
    OTHER: '其他',
  }
  return sourceMap[value] || value
}

// 返回上一页
function goBack() {
  router.back()
}

// 编辑报告
function editReport() {
  router.push({
    path: '/respond/violationIssues/reportManagement/addEdit',
    query: { id: route.query.id },
  })
}

// 加载字典数据
async function loadDictData() {
  try {
    // 加载报告类型选项
    reportTypeOptions.value = await dictApi.dictAll(32)
  }
  catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadDictData()
  loadReportDetail()
})
</script>

<template>
  <div v-loading="loading" class="min-h-screen bg-gray-50">
    <!-- 顶部导航与操作区 -->
    <div class="bg-white shadow-sm">
      <div class="mx-auto max-w-7xl px-4 py-4">
        <!-- 页面标题和操作按钮 -->
        <div class="mt-4 flex items-center justify-between">
          <h1 class="text-xl font-bold">
            {{ reportData.title || '调查报告详情' }}
          </h1>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="editReport">
              <i class="el-icon-edit mr-1" />编辑
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="goBack">
              <i class="el-icon-back mr-1" />返回
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <PageMain style="background-color: transparent;">
      <div>
        <!-- 基本信息 -->
        <el-card shadow="hover" class="mb-6">
          <div class="grid grid-cols-2 gap-4">
            <div class="grid grid-cols-[auto_1fr] gap-1">
              <div class="text-left text-gray-500">
                报告标题：
              </div>
              <div>{{ reportData.title || '-' }}</div>
              <div class="text-left text-gray-500">
                报告编号：
              </div>
              <div>{{ reportData.reportCode || '-' }}</div>
              <div class="text-left text-gray-500">
                关联调查：
              </div>
              <div>
                <el-link v-if="reportData.investigateName" type="primary">
                  {{ reportData.investigateName }}
                </el-link>
                <span v-else>-</span>
              </div>
              <div class="text-left text-gray-500">
                报告类型：
              </div>
              <div>{{ getReportTypeName(reportData.reportType) || '-' }}</div>
              <div class="text-left text-gray-500">
                调查来源：
              </div>
              <div>{{ getInvestigateSourceName(reportData.investigateSource) || '-' }}</div>
            </div>
            <div class="grid grid-cols-[auto_1fr] gap-1">
              <div class="text-left text-gray-500">
                调查人员：
              </div>
              <div>{{ reportData.employeeName || '-' }}</div>
              <div class="text-left text-gray-500">
                成立日期：
              </div>
              <div>{{ reportData.establishDate || '-' }}</div>
              <div class="text-left text-gray-500">
                问题等级：
              </div>
              <div>
                <el-tag :type="reportData.level === 'HIGH' ? 'danger' : reportData.level === 'MIDDLE' ? 'warning' : 'info'">
                  {{ getLevelName(reportData.level) || '-' }}
                </el-tag>
              </div>
              <div class="text-left text-gray-500">
                所属机构：
              </div>
              <div>{{ reportData.orgName || '-' }}</div>
            </div>
          </div>
        </el-card>
        <!-- 调查报告摘要 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              调查报告摘要
            </div>
          </template>
          <div class="p-4">
            <div class="form-display-content">
              {{ reportData.summary || '暂无摘要内容' }}
            </div>
          </div>
        </el-card>

        <!-- 调查背景 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              一、调查背景
            </div>
          </template>
          <div class="p-4">
            <div class="form-display-content">
              {{ reportData.investigateBackground || '暂无背景描述' }}
            </div>
          </div>
        </el-card>
        <!-- 调查方法 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              二、调查方法
            </div>
          </template>
          <div class="p-4">
            <div class="form-display-content">
              {{ reportData.investigateMethod || '暂无方法说明' }}
            </div>
          </div>
        </el-card>
        <!-- 调查过程 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              三、调查过程
            </div>
          </template>
          <div class="p-4">
            <div class="form-display-content">
              {{ reportData.investigateProcess || '暂无过程记录' }}
            </div>
          </div>
        </el-card>
        <!-- 调查发现 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              四、调查发现
            </div>
          </template>
          <div class="p-4">
            <div class="form-display-content">
              {{ reportData.investigateFound || '暂无发现内容' }}
            </div>
          </div>
        </el-card>
        <!-- 调查结论 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              五、调查结论
            </div>
          </template>
          <div class="p-4">
            <div class="form-display-content">
              {{ reportData.investigateConclusion || '暂无结论内容' }}
            </div>
          </div>
        </el-card>
        <!-- 建议措施 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              六、建议措施
            </div>
          </template>
          <div class="p-4">
            <div class="form-display-content">
              {{ reportData.recommendMeasure || '暂无建议措施' }}
            </div>
          </div>
        </el-card>
        <!-- 附件 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              七、附件
            </div>
          </template>
          <div class="p-4">
            <UploadMbb
              v-model="reportData.attachmentList"
              :readonly="true"
            />
          </div>
        </el-card>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-form-item {
    margin-bottom: 24px;
  }

  .el-collapse-item__header {
    font-weight: 500;
  }

  .el-table {
    margin-top: 10px;
  }

  .upload-demo {
    margin-top: 10px;
  }

  .form-display-value {
    padding: 8px 0;
    color: #303133;
    font-size: 14px;
    line-height: 1.5;
    min-height: 32px;
    display: flex;
    align-items: center;
  }

  .form-display-content {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 4px;
    color: #303133;
    font-size: 14px;
    line-height: 1.6;
    min-height: 100px;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .attachment-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .attachment-item {
    transition: all 0.3s ease;
  }

  .attachment-item:hover {
    background-color: #f5f7fa;
    border-color: #409eff;
  }

  .el-card {
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .el-card__header {
    padding: 18px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
  }

  .text-lg {
    font-size: 18px;
  }

  .font-semibold {
    font-weight: 600;
  }

  .text-center {
    text-align: center;
  }

  .text-gray-500 {
    color: #909399;
  }

  .py-8 {
    padding-top: 32px;
    padding-bottom: 32px;
  }
</style>
