import api from '@/api/index'

export default {
  // 更新密码
  updatePassword: (userId: any, params: any) => {
    return api.put(`/whiskerguardorgservice/api/employees/${userId}/password`, params)
  },
  // 更新个人信息
  updatePersonalInfo: (userId: any, params: any) => {
    return api.put(`/whiskerguardorgservice/api/employees/${userId}/profile`, params)
  },
  // 忘记密码
  forgetPassword: (params: any) => {
    return api.put(`/whiskerguardorgservice/api/employees/password/reset`, params)
  },
  // 发送验证码
  sendCode: (params: any) => {
    return api.post(`/whiskerguardgeneralservice/api/verification/code/send`, params)
  },
}
