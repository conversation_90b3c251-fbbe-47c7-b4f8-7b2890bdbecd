{"id": 111, "tenantCode": "mbb09498MEBAK", "name": "中合数联（北京）科技有限公司", "status": 1, "subscriptionPlan": null, "subscriptionStart": null, "subscriptionEnd": null, "contactEmail": "<EMAIL>", "contactPhone": "0755-********", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 06:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 06:24:24", "isDeleted": false, "profile": {"id": 21, "registrationNumber": "91440300MA5GXXXXXX", "registrationDate": "2022-08-18", "registeredCapital": 5000000.0, "companyType": "有限责任公司", "businessScope": "技术开发、技术咨询、软件开发、信息系统集成服务、数据处理和存储支持服务。", "industry": "软件和信息技术服务业", "taxRegistrationNumber": "91440300MA5GXXXXXX", "organizationCode": "91440300MA5GXXXXXX", "registeredAddress": "深圳市南山区粤海街道高新南一道9号中科研发园", "postalCode": "518057", "website": "www.kcyunlian.com", "fax": "0755-********", "contactPerson": "王浩", "contactMobile": "***********", "contactEmail": "<EMAIL>", "bankName": "招商银行深圳高新园支行", "bankAccount": "7559000123456789012", "businessLicensePath": "/home/<USER>/licenses/kcyunlian_license.pdf", "legalPerson": "李静", "legalPersonId": "44030119900307XXXX", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 06:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 06:24:24", "isDeleted": false, "tenant": {"id": 111, "tenantCode": "mbb09498MEBAK", "name": "中合数联（北京）科技有限公司", "status": 1, "subscriptionPlan": null, "subscriptionStart": null, "subscriptionEnd": null, "contactEmail": "<EMAIL>", "contactPhone": "0755-********", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 06:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 06:24:24", "isDeleted": false, "profile": null, "attachments": null}, "employeeCount": 17}, "attachments": null}