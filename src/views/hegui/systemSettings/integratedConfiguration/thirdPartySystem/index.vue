<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'

// 菜单状态
const activeMenu = ref('5')

// 表格数据
const activeTab = ref('office')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(30)
const integrationList = ref([
  { name: '企业微信', type: '办公协同', status: '已连接', createTime: '2023-05-12 14:30', lastConnect: '2023-06-01 09:15' },
  { name: '钉钉', type: '办公协同', status: '已连接', createTime: '2023-04-28 10:20', lastConnect: '2023-05-30 16:45' },
  { name: 'LDAP认证', type: '身份认证', status: '未连接', createTime: '2023-03-15 11:10', lastConnect: '2023-03-20 14:00' },
  { name: 'MySQL数据源', type: '数据集成', status: '等待配置', createTime: '2023-06-05 09:30', lastConnect: '-' },
  { name: '阿里云OSS', type: '数据集成', status: '已连接', createTime: '2023-02-18 16:20', lastConnect: '2023-06-02 11:30' },
  { name: 'OpenAI API', type: 'AI服务', status: '已连接', createTime: '2023-05-20 13:45', lastConnect: '2023-06-03 10:15' },
  { name: 'Slack', type: '办公协同', status: '未连接', createTime: '2023-01-10 09:00', lastConnect: '2023-01-15 14:30' },
])

// 操作日志
const operationLogs = ref([
  { operator: '张明远', time: '2023-06-03 14:30', action: '修改了企业微信集成配置' },
  { operator: '李思思', time: '2023-06-02 11:20', action: '添加了OpenAI API集成' },
  { operator: '王建国', time: '2023-06-01 09:45', action: '测试了LDAP认证连接' },
  { operator: '陈晓雯', time: '2023-05-30 16:30', action: '导出了当前集成配置' },
  { operator: '赵志强', time: '2023-05-28 14:15', action: '禁用了钉钉集成' },
])

// 对话框状态
const dialogVisible = ref(false)
const integrationForm = ref({
  name: '',
  type: '',
  vendor: '',
  status: '启用',
  description: '',
  config: {
    corpId: '',
    appId: '',
    appSecret: '',
    callbackUrl: '',
    token: '',
    aesKey: '',
  },
})

// 图表引用
const pieChart = ref<HTMLElement | null>(null)

// 状态标签类型
function getStatusType(status: string) {
  switch (status) {
    case '已连接': return 'success'
    case '未连接': return 'danger'
    case '等待配置': return 'info'
    default: return ''
  }
}

// 初始化图表
function initChart() {
  if (!pieChart.value) { return }

  const chart = echarts.init(pieChart.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '集成状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 18, name: '已连接' },
          { value: 7, name: '未连接' },
          { value: 5, name: '等待配置' },
        ],
        color: ['#67C23A', '#F56C6C', '#909399'],
      },
    ],
  }

  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 保存集成
function handleSave() {
  dialogVisible.value = false
  // 这里添加保存逻辑
}

// 测试连接
function handleTestConnection() {
  // 这里添加测试连接逻辑
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<template>
  <div class="absolute-container" style="">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              第三方系统集成
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex items-center space-x-2">
            <el-button v-auth="'thirdPartySystem/index/add'" type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <i class="fas fa-plus" />
              </el-icon>
              添加集成
            </el-button>
            <el-button v-auth="'thirdPartySystem/index/import'" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <i class="fas fa-file-import" />
              </el-icon>
              导入配置
            </el-button>
            <el-button v-auth="'thirdPartySystem/index/export'" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <i class="fas fa-file-export" />
              </el-icon>
              导出配置
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  第三方系统集成
                </div>
              </template>
              <!-- 标签页 -->
              <el-tabs v-model="activeTab" class="mb-6">
                <el-tab-pane label="办公协同" name="office" />
                <el-tab-pane label="身份认证" name="auth" />
                <el-tab-pane label="数据集成" name="data" />
                <el-tab-pane label="AI服务" name="ai" />
                <el-tab-pane label="其他" name="other" />
              </el-tabs>

              <!-- 表格 -->
              <el-table :data="integrationList" border style="width: 100%;">
                <el-table-column prop="name" label="名称" width="180" />
                <el-table-column prop="type" label="集成类型" width="120" />
                <el-table-column prop="status" label="状态" width="120">
                  <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)" size="small">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="180" />
                <el-table-column prop="lastConnect" label="上次连接" width="180" />
                <el-table-column label="操作" width="230">
                  <template #default="{ row }">
                    <el-button v-auth="'thirdPartySystem/index/view'" size="small" type="primary">
                      <el-icon><i class="fas fa-view" /></el-icon>
                      查看
                    </el-button>
                    <el-button v-auth="'thirdPartySystem/index/view'" size="small" type="warning">
                      <el-icon><i class="fas fa-edit" /></el-icon>
                      编辑
                    </el-button>
                    <el-button v-auth="'thirdPartySystem/index/view'" size="small" type="danger">
                      <el-icon><i class="fas fa-delete" /></el-icon>
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页 -->
              <div class="mt-6 flex justify-between">
                <el-pagination
                  :current-page="currentPage" :page-size="pageSize" :total="total"
                  layout="prev, pager, next"
                />
                <el-select v-model="pageSize" class="w-32">
                  <el-option label="10 条/页" :value="10" />
                  <el-option label="20 条/页" :value="20" />
                  <el-option label="50 条/页" :value="50" />
                </el-select>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  集成状态
                </div>
              </template>
              <div ref="pieChart" class="h-48" />
              <div class="mt-4 space-y-2">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-500">办公协同</span>
                  <span class="font-medium">12</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-500">身份认证</span>
                  <span class="font-medium">8</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-500">数据集成</span>
                  <span class="font-medium">5</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-500">AI服务</span>
                  <span class="font-medium">3</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-500">其他</span>
                  <span class="font-medium">2</span>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  近期操作
                </div>
              </template>
              <div class="space-y-4">
                <div v-for="(log, index) in operationLogs" :key="index" class="flex">
                  <div class="mr-3 flex flex-col items-center">
                    <div class="h-2 w-2 rounded-full bg-blue-500" />
                    <div v-if="index !== operationLogs.length - 1" class="my-1 h-8 w-px bg-gray-200" />
                  </div>
                  <div>
                    <div class="text-sm font-medium">
                      {{ log.operator }}
                    </div>
                    <div class="text-xs text-gray-500">
                      {{ log.time }}
                    </div>
                    <div class="mt-1 text-sm text-gray-600">
                      {{ log.action }}
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <!-- 添加/编辑对话框 -->
      <el-dialog v-model="dialogVisible" title="添加集成" width="50%">
        <el-form :model="integrationForm" label-width="120px">
          <el-form-item label="集成名称">
            <el-input v-model="integrationForm.name" placeholder="请输入集成名称" />
          </el-form-item>
          <el-form-item label="集成类型">
            <el-select v-model="integrationForm.type" placeholder="请选择集成类型">
              <el-option label="办公协同" value="office" />
              <el-option label="身份认证" value="auth" />
              <el-option label="数据集成" value="data" />
              <el-option label="AI服务" value="ai" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="系统供应商">
            <el-select v-model="integrationForm.vendor" placeholder="请选择系统供应商">
              <el-option label="企业微信" value="wecom" />
              <el-option label="钉钉" value="dingtalk" />
              <el-option label="飞书" value="feishu" />
              <el-option label="阿里云" value="aliyun" />
              <el-option label="腾讯云" value="tencent" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-radio-group v-model="integrationForm.status">
              <el-radio label="启用" />
              <el-radio label="禁用" />
            </el-radio-group>
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="integrationForm.description" type="textarea" rows="3" />
          </el-form-item>

          <!-- 动态配置表单 -->
          <template v-if="integrationForm.type === 'office' && integrationForm.vendor === 'wecom'">
            <el-divider />
            <h4 class="mb-4 text-lg font-medium">
              企业微信配置
            </h4>
            <el-form-item label="企业ID">
              <el-input v-model="integrationForm.config.corpId" placeholder="请输入企业ID" />
            </el-form-item>
            <el-form-item label="应用ID">
              <el-input v-model="integrationForm.config.appId" placeholder="请输入应用ID" />
            </el-form-item>
            <el-form-item label="应用密钥">
              <el-input v-model="integrationForm.config.appSecret" type="password" placeholder="请输入应用密钥" />
            </el-form-item>
            <el-form-item label="回调地址">
              <el-input v-model="integrationForm.config.callbackUrl" placeholder="请输入回调地址" />
            </el-form-item>
            <el-form-item label="Token">
              <el-input v-model="integrationForm.config.token" placeholder="请输入Token" />
            </el-form-item>
            <el-form-item label="EncodingAESKey">
              <el-input v-model="integrationForm.config.aesKey" placeholder="请输入EncodingAESKey" />
            </el-form-item>
          </template>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="handleSave">
            保存
          </el-button>
          <el-button @click="handleTestConnection">
            测试连接
          </el-button>
        </template>
      </el-dialog>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .active-menu {
    background-color: rgb(24 144 255 / 10%) !important;
  }

  .el-menu {
    --el-menu-active-color: #1890ff;
    --el-menu-hover-bg-color: rgb(24 144 255 / 20%);
  }

  .el-menu-item.is-active {
    background-color: rgb(24 144 255 / 10%) !important;
  }

  .el-sub-menu.is-active .el-sub-menu__title {
    color: #1890ff !important;
  }

  .el-table {
    --el-table-border-color: #f0f0f0;
    --el-table-header-bg-color: #fafafa;
  }

  .el-tabs {
    --el-tabs-header-height: 40px;
  }

  .el-tabs__item.is-active {
    font-weight: 500;
    color: #1890ff;
  }

  .el-tabs__active-bar {
    background-color: #1890ff;
  }

  .el-dialog {
    --el-dialog-title-font-size: 16px;
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-divider {
    --el-border-width-base: 1px;

    margin: 20px 0;
  }
</style>
