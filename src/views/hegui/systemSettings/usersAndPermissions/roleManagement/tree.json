[{"id": 1878, "parentId": 0, "path": "/one", "redirect": null, "component": "", "name": "/one", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"一体\",\"icon\":\"ep:menu\",\"activeIcon\":\"ep:menu\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 1879, "parentId": 1878, "path": "/one/systemManagement", "redirect": null, "component": "Layout", "name": "/one/systemManagement", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"制度管理\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 1880, "parentId": 1879, "path": "/one/systemManagement/index", "redirect": null, "component": "hegui/one/system/systemManagement/index.vue", "name": "/one/systemManagement/index", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"制度库管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 1891, "parentId": 1880, "path": "/one/systemManagement/detail", "redirect": null, "component": "hegui/one/system/systemManagement/detail.vue", "name": "/one/systemManagement/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"制度详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/one/systemManagement/index\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2525, "parentId": 1880, "path": "/one/systemManagement/add", "redirect": null, "component": "hegui/one/system/systemManagement/addEdit.vue", "name": "/one/systemManagement/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增制度\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/one/systemManagement/index\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 1896, "parentId": 1880, "path": "/one/systemManagement/addEdit", "redirect": null, "component": "hegui/one/system/systemManagement/addEdit.vue", "name": "/one/systemManagement/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"编辑制度\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/one/systemManagement/index\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 1881, "name": "新建制度", "value": "systemManagement/index/add"}, {"id": 1883, "name": "删除", "value": "systemManagement/index/delete"}, {"id": 1885, "name": "修改", "value": "systemManagement/index/edit"}, {"id": 1886, "name": "批量导入", "value": "systemManagement/index/import"}, {"id": 1888, "name": "重置", "value": "systemManagement/index/reset"}, {"id": 1889, "name": "查询", "value": "systemManagement/index/search"}, {"id": 1890, "name": "查看详情", "value": "systemManagement/index/viewDetail"}, {"id": 2328, "name": "制度分类管理", "value": "systemManagement/index/categories"}, {"id": 2333, "name": "查看", "value": "systemManagement/index/view"}], "isAvailable": null, "isShow": true}, {"id": 1901, "parentId": 1879, "path": "/one/regulatoryConversion/index", "redirect": null, "component": "hegui/one/system/regulatoryConversion/index.vue", "name": "/one/regulatoryConversion/index", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"法规转化\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 1908, "parentId": 1901, "path": "/one/regulatoryConversion/detail", "redirect": null, "component": "hegui/one/system/regulatoryConversion/detail.vue", "name": "/one/regulatoryConversion/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"制度详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/one/regulatoryConversion/index\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2610, "name": "立即转化", "value": "regulatoryDatabaseMode/detail/transform"}], "isAvailable": null, "isShow": true}], "auths": [{"id": 1903, "name": "删除", "value": "regulatoryConversion/index/delete"}, {"id": 1904, "name": "详情", "value": "regulatoryConversion/index/detail"}, {"id": 1905, "name": "不通过", "value": "regulatoryConversion/index/reject"}, {"id": 1906, "name": "重置", "value": "regulatoryConversion/index/reset"}, {"id": 1907, "name": "查询", "value": "regulatoryConversion/index/search"}, {"id": 2337, "name": "通过", "value": "regulatoryConversion/index/approve"}], "isAvailable": null, "isShow": true}, {"id": 1916, "parentId": 1879, "path": "/system/review", "redirect": null, "component": "hegui/one/system/systemAdditionAndReview.vue", "name": "/system/review", "realName": null, "isMenu": true, "sort": 3, "mark": 1, "meta": "{\"title\":\"制度审查记录\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 1930, "name": "审核报告", "value": "systemAdditionAndReview/index/auditReport"}, {"id": 1933, "name": "删除", "value": "systemAdditionAndReview/index/delete"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 1939, "parentId": 1878, "path": "/database", "redirect": null, "component": "Layout", "name": "database", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"合规数据库\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 1940, "parentId": 1939, "path": "/database/laws", "redirect": null, "component": "hegui/one/database/regulatoryDatabase.vue", "name": "database/laws", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"法规库\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 1952, "parentId": 1940, "path": "/database/laws/detail", "redirect": null, "component": "hegui/one/database/regulatoryDatabaseMode/detail.vue", "name": "/database/laws/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"法规详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/database/laws\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 1955, "name": "立即转化", "value": "regulatoryDatabaseMode/detail/transform"}], "isAvailable": null, "isShow": true}], "auths": [{"id": 1944, "name": "重置", "value": "regulatoryDatabase/reset"}, {"id": 1945, "name": "查询", "value": "regulatoryDatabase/search"}, {"id": 1951, "name": "查看详情", "value": "regulatoryDatabaseMode/center/viewDetail"}, {"id": 2338, "name": "智能推荐", "value": "regulatoryDatabase/recommend"}, {"id": 2339, "name": "已加入", "value": "regulatoryDatabaseMode/center/join"}], "isAvailable": null, "isShow": true}, {"id": 2152, "parentId": 1939, "path": "/database/tenantRegulations", "redirect": null, "component": "hegui/one/database/tenantRegulations.vue", "name": "/database/tenantRegulations", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"专属法规库\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 2155, "name": "查看详情", "value": "database/tenantRegulations/detail"}, {"id": 2156, "name": "删除", "value": "database/tenantRegulations/del"}], "isAvailable": null, "isShow": true}, {"id": 1961, "parentId": 1939, "path": "/database/cases", "redirect": null, "component": "hegui/one/database/complianceCaseLibrary.vue", "name": "database/cases", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"合规案例库\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2528, "parentId": 1961, "path": "/database/cases/add", "redirect": null, "component": "hegui/one/database/complianceCaseLibraryMode/addEdit.vue", "name": "/database/cases/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增案例\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/database/cases\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2606, "parentId": 1961, "path": "/database/cases/detail", "redirect": null, "component": "hegui/one/database/complianceCaseLibraryMode/detail.vue", "name": "/database/cases/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"案例库详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": null}, {"id": 1974, "parentId": 1961, "path": "/database/cases/addEdit", "redirect": null, "component": "hegui/one/database/complianceCaseLibraryMode/addEdit.vue", "name": "/database/cases/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"编辑案例\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/database/cases\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 1962, "name": "新增案例", "value": "complianceCaseLibrary/index/add"}, {"id": 1966, "name": "编辑", "value": "complianceCaseLibrary/index/edit"}, {"id": 2607, "name": "查看", "value": "complianceCaseLibrary/index/view"}, {"id": 2613, "name": "删除", "value": "complianceCaseLibrary/index/delete"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2364, "parentId": 1878, "path": "/one/qa", "redirect": null, "component": "Layout", "name": "/one/qa", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"问答库管理\",\"icon\":\"ep:key\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2365, "parentId": 2364, "path": "/one/qa/index", "redirect": null, "component": "hegui/one/qa/index.vue", "name": "/one/qa/index", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"智能问答\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 1977, "parentId": 0, "path": "/", "redirect": null, "component": "", "name": "/two", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"预防之翼\",\"icon\":\"ep:aim\",\"activeIcon\":\"ep:aim\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 1978, "parentId": 1977, "path": "/threeListManagement", "redirect": null, "component": "Layout", "name": "/threeListManagement", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"三张清单管理\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 1979, "parentId": 1978, "path": "/threeListManagement/complianceRisk", "redirect": null, "component": "hegui/prevention/threeListManagement/complianceRisk.vue", "name": "threeListManagement/complianceRisk", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"合规风险识别清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 1992, "parentId": 1979, "path": "/threeListManagement/complianceRisk/edit", "redirect": null, "component": "hegui/prevention/threeListManagement/complianceRisk_edit.vue", "name": "/threeListManagement/complianceRisk/edit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"编辑合规清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/complianceRisk\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2531, "parentId": 1979, "path": "/threeListManagement/complianceRisk/add", "redirect": null, "component": "hegui/prevention/threeListManagement/complianceRisk_edit.vue", "name": "/threeListManagement/complianceRisk/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增合规清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/complianceRisk\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2597, "parentId": 1979, "path": "/threeListManagement/complianceRisk/detail", "redirect": null, "component": "hegui/prevention/threeListManagement/complianceRisk_detail.vue", "name": "/threeListManagement/complianceRisk/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 1980, "name": "新增清单", "value": "threeListManagement/complianceRisk/add"}, {"id": 1985, "name": "删除", "value": "threeListManagement/complianceRisk/delete"}, {"id": 1986, "name": "编辑", "value": "threeListManagement/complianceRisk/edit"}, {"id": 1991, "name": "查看", "value": "threeListManagement/complianceRisk/view"}, {"id": 2608, "name": "导出Excel", "value": "threeListManagement/complianceRisk/export"}, {"id": 2609, "name": "导入", "value": "threeListManagement/complianceRisk/import"}], "isAvailable": null, "isShow": true}, {"id": 1994, "parentId": 1978, "path": "/threeListManagement/jobSpecifications", "redirect": null, "component": "hegui/prevention/threeListManagement/jobSpecifications.vue", "name": "threeListManagement/jobSpecifications", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"重点岗位合规职责清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2004, "parentId": 1994, "path": "/threeListManagement/jobSpecifications/edit", "redirect": null, "component": "hegui/prevention/threeListManagement/jobSpecifications_edit.vue", "name": "/threeListManagement/jobSpecifications/edit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"编辑重点岗位合规职责清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/jobSpecifications\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2535, "parentId": 1994, "path": "/threeListManagement/jobSpecifications/add", "redirect": null, "component": "hegui/prevention/threeListManagement/jobSpecifications_edit.vue", "name": "/threeListManagement/jobSpecifications/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增重点岗位合规职责清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/jobSpecifications\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2005, "parentId": 1994, "path": "/threeListManagement/jobSpecifications/detail", "redirect": null, "component": "hegui/prevention/threeListManagement/jobSpecifications_detail.vue", "name": "/threeListManagement/jobSpecifications/detail", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\": \"重点岗位合规职责清单详情\", \"icon\": \"\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": false, \"breadcrumb\": false, \"activeMenu\": \"/threeListManagement/jobSpecifications\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 1995, "name": "新增岗位职责", "value": "threeListManagement/jobSpecifications/add"}, {"id": 1997, "name": "删除", "value": "threeListManagement/jobSpecifications/delete"}, {"id": 1998, "name": "编辑", "value": "threeListManagement/jobSpecifications/edit"}, {"id": 1999, "name": "导出Excel", "value": "threeListManagement/jobSpecifications/export"}, {"id": 2000, "name": "批量导入", "value": "threeListManagement/jobSpecifications/import"}, {"id": 2003, "name": "查看", "value": "threeListManagement/jobSpecifications/view"}], "isAvailable": null, "isShow": true}, {"id": 2006, "parentId": 1978, "path": "/threeListManagement/operationFlow", "redirect": null, "component": "hegui/prevention/threeListManagement/operationFlow.vue", "name": "threeListManagement/operationFlow", "realName": null, "isMenu": true, "sort": 3, "mark": 1, "meta": "{\"title\":\"关键业务流程管控清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2016, "parentId": 2006, "path": "/threeListManagement/operationFlow/detail", "redirect": null, "component": "hegui/prevention/threeListManagement/operationFlow_detail.vue", "name": "/threeListManagement/operationFlow/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"关键业务流程管控清单详情\", \"icon\": \"\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": false, \"breadcrumb\": false, \"activeMenu\": \"/threeListManagement/operationFlow\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2538, "parentId": 2006, "path": "/threeListManagement/operationFlow/add", "redirect": null, "component": "hegui/prevention/threeListManagement/operationFlow_edit.vue", "name": "/threeListManagement/operationFlow/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增关键业务流程管控清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/operationFlow\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2017, "parentId": 2006, "path": "/threeListManagement/operationFlow/edit", "redirect": null, "component": "hegui/prevention/threeListManagement/operationFlow_edit.vue", "name": "/threeListManagement/operationFlow/edit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"编辑关键业务流程管控清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/operationFlow\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 2007, "name": "新增流程管控", "value": "threeListManagement/operationFlow/add"}, {"id": 2009, "name": "删除", "value": "threeListManagement/operationFlow/delete"}, {"id": 2010, "name": "编辑", "value": "threeListManagement/operationFlow/edit"}, {"id": 2011, "name": "导出Excel", "value": "threeListManagement/operationFlow/export"}, {"id": 2012, "name": "批量导入", "value": "threeListManagement/operationFlow/import"}, {"id": 2015, "name": "查看", "value": "threeListManagement/operationFlow/view"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2018, "parentId": 1977, "path": "/training", "redirect": null, "component": "Layout", "name": "training", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"合规培训体系\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2019, "parentId": 2018, "path": "/training/curriculum", "redirect": null, "component": "hegui/prevention/training/curriculum.vue", "name": "training/curriculum", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"培训课程库\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2020, "parentId": 2019, "path": "/training/curriculum/edit", "redirect": null, "component": "hegui/prevention/training/curriculum_edit.vue", "name": "/training/curriculum/edit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"编辑课程\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/curriculum\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2542, "parentId": 2019, "path": "/training/curriculum/add", "redirect": null, "component": "hegui/prevention/training/curriculum_edit.vue", "name": "/training/curriculum/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增课程\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/curriculum\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2021, "parentId": 2019, "path": "/training/curriculume/detail", "redirect": null, "component": "hegui/prevention/training/curriculum_detail.vue", "name": "/training/curriculum/detail", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"培训课程库详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/curriculum\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 2259, "name": "新增课程", "value": "curriculum/index/add"}, {"id": 2262, "name": "查看详情", "value": "curriculum/index/viewDetail"}, {"id": 2263, "name": "编辑", "value": "curriculum/index/edit"}], "isAvailable": null, "isShow": true}, {"id": 2371, "parentId": 2018, "path": "/training/examination", "redirect": null, "component": "hegui/prevention/training/examination/index.vue", "name": "/training/examination", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"考核学习\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2372, "parentId": 2371, "path": "/training/examination/detail", "redirect": null, "component": "hegui/prevention/training/examination/detail.vue", "name": "/training/examination/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"考核学习\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/examination\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2373, "parentId": 2371, "path": "/training/examination/center", "redirect": null, "component": "hegui/prevention/training/examination/center.vue", "name": "/training/examination/center", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"在线考试\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/examination\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2374, "parentId": 2371, "path": "/training/examination/error", "redirect": null, "component": "hegui/prevention/training/examination/error.vue", "name": "/training/examination/error", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"错题解析\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/examination\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2375, "parentId": 2371, "path": "/training/examination/addEdit", "redirect": null, "component": "hegui/prevention/training/examination/addEdit.vue", "name": "/training/examination/addEdit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"考核学习\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/examination\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 2503, "name": "新增考核认证", "value": "examination/index/addauth"}, {"id": 2504, "name": "查看详情", "value": "examination/index/detailauth"}], "isAvailable": null, "isShow": true}, {"id": 2022, "parentId": 2018, "path": "/training/plan", "redirect": null, "component": "hegui/prevention/training/plan.vue", "name": "training/plan", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"培训计划\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2023, "parentId": 2022, "path": "/training/plan/edit", "redirect": null, "component": "hegui/prevention/training/plan_edit.vue", "name": "/training/plan/edit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"编辑培训计划\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/plan\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2544, "parentId": 2022, "path": "/training/plan/add", "redirect": null, "component": "hegui/prevention/training/plan_edit.vue", "name": "/training/plan/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增培训计划\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/plan\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2024, "parentId": 2022, "path": "/training/plan/detail", "redirect": null, "component": "hegui/prevention/training/plan_detail.vue", "name": "/training/plan/detail", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"培训计划详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/plan\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 2353, "name": "新增培训计划", "value": "/training/plan/insert"}, {"id": 2354, "name": "编辑", "value": "/training/plan/edit"}, {"id": 2359, "name": "删除", "value": "/training/plan/delete"}], "isAvailable": null, "isShow": true}, {"id": 2028, "parentId": 2018, "path": "/training/report", "redirect": null, "component": "hegui/prevention/training/report.vue", "name": "training/report", "realName": null, "isMenu": true, "sort": 4, "mark": 1, "meta": "{\"title\": \"培训报告\", \"icon\": \"\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2030, "parentId": 2018, "path": "/training/learningCenter", "redirect": null, "component": "hegui/prevention/training/learningCenter.vue", "name": "training/learningCenter", "realName": null, "isMenu": true, "sort": 5, "mark": 1, "meta": "{\"title\": \"学习中心\", \"icon\": \"\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2403, "parentId": 2030, "path": "/training/learningCenter/detail", "redirect": null, "component": "hegui/prevention/training/learningCenter_detail.vue", "name": "/training/learningCenter/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"学习中心\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/learningCenter\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2033, "parentId": 2018, "path": "/training/consultingNews", "redirect": null, "component": "hegui/prevention/training/consultingNews/index.vue", "name": "training/consultingNews", "realName": null, "isMenu": true, "sort": 6, "mark": 1, "meta": "{\"title\":\"咨讯动态\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2034, "parentId": 2033, "path": "/training/consultingNews/detail", "redirect": null, "component": "hegui/prevention/training/consultingNews/detail.vue", "name": "/training/consultingNews/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"咨讯动态详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/consultingNews\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2035, "name": "复制资讯", "value": "consultingNews/detail/copy"}], "isAvailable": null, "isShow": true}, {"id": 2547, "parentId": 2033, "path": "/training/consultingNews/add", "redirect": null, "component": "hegui/prevention/training/consultingNews/addEdit.vue", "name": "/training/consultingNews/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增咨讯动态\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/consultingNews\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2038, "parentId": 2033, "path": "/training/consultingNews/addEdit", "redirect": null, "component": "hegui/prevention/training/consultingNews/addEdit.vue", "name": "/training/consultingNews/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"咨讯动态编辑\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/training/consultingNews\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 2319, "name": "发布资讯", "value": "consultingNews/index/add"}, {"id": 2321, "name": "新增分类", "value": "consultingNews/index/addCategory"}, {"id": 2322, "name": "标签管理", "value": "consultingNews/index/tagManage"}, {"id": 2323, "name": "分类管理", "value": "consultingNews/index/categoryManage"}, {"id": 2325, "name": "查看", "value": "consultingNews/index/view"}, {"id": 2326, "name": "编辑", "value": "consultingNews/index/edit"}, {"id": 2327, "name": "下线", "value": "consultingNews/index/offline"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2043, "parentId": 0, "path": "", "redirect": null, "component": "", "name": "/three", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"监控之翼\",\"icon\":\"ep:help\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2044, "parentId": 2043, "path": "/monitor/cockpit", "redirect": null, "component": "Layout", "name": "/monitor/cockpit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"合规驾驶舱\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2045, "parentId": 2044, "path": "/monitor/cockpit/1", "redirect": null, "component": "hegui/monitor/cockpit/realTimeMonitoring/index.vue", "name": "/monitor/cockpit/1", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"实时监控\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2050, "parentId": 2043, "path": "/monitor/examination", "redirect": null, "component": "Layout", "name": "/monitor/examination", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"合规审查\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2051, "parentId": 2050, "path": "/monitor/examination/contractReview", "redirect": null, "component": "hegui/monitor/examination/contractReview/index.vue", "name": "/monitor/examination/contractReview", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"合同审查\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2052, "parentId": 2051, "path": "/monitor/examination/contractReview/detail", "redirect": null, "component": "hegui/monitor/examination/contractReview/detail.vue", "name": "/monitor/examination/contractReview/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"合同审查详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/monitor/examination/contractReview\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2056, "name": "一键审查", "value": "contractReview/detail/startReview"}], "isAvailable": null, "isShow": true}, {"id": 2551, "parentId": 2051, "path": "/monitor/examination/contractReview/add", "redirect": null, "component": " hegui/monitor/examination/contractReview/addEdit.vue", "name": "/monitor/examination/contractReview/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"合同审查新增\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/monitor/examination/contractReview\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2614, "name": "保存", "value": "contractReview/addEdit/save"}], "isAvailable": null, "isShow": true}, {"id": 2058, "parentId": 2051, "path": "/monitor/examination/contractReview/addEdit", "redirect": null, "component": "hegui/monitor/examination/contractReview/addEdit.vue", "name": "/monitor/examination/contractReview/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"合同审查修改\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/monitor/examination/contractReview\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2615, "name": "保存", "value": "contractReview/addEdit/save"}], "isAvailable": null, "isShow": true}], "auths": [{"id": 2224, "name": "新增审查", "value": "contractReview/index/add"}, {"id": 2228, "name": "查看", "value": "contractReview/index/view"}, {"id": 2229, "name": "编辑", "value": "contractReview/index/edit"}, {"id": 2230, "name": "删除", "value": "contractReview/index/delete"}], "isAvailable": null, "isShow": true}, {"id": 2076, "parentId": 2050, "path": "/monitor/examination/decisionReview", "redirect": null, "component": "hegui/monitor/examination/decisionReview/index.vue", "name": "/monitor/examination/decisionReview", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"重大决策审查\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2077, "parentId": 2076, "path": "/monitor/examination/decisionReview/detail", "redirect": null, "component": "hegui/monitor/examination/decisionReview/detail.vue", "name": "/monitor/examination/decisionReview/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"重大决策审查详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/monitor/examination/decisionReview\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2080, "name": "审查记录", "value": "decisionReview/detail/reviewRecord"}, {"id": 2612, "name": "一键审核", "value": "decisionReview/detail/submitReview"}], "isAvailable": null, "isShow": true}, {"id": 2554, "parentId": 2076, "path": "/monitor/examination/decisionReview/add", "redirect": null, "component": "hegui/monitor/examination/decisionReview/addEdit.vue", "name": "/monitor/examination/decisionReview/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"重大决策审查新增\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/monitor/examination/decisionReview\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2083, "parentId": 2076, "path": "/monitor/examination/decisionReview/addEdit", "redirect": null, "component": "hegui/monitor/examination/decisionReview/addEdit.vue", "name": "/monitor/examination/decisionReview/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"重大决策审查修改\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/monitor/examination/decisionReview\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 2233, "name": "新增审查", "value": "decisionReview/index/add"}, {"id": 2234, "name": "导出数据", "value": "decisionReview/index/export"}, {"id": 2235, "name": "查看", "value": "decisionReview/index/view"}, {"id": 2236, "name": "编辑", "value": "decisionReview/index/edit"}, {"id": 2237, "name": "删除", "value": "decisionReview/index/delete"}], "isAvailable": null, "isShow": true}, {"id": 2093, "parentId": 2050, "path": "/monitor/examination/ohter", "redirect": null, "component": "hegui/monitor/examination/otherReviews/index.vue", "name": "/monitor/examination/ohter", "realName": null, "isMenu": true, "sort": 3, "mark": 1, "meta": "{\"title\":\"其他审查\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2094, "parentId": 2093, "path": "/monitor/examination/ohter/detail", "redirect": null, "component": "hegui/monitor/examination/otherReviews/detail.vue", "name": "/monitor/examination/ohter/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"其他审查详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/monitor/examination/ohter\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2254, "name": "一键审查", "value": "otherReviews/detail/submitReview"}, {"id": 2257, "name": "审查记录", "value": "otherReviews/detail/reviewRecords"}], "isAvailable": null, "isShow": true}, {"id": 2561, "parentId": 2093, "path": "/monitor/examination/ohter/add", "redirect": null, "component": "hegui/monitor/examination/otherReviews/addEdit.vue", "name": "/monitor/examination/ohter/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"其他审查新增\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/monitor/examination/ohter\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2095, "parentId": 2093, "path": "/monitor/examination/ohter/addEdit", "redirect": null, "component": "hegui/monitor/examination/otherReviews/addEdit.vue", "name": "/monitor/examination/ohter/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"其他审查修改\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/monitor/examination/ohter\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 2242, "name": "新增审查", "value": "otherReviews/index/add"}, {"id": 2244, "name": "查看", "value": "otherReviews/index/view"}, {"id": 2245, "name": "编辑", "value": "otherReviews/index/edit"}, {"id": 2246, "name": "删除", "value": "otherReviews/index/delete"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2096, "parentId": 2043, "path": "/monitor/intelligentReporting", "redirect": null, "component": "Layout", "name": "/monitor/intelligentReporting", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"智能举报管理\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2097, "parentId": 2096, "path": "/monitor/intelligentReporting/reportAcceptance", "redirect": null, "component": "hegui/monitor/intelligentReporting/reportAcceptance/index.vue", "name": "/monitor/intelligentReporting/reportAcceptance", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"举报受理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2098, "parentId": 2097, "path": "/monitor/intelligentReporting/reportAcceptance/detail", "redirect": null, "component": "hegui/monitor/intelligentReporting/reportAcceptance/detail.vue", "name": "/monitor/intelligentReporting/reportAcceptance/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"举报详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/monitor/intelligentReporting/reportAcceptance\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2583, "parentId": 2097, "path": "/monitor/intelligentReporting/reportAcceptance/addEdit", "redirect": null, "component": "hegui/monitor/intelligentReporting/reportAcceptance/addEdit.vue", "name": "/monitor/intelligentReporting/reportAcceptance/addEdit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增举报\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/monitor/intelligentReporting/reportAcceptance\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 2424, "name": "查看", "value": "reportAcceptance/index/view"}, {"id": 2524, "name": "新建举报", "value": "reportAcceptance/index/add"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2103, "parentId": 0, "path": "", "redirect": null, "component": "", "name": "/four", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"应对之翼\",\"icon\":\"ep:monitor\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2104, "parentId": 2103, "path": "/respond/violationIssues", "redirect": null, "component": "Layout", "name": "/respond/violationIssues", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"违规问题调查\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2105, "parentId": 2104, "path": "/respond/violationIssues/taskManagement", "redirect": null, "component": "hegui/respond/violationIssues/taskManagement/index.vue", "name": "/respond/violationIssues/taskManagement", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"调查任务管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2106, "parentId": 2105, "path": "/respond/violationIssues/taskManagement/detail", "redirect": null, "component": "hegui/respond/violationIssues/taskManagement/detail.vue", "name": "/respond/violationIssues/taskManagement/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"调查任务管理详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/violationIssues/taskManagement\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2520, "parentId": 2105, "path": "/respond/violationIssues/taskManagement/addEdit", "redirect": null, "component": "hegui/respond/violationIssues/taskManagement/addEdit.vue", "name": "/respond/violationIssues/taskManagement/addEdit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"调查任务管理编辑\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/violationIssues/taskManagement\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2585, "parentId": 2105, "path": "/respond/violationIssues/taskManagement/taskReport", "redirect": null, "component": "hegui/respond/violationIssues/taskManagement/taskReport.vue", "name": "/respond/violationIssues/taskManagement/taskReport", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"调查报告\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2589, "parentId": 2105, "path": "/respond/violationIssues/taskManagement/taskReview", "redirect": null, "component": "hegui/respond/violationIssues/taskManagement/taskReview.vue", "name": "/respond/violationIssues/taskManagement/taskReview", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"调查记录\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/violationIssues/taskManagement\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2591, "parentId": 2105, "path": "/respond/violationIssues/taskManagement/add", "redirect": null, "component": "hegui/respond/violationIssues/taskManagement/addEdit.vue", "name": "/respond/violationIssues/taskManagement/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"调查任务管理新增\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/violationIssues/taskManagement\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 2164, "name": "查看", "value": "violationIssues/taskManagement/index/view"}, {"id": 2519, "name": "新增调查任务", "value": "violationIssues/taskManagement/index/add"}, {"id": 2590, "name": "编辑", "value": "violationIssues/taskManagement/index/edit"}], "isAvailable": null, "isShow": true}, {"id": 2107, "parentId": 2104, "path": "/respond/violationIssues/reportManagement", "redirect": null, "component": "hegui/respond/violationIssues/reportManagement/index.vue", "name": "/respond/violationIssues/reportManagement", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"调查报告管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2108, "parentId": 2107, "path": "/respond/violationIssues/reportManagement/addEdit", "redirect": null, "component": "hegui/respond/violationIssues/reportManagement/addEdit.vue", "name": "/respond/violationIssues/reportManagement/addEdit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"调查报告管理-编辑\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/violationIssues/reportManagement\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2564, "parentId": 2107, "path": "/respond/violationIssues/reportManagement/add", "redirect": null, "component": "hegui/respond/violationIssues/reportManagement/addEdit.vue", "name": "/respond/violationIssues/reportManagement/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"调查报告管理-新增\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/violationIssues/reportManagement\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2605, "parentId": 2107, "path": "/respond/violationIssues/reportManagement/detail", "redirect": null, "component": "hegui/respond/violationIssues/reportManagement/detail.vue", "name": "/respond/violationIssues/reportManagement/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"调查报告详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": null}], "auths": [{"id": 2601, "name": "新增报告", "value": "violationIssues/reportManagement/index/add"}, {"id": 2602, "name": "查看", "value": "violationIssues/reportManagement/index/view"}, {"id": 2603, "name": "编辑", "value": "violationIssues/reportManagement/index/edit"}, {"id": 2604, "name": "删除", "value": "violationIssues/reportManagement/index/delete"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2109, "parentId": 2103, "path": "/respond/accountability", "redirect": null, "component": "Layout", "name": "/respond/accountability", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"责任追究处理\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2110, "parentId": 2109, "path": "/respond/accountability/handlingMeasures", "redirect": null, "component": "hegui/respond/accountability/handlingMeasures/index.vue", "name": "/respond/accountability/handlingMeasures", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"处理措施管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2111, "parentId": 2110, "path": "/respond/accountability/handlingMeasures/detail", "redirect": null, "component": "hegui/respond/accountability/handlingMeasures/detail.vue", "name": "/respond/accountability/handlingMeasures/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"处理措施管理详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/accountability/handlingMeasures\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2568, "parentId": 2110, "path": "/respond/accountability/handlingMeasures/add", "redirect": null, "component": "hegui/respond/accountability/handlingMeasures/addEdit.vue", "name": "/respond/accountability/handlingMeasures/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增处理措施管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/accountability/handlingMeasures\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2112, "parentId": 2110, "path": "/respond/accountability/handlingMeasures/addEdit", "redirect": null, "component": "hegui/respond/accountability/handlingMeasures/addEdit.vue", "name": "/respond/accountability/handlingMeasures/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"编辑措施管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/accountability/handlingMeasures\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 2182, "name": "新增处理措施", "value": "handlingMeasures/index/add"}, {"id": 2186, "name": "重置", "value": "handlingMeasures/index/reset"}, {"id": 2187, "name": "查看", "value": "handlingMeasures/index/view"}, {"id": 2188, "name": "编辑", "value": "handlingMeasures/index/edit"}], "isAvailable": null, "isShow": true}, {"id": 2115, "parentId": 2109, "path": "/respond/accountability/rectificationTracking", "redirect": null, "component": "hegui/respond/accountability/rectificationTracking/index.vue", "name": "/respond/accountability/rectificationTracking", "realName": null, "isMenu": true, "sort": 3, "mark": 1, "meta": "{\"title\":\"整改跟踪\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2116, "parentId": 2115, "path": "/respond/accountability/rectificationTracking/detail", "redirect": null, "component": "hegui/respond/accountability/rectificationTracking/detail.vue", "name": "/respond/accountability/rectificationTracking/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"处理措施管理详情\", \"icon\": \"\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": false, \"breadcrumb\": false, \"activeMenu\": \"/respond/accountability/rectificationTracking\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2595, "parentId": 2115, "path": "/respond/accountability/rectificationTracking/addEdit", "redirect": null, "component": "hegui/respond/accountability/rectificationTracking/addEdit.vue", "name": "/respond/accountability/rectificationTracking/addEdit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增整改追踪\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/accountability/rectificationTracking\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [{"id": 2211, "name": "新增整改项目", "value": "rectificationTracking/add"}, {"id": 2215, "name": "查看", "value": "rectificationTracking/view"}, {"id": 2330, "name": "删除", "value": "rectificationTracking/delete"}, {"id": 2331, "name": "编辑", "value": "rectificationTracking/edit"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2117, "parentId": 2103, "path": "/respond/improveAndOptimize", "redirect": null, "component": "Layout", "name": "/respond/improveAndOptimize", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"持续改进优化\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2118, "parentId": 2117, "path": "/respond/improveAndOptimize/experienceAndLesson", "redirect": null, "component": "hegui/respond/improveAndOptimize/experienceAndLesson/index.vue", "name": "/respond/improveAndOptimize/experienceAndLesson", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"经验教训库\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2410, "parentId": 2118, "path": "/respond/improveAndOptimize/experienceAndLesson/addEdit", "redirect": null, "component": "hegui/respond/improveAndOptimize/experienceAndLesson/addEdit.vue", "name": "/respond/improveAndOptimize/experienceAndLesson/addEdit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"编辑经验教训库\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/improveAndOptimize/experienceAndLesson\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2450, "parentId": 2118, "path": "/respond/improveAndOptimize/experienceAndLesson/detail", "redirect": null, "component": "hegui/respond/improveAndOptimize/experienceAndLesson/detail.vue", "name": "/respond/improveAndOptimize/experienceAndLesson/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"经验教训库-详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/improveAndOptimize/experienceAndLesson\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2512, "name": "编辑", "value": "experienceAndLesson/detail/edit"}], "isAvailable": null, "isShow": true}, {"id": 2571, "parentId": 2118, "path": "/respond/improveAndOptimize/experienceAndLesson/add", "redirect": null, "component": "hegui/respond/improveAndOptimize/experienceAndLesson/addEdit.vue", "name": "/respond/improveAndOptimize/experienceAndLesson/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"经验教训库新增\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/improveAndOptimize/experienceAndLesson\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2572, "name": "保存", "value": "experienceAndLesson/add/save"}], "isAvailable": null, "isShow": true}], "auths": [{"id": 2222, "name": "查看", "value": "experienceAndLesson/index/view"}, {"id": 2223, "name": "编辑", "value": "experienceAndLesson/index/edit"}, {"id": 2451, "name": "新增", "value": "experienceAndLesson/index/save"}, {"id": 2452, "name": "查询", "value": "experienceAndLesson/index/inquire"}, {"id": 2453, "name": "重置", "value": "experienceAndLesson/index/reset"}, {"id": 2454, "name": "删除", "value": "experienceAndLesson/index/delete"}], "isAvailable": null, "isShow": true}, {"id": 2119, "parentId": 2117, "path": "/respond/improveAndOptimize/improvementMeasures", "redirect": null, "component": "hegui/respond/improveAndOptimize/improvementMeasures/index.vue", "name": "/respond/improveAndOptimize/improvementMeasures", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"改进措施管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2376, "parentId": 2119, "path": "/respond/improveAndOptimize/improvementMeasures/detail", "redirect": null, "component": "hegui/respond/improveAndOptimize/improvementMeasures/detail.vue", "name": "/respond/improveAndOptimize/improvementMeasures/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"改进措施详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/improveAndOptimize/improvementMeasures\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2402, "parentId": 2119, "path": "/respond/improveAndOptimize/improvementMeasures/addEdit", "redirect": null, "component": "hegui/respond/improveAndOptimize/improvementMeasures/addEdit.vue", "name": "/respond/improveAndOptimize/improvementMeasures/addEdit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"改进措施编辑\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/improveAndOptimize/improvementMeasures\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2513, "name": "保存", "value": "improvementMeasures/addEdit/save"}], "isAvailable": null, "isShow": true}, {"id": 2573, "parentId": 2119, "path": "/respond/improveAndOptimize/improvementMeasures/add", "redirect": null, "component": "hegui/respond/improveAndOptimize/improvementMeasures/addEdit.vue", "name": "/respond/improveAndOptimize/improvementMeasures/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增改进措施\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/improveAndOptimize/improvementMeasures\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2574, "name": "保存", "value": "improvementMeasures/add/save"}], "isAvailable": null, "isShow": true}], "auths": [{"id": 2440, "name": "新增", "value": "improvementMeasures/index/addMeasure"}, {"id": 2471, "name": "查看", "value": "improvementMeasures/index/view"}, {"id": 2472, "name": "编辑", "value": "improvementMeasures/index/edit"}, {"id": 2473, "name": "删除", "value": "improvementMeasures/index/delete"}], "isAvailable": null, "isShow": true}, {"id": 2121, "parentId": 2117, "path": "/respond/improveAndOptimize/optimizationReport", "redirect": null, "component": "hegui/respond/improveAndOptimize/optimizationReport/index.vue", "name": "/respond/improveAndOptimize/optimizationReport", "realName": null, "isMenu": true, "sort": 4, "mark": 1, "meta": "{\"title\":\"优化报告\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2122, "parentId": 2121, "path": "/respond/improveAndOptimize/optimizationReport/detail", "redirect": null, "component": "hegui/respond/improveAndOptimize/optimizationReport/detail.vue", "name": "/respond/improveAndOptimize/optimizationReport/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"优化报告详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/improveAndOptimize/optimizationReport\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2599, "name": "编辑", "value": "optimizationReport/detail/edit"}], "isAvailable": null, "isShow": true}, {"id": 2575, "parentId": 2121, "path": " /respond/improveAndOptimize/optimizationReport/add", "redirect": null, "component": "hegui/respond/improveAndOptimize/optimizationReport/addEdit.vue", "name": " /respond/improveAndOptimize/optimizationReport/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增优化报告\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/improveAndOptimize/optimizationReport\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2576, "name": "保存草稿", "value": "optimizationReport/add/saveDraft"}, {"id": 2577, "name": "提交审核", "value": "optimizationReport/add/submitReview"}], "isAvailable": null, "isShow": true}, {"id": 2123, "parentId": 2121, "path": "/respond/improveAndOptimize/optimizationReport/addEdit", "redirect": null, "component": "hegui/respond/improveAndOptimize/optimizationReport/addEdit.vue", "name": "/respond/improveAndOptimize/optimizationReport/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"修改优化报告\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/respond/improveAndOptimize/optimizationReport\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2445, "name": "保存草稿", "value": "optimizationReport/addEdit/saveDraft"}, {"id": 2446, "name": "提交审核", "value": "optimizationReport/addEdit/submitReview"}], "isAvailable": null, "isShow": true}], "auths": [{"id": 2444, "name": "新增报告", "value": "optimizationReport/index/add"}, {"id": 2509, "name": "查看", "value": "optimizationReport/index/view"}, {"id": 2510, "name": "编辑", "value": "optimizationReport/index/edit"}, {"id": 2511, "name": "删除", "value": "optimizationReport/index/delete"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2124, "parentId": 0, "path": "", "redirect": null, "component": "", "name": "/five", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"系统设置\",\"icon\":\"ep:setting\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2125, "parentId": 2124, "path": "/systemSettings/enterpriseInformation", "redirect": null, "component": "Layout", "name": "/systemSettings/enterpriseInformation", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\": \"企业信息\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2126, "parentId": 2125, "path": "/systemSettings/enterpriseInformation/essentialInformation", "redirect": null, "component": "hegui/systemSettings/enterpriseInformation/essentialInformation/index.vue", "name": "/systemSettings/enterpriseInformation/essentialInformation", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"基本信息\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 2387, "name": "提交", "value": "essentialInformation/index/submit"}, {"id": 2388, "name": "取消", "value": "essentialInformation/index/cancel"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2128, "parentId": 2124, "path": "/systemSettings/usersAndPermissions", "redirect": null, "component": "Layout", "name": "/systemSettings/usersAndPermissions", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\": \"用户与权限\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2129, "parentId": 2128, "path": "/systemSettings/usersAndPermissions/userManagement", "redirect": null, "component": "hegui/systemSettings/usersAndPermissions/userManagement/index.vue", "name": "/systemSettings/usersAndPermissions/userManagement", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"用户管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2130, "parentId": 2129, "path": "/systemSettings/usersAndPermissions/userManagement/addEdit", "redirect": null, "component": "hegui/systemSettings/usersAndPermissions/userManagement/addEdit.vue", "name": "/systemSettings/usersAndPermissions/userManagement/addEdit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"编辑用户\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/systemSettings/usersAndPermissions/userManagement\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2463, "name": "更新", "value": "userManagement/dialog/confirm"}], "isAvailable": null, "isShow": true}, {"id": 2578, "parentId": 2129, "path": "/systemSettings/usersAndPermissions/userManagement/add", "redirect": null, "component": "hegui/systemSettings/usersAndPermissions/userManagement/addEdit.vue", "name": "/systemSettings/usersAndPermissions/userManagement/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增用户\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/systemSettings/usersAndPermissions/userManagement\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2579, "name": "创建", "value": "userManagement/dialog/addconfirm"}], "isAvailable": null, "isShow": true}], "auths": [{"id": 2273, "name": "新增用户", "value": "userManagement/index/add"}, {"id": 2275, "name": "导入用户", "value": "userManagement/index/import"}, {"id": 2277, "name": "查询", "value": "userManagement/index/search"}, {"id": 2278, "name": "重置", "value": "userManagement/index/reset"}, {"id": 2279, "name": "编辑", "value": "userManagement/index/edit"}, {"id": 2280, "name": "删除", "value": "userManagement/index/delete"}, {"id": 2281, "name": "批量启用", "value": "userManagement/index/batchEnable"}, {"id": 2282, "name": "批量禁用", "value": "userManagement/index/batchDisable"}, {"id": 2283, "name": "批量删除", "value": "userManagement/index/batchDelete"}, {"id": 2284, "name": "取消选择", "value": "userManagement/index/cancelSelection"}, {"id": 2448, "name": "关闭", "value": "userManagement/dialog/cancel"}, {"id": 2449, "name": "确定", "value": "userManagement/dialog/confirm"}], "isAvailable": null, "isShow": true}, {"id": 2131, "parentId": 2128, "path": "/systemSettings/usersAndPermissions/roleManagement", "redirect": null, "component": "hegui/systemSettings/usersAndPermissions/roleManagement/index.vue", "name": "/systemSettings/usersAndPermissions/roleManagement", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"角色管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 2287, "name": "新增角色", "value": "roleManagement/index/add"}, {"id": 2289, "name": "查询", "value": "roleManagement/index/search"}, {"id": 2290, "name": "重置", "value": "roleManagement/index/reset"}, {"id": 2291, "name": "编辑", "value": "roleManagement/index/edit"}, {"id": 2292, "name": "分配权限", "value": "roleManagement/index/assignPermissions"}, {"id": 2293, "name": "启用", "value": "roleManagement/index/toggleStatus"}, {"id": 2294, "name": "删除", "value": "roleManagement/index/delete"}, {"id": 2299, "name": "导入角色", "value": "roleManagement/index/import"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2133, "parentId": 2124, "path": "/systemSettings/organizationalStructure", "redirect": null, "component": "Layout", "name": "/systemSettings/organizationalStructure", "realName": null, "isMenu": true, "sort": 3, "mark": 1, "meta": "{\"title\": \"组织架构\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2135, "parentId": 2133, "path": "/systemSettings/organizationalStructure/departmentManagement", "redirect": null, "component": "hegui/systemSettings/organizationalStructure/departmentManagement/index.vue", "name": "/systemSettings/organizationalStructure/departmentManagement", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"部门管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 2300, "name": "查询", "value": "departmentManagement/index/search"}, {"id": 2301, "name": "新增部门", "value": "departmentManagement/index/add"}, {"id": 2302, "name": "导入部门", "value": "departmentManagement/index/import"}, {"id": 2304, "name": "重置", "value": "departmentManagement/index/reset"}, {"id": 2305, "name": "编辑", "value": "departmentManagement/index/edit"}, {"id": 2306, "name": "新增", "value": "departmentManagement/index/addChild"}, {"id": 2307, "name": "删除", "value": "departmentManagement/index/delete"}, {"id": 2308, "name": "取消选择", "value": "departmentManagement/index/cancelSelection"}], "isAvailable": null, "isShow": true}, {"id": 2136, "parentId": 2133, "path": "/systemSettings/organizationalStructure/postManagement", "redirect": null, "component": "hegui/systemSettings/organizationalStructure/postManagement/index.vue", "name": "/systemSettings/organizationalStructure/postManagement", "realName": null, "isMenu": true, "sort": 3, "mark": 1, "meta": "{\"title\":\"岗位管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 2309, "name": "新增岗位", "value": "postManagement/index/add"}, {"id": 2310, "name": "导入岗位", "value": "postManagement/index/import"}, {"id": 2311, "name": "导出", "value": "postManagement/index/export"}, {"id": 2312, "name": "重置", "value": "postManagement/index/reset"}, {"id": 2313, "name": "筛选", "value": "postManagement/index/filter"}, {"id": 2315, "name": "编辑", "value": "postManagement/index/edit"}, {"id": 2318, "name": "删除", "value": "postManagement/index/delete"}], "isAvailable": null, "isShow": true}, {"id": 2137, "parentId": 2133, "path": "/systemSettings/organizationalStructure/menuManagement", "redirect": null, "component": "hegui/systemSettings/organizationalStructure/menuManagement/index.vue", "name": "/systemSettings/organizationalStructure/menuManagement", "realName": null, "isMenu": true, "sort": 4, "mark": 1, "meta": "{\"title\":\"菜单管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2138, "parentId": 2137, "path": "/systemSettings/organizationalStructure/menuManagement/addEdit", "redirect": null, "component": "hegui/systemSettings/organizationalStructure/menuManagement/addEdit.vue", "name": "/systemSettings/organizationalStructure/menuManagement/addEdit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"编辑菜单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/systemSettings/organizationalStructure/menuManagement\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2464, "name": "保存", "value": "menuManagement/addEdit/save"}, {"id": 2465, "name": "添加权限", "value": "menuManagement/addEdit/addPermissions"}], "isAvailable": null, "isShow": true}, {"id": 2580, "parentId": 2137, "path": "/systemSettings/organizationalStructure/menuManagement/add", "redirect": null, "component": "hegui/systemSettings/organizationalStructure/menuManagement/addEdit.vue", "name": "/systemSettings/organizationalStructure/menuManagement/add", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增菜单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/systemSettings/organizationalStructure/menuManagement\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2581, "name": "创建", "value": "menuManagement/add/save"}, {"id": 2582, "name": "添加权限", "value": "menuManagement/add/addPermissions"}], "isAvailable": null, "isShow": true}], "auths": [{"id": 2459, "name": "新增根菜单", "value": "menuManagement/index/addrootMenu"}, {"id": 2460, "name": "编辑", "value": "menuManagement/index/edit"}, {"id": 2461, "name": "添加子菜单", "value": "menuManagement/index/addsubmenus"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2146, "parentId": 2124, "path": "/systemSettings/operationLog", "redirect": null, "component": "Layout", "name": "/systemSettings/operationLog", "realName": null, "isMenu": true, "sort": 6, "mark": 1, "meta": "{\"title\": \"操作日志\", \"icon\": \"i-heroicons-solid:menu-alt-3\", \"activeIcon\": \"\", \"defaultOpened\": false, \"permanent\": false, \"menu\": true, \"breadcrumb\": true, \"activeMenu\": \"\", \"cache\": [], \"noCache\": [], \"badge\": \"\", \"link\": \"\", \"iframe\": \"\", \"copyright\": false, \"paddingBottom\": \"0px\", \"sidebar\": true}", "children": [{"id": 2147, "parentId": 2146, "path": "/systemSettings/operationLog/logQuery", "redirect": null, "component": "hegui/systemSettings/operationLog/logQuery/index.vue", "name": "/systemSettings/operationLog/logQuery", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"日志查询\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 2411, "name": "导出日志", "value": "operationLog/logQuery/index/export"}, {"id": 2412, "name": "详情", "value": "operationLog/logQuery/index/detail"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2366, "parentId": 0, "path": "", "redirect": null, "component": "", "name": "", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"个人中心\",\"icon\":\"ep:avatar\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2367, "parentId": 2366, "path": "/personalCenter/personalInformation", "redirect": null, "component": "Layout", "name": "/personalCenter/personalInformation", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"个人信息\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2368, "parentId": 2367, "path": "/personalCenter/personalInformation/basicInformation", "redirect": null, "component": "hegui/personalCenter/personalInformation/basicInformation/index.vue", "name": "/personalCenter/personalInformation/basicInformation", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"基本资料\",\"icon\":\"ep:user\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 2478, "name": "编辑资料", "value": "basicInformation/index/editInfomation"}], "isAvailable": null, "isShow": true}, {"id": 2369, "parentId": 2367, "path": "/personalCenter/personalInformation/basicInformation/detail", "redirect": null, "component": "hegui/personalCenter/personalInformation/basicInformation/detail.vue", "name": "/personalCenter/personalInformation/basicInformation/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"基本资料编辑\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/personalCenter/personalInformation/basicInformation\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2517, "name": "保存", "value": "basicInformation/detail/save"}], "isAvailable": null, "isShow": true}, {"id": 2370, "parentId": 2367, "path": "/personalCenter/personalInformation/passwordModification", "redirect": null, "component": "hegui/personalCenter/personalInformation/passwordModification/index.vue", "name": "/personalCenter/personalInformation/passwordModification", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"密码修改\",\"icon\":\"ep:hide\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [{"id": 2518, "name": "保存", "value": "Respwd/index/save"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2466, "parentId": 2366, "path": "/personalCenter/messageNotification", "redirect": null, "component": "Layout", "name": "/personalCenter/messageNotification", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"消息通知\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2467, "parentId": 2466, "path": "/personalCenter/messageNotification/allMessages", "redirect": null, "component": "hegui/personalCenter/messageNotification/allMessages/index.vue", "name": "/personalCenter/messageNotification/allMessages", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"全部消息\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 2479, "name": "全部标为已读", "value": "allMessages/index/batchMarkAsRead"}, {"id": 2480, "name": "清空已读消息", "value": "allMessages/index/clearReadMessages"}, {"id": 2481, "name": "标为已读", "value": "allMessages/index/toggleStatus"}, {"id": 2482, "name": "删除", "value": "allMessages/index/deleteMessage"}], "isAvailable": null, "isShow": true}, {"id": 2468, "parentId": 2466, "path": "/personalCenter/messageNotification/systemNotification", "redirect": null, "component": "hegui/personalCenter/messageNotification/systemNotification/index.vue", "name": "/personalCenter/messageNotification/systemNotification", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"系统通知\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 2483, "name": "全部标为已读", "value": "systemNotification/index/batchMarkAsRead"}, {"id": 2484, "name": "清空已读消息", "value": "systemNotification/index/clearReadMessages"}, {"id": 2485, "name": "标为已读", "value": "systemNotification/index/toggleStatus"}, {"id": 2486, "name": "删除", "value": "systemNotification/index/deleteMessage"}], "isAvailable": null, "isShow": true}, {"id": 2469, "parentId": 2466, "path": "/personalCenter/messageNotification/toDoNotification", "redirect": null, "component": "hegui/personalCenter/messageNotification/toDoNotification/index.vue", "name": "/personalCenter/messageNotification/toDoNotification", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"待办通知\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 2487, "name": "全部标为已读", "value": "toDoNotification/index/batchMarkAsRead"}, {"id": 2488, "name": "清空已读消息", "value": "toDoNotification/index/clearReadMessages"}, {"id": 2489, "name": "标为已读", "value": "toDoNotification/index/toggleStatus"}, {"id": 2490, "name": "删除", "value": "toDoNotification/index/deleteMessage"}], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}, {"id": 2474, "parentId": 2366, "path": "/personalCenter/toDoList", "redirect": null, "component": "Layout", "name": "/personalCenter/toDoList", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"待办事项\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 2475, "parentId": 2474, "path": "/personalCenter/toDoList/toDoTasks", "redirect": null, "component": "hegui/personalCenter/toDoList/toDoTasks/index.vue", "name": "/personalCenter/toDoList/toDoTasks", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"待办任务\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [{"id": 2493, "name": "处理", "value": "toDoTasks/index/handle"}, {"id": 2494, "name": "详情", "value": "toDoTasks/index/viewDetail"}, {"id": 2495, "name": "转发", "value": "toDoTasks/index/transferTask"}], "isAvailable": null, "isShow": true}, {"id": 2477, "parentId": 2474, "path": "/personalCenter/toDoList/completedTasks", "redirect": null, "component": "hegui/personalCenter/toDoList/completedTasks/index.vue", "name": "/personalCenter/toDoList/completedTasks", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"已办任务\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [], "auths": [], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}], "auths": [], "isAvailable": null, "isShow": true}]