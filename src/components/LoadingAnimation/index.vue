<script setup lang="ts">
import { computed, onUnmounted, ref, watch } from 'vue'

defineOptions({
  name: 'LoadingAnimation',
})

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  text: '正在处理中...',
  fullscreen: true,
  progress: 0,
  logoUrl: 'https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png',
  autoProgress: true,
})

const emit = defineEmits<{
  imageError: []
}>()

interface Props {
  visible?: boolean
  text?: string
  fullscreen?: boolean
  progress?: number
  logoUrl?: string
  autoProgress?: boolean
}

const imageError = ref(false)
const internalProgress = ref(0)
const estimatedTime = ref(5)
const loadingSubText = ref('')
const progressInterval = ref<any>(null)

function handleImageError() {
  imageError.value = true
  emit('imageError')
}

// 模拟真实进度条
function simulateProgress() {
  internalProgress.value = 0
  estimatedTime.value = Math.floor(Math.random() * 3) + 4

  const stages = [
    { end: 20, speed: 0.8, texts: ['正在初始化...', '正在连接服务器...'] },
    { end: 45, speed: 0.6, texts: ['正在分析数据...', '正在处理请求...'] },
    { end: 70, speed: 0.4, texts: ['正在生成结果...', '正在优化内容...'] },
    { end: 90, speed: 0.2, texts: ['正在完善细节...', '即将完成...'] },
    { end: 95, speed: 0.1, texts: ['最后检查...', '准备就绪...'] },
  ]

  let currentStage = 0

  progressInterval.value = setInterval(() => {
    if (internalProgress.value < 95 && currentStage < stages.length) {
      const stage = stages[currentStage]
      const baseIncrement = stage.speed + Math.random() * 0.3
      const randomFactor = Math.random() < 0.3 ? 0.5 : 1
      const increment = baseIncrement * randomFactor

      internalProgress.value = Math.min(stage.end, internalProgress.value + increment)

      const progressRatio = internalProgress.value / 100
      const timeReduction = 0.05 + (progressRatio * 0.1)
      estimatedTime.value = Math.max(0, estimatedTime.value - timeReduction)

      if (Math.random() < 0.15) {
        const stageTexts = stage.texts
        loadingSubText.value = stageTexts[Math.floor(Math.random() * stageTexts.length)]
      }

      if (internalProgress.value >= stage.end) {
        currentStage++
      }
    }
  }, 150 + Math.random() * 100)
}

watch(() => props.visible, (newVal: boolean) => {
  if (newVal && props.autoProgress) {
    simulateProgress()
  }
  else if (!newVal && progressInterval.value) {
    clearInterval(progressInterval.value)
  }
})

onUnmounted(() => {
  if (progressInterval.value) {
    clearInterval(progressInterval.value)
  }
})

const currentProgress = computed(() => {
  if (props.autoProgress) {
    return internalProgress.value
  }
  return props.progress
})
</script>

<template>
  <div
    v-if="visible"
    class="loading-animation"
    :class="{
      'loading-animation--fullscreen': fullscreen,
      'loading-animation--local': !fullscreen,
    }"
  >
    <!-- 遮罩层 -->
    <div class="loading-mask" />

    <!-- 进度条模式 -->
    <div class="progress-modal">
      <div class="progress-modal-content">
        <!-- Logo with rotation animation -->
        <div class="progress-logo-container">
          <img
            :src="logoUrl"
            alt="Logo"
            class="progress-logo-image"
            @error="handleImageError"
          >
        </div>

        <!-- Title -->
        <h3 class="progress-title">
          {{ text }}
        </h3>
        <p class="progress-subtitle">
          {{ loadingSubText || '正在进行智能分析，请稍候' }}
        </p>

        <!-- Progress bar -->
        <div class="progress-bar-container">
          <div class="progress-bar-info">
            <span>任务进度</span>
            <span>{{ Math.round(currentProgress) }}%</span>
          </div>
          <div class="progress-bar-bg">
            <div
              class="progress-bar-fill-new"
              :style="{ width: `${currentProgress}%` }"
            />
          </div>
        </div>

        <!-- Estimated time (暂时隐藏) -->
        <!-- <p v-if="autoProgress" class="progress-estimated-time">
          预计剩余时间: {{ Math.ceil(estimatedTime) }}秒
        </p> -->
      </div>
    </div>
  </div>
</template>

<style scoped>
.loading-animation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-animation--fullscreen {
  position: fixed;
}

.loading-animation--local {
  position: absolute;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

/* 进度条模式样式 */
.progress-modal {
  position: relative;
  width: 450px;
  overflow: hidden;
  border-radius: 12px;
  background: white;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.3s ease-out;
  z-index: 1;
}

.progress-modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 3.5rem 3rem;
}

.progress-logo-container {
  margin-bottom: 0rem;
}

.progress-logo-image {
  height: 4rem;
  width: 4rem;
  border-radius: 50%;
  object-fit: cover;
}

.progress-title {
  margin-bottom: 0rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
}

.progress-subtitle {
  margin-bottom: 0rem;
  color: #6b7280;
  text-align: center;
  font-size: 0.875rem;
}

.progress-bar-container {
  width: 100%;
  margin-bottom: 1rem;
}

.progress-bar-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.progress-bar-bg {
  height: 8px;
  width: 100%;
  overflow: hidden;
  margin-top: 5px;
  border-radius: 9999px;
  background-color: #e5e7eb;
}

.progress-bar-fill-new {
  height: 100%;
  background: linear-gradient(45deg, #60a5fa, #a855f7, #ec4899);
  background-size: 200% 100%;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 9999px;
  position: relative;
  animation: gradientShift 3s ease-in-out infinite;
}

.progress-bar-fill-new::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 9999px;
  animation: progressShimmerNew 2s ease-in-out infinite;
}

.progress-bar-fill-new::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: progressPulse 1s ease-in-out infinite;
}

.progress-estimated-time {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  text-align: center;
}

/* 动画定义 */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes progressShimmerNew {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes progressPulse {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.5);
  }
}
</style>
