<template>
  <div class="test-page">
    <el-card>
      <template #header>
        <h2>违规问题调查选择器测试页面</h2>
      </template>

      <div class="test-section">
        <h3>基础用法测试</h3>
        <ViolationInvestigationSelector
          v-model="testValue1"
          placeholder="请选择违规问题调查"
          @change="handleChange1"
        />
        <div class="result">
          <p><strong>选中值：</strong>{{ testValue1 || '未选择' }}</p>
          <p><strong>选中行：</strong>{{ JSON.stringify(selectedRow1, null, 2) }}</p>
        </div>
      </div>

      <el-divider />

      <div class="test-section">
        <h3>自定义字段测试</h3>
        <ViolationInvestigationSelector
          v-model="testValue2"
          value-key="investigateCode"
          display-key="title"
          placeholder="请选择违规问题调查（返回编号）"
          @change="handleChange2"
        />
        <div class="result">
          <p><strong>选中值（编号）：</strong>{{ testValue2 || '未选择' }}</p>
          <p><strong>选中行：</strong>{{ JSON.stringify(selectedRow2, null, 2) }}</p>
        </div>
      </div>

      <el-divider />

      <div class="test-section">
        <h3>清除功能测试</h3>
        <p>选择一个值后，输入框右侧会出现 ❌ 按钮，点击可清除</p>
        <ViolationInvestigationSelector
          v-model="presetValue"
          placeholder="测试清除功能"
          @change="handlePresetChange"
        />
        <div class="result">
          <p><strong>当前值：</strong>{{ presetValue || '未选择' }}</p>
          <p><strong>选中行：</strong>{{ selectedPresetRow ? selectedPresetRow.title : '无' }}</p>
          <el-button type="primary" @click="presetValue = 1">设置测试值(ID=1)</el-button>
          <el-button @click="presetValue = null">程序清空</el-button>
        </div>
      </div>

      <el-divider />

      <div class="test-section">
        <h3>表单集成测试</h3>
        <el-form :model="formData" label-width="120px">
          <el-form-item label="关联调查：">
            <ViolationInvestigationSelector
              v-model="formData.investigationId"
              placeholder="请选择关联的违规问题调查"
              @change="handleFormChange"
            />
          </el-form-item>
          <el-form-item label="处理人员：">
            <el-input v-model="formData.handler" placeholder="请输入处理人员" />
          </el-form-item>
          <el-form-item label="处理说明：">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入处理说明"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交表单</el-button>
            <el-button @click="resetForm">重置表单</el-button>
          </el-form-item>
        </el-form>
        <div class="result">
          <p><strong>表单数据：</strong></p>
          <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import ViolationInvestigationSelector from '@/components/ViolationInvestigationSelector/index.vue'

defineOptions({
  name: 'ViolationSelectorTest'
})

// 测试数据
const testValue1 = ref()
const selectedRow1 = ref()

const testValue2 = ref()
const selectedRow2 = ref()

// 预设一些测试值来验证回显功能
const presetValue = ref(1) // 假设有ID为1的调查任务
const selectedPresetRow = ref()

const formData = reactive({
  investigationId: null,
  handler: '',
  description: ''
})

// 事件处理
function handleChange1(value: any, row: any) {
  selectedRow1.value = row
  console.log('基础用法选择变化:', { value, row })
  ElMessage.success(`选择了调查：${row?.title || '未知'}`)
}

function handleChange2(value: any, row: any) {
  selectedRow2.value = row
  console.log('自定义字段选择变化:', { value, row })
  ElMessage.success(`选择了调查编号：${value || '未知'}`)
}

function handlePresetChange(value: any, row: any) {
  selectedPresetRow.value = row
  console.log('预设值选择变化:', { value, row })
  if (value) {
    ElMessage.success(`选择了调查：${row?.title || '未知'}`)
  } else {
    ElMessage.info('已清空选择')
  }
}

function handleFormChange(value: any, row: any) {
  console.log('表单选择变化:', { value, row })
}

function submitForm() {
  console.log('提交表单:', formData)
  ElMessage.success('表单提交成功（仅演示）')
}

function resetForm() {
  formData.investigationId = null
  formData.handler = ''
  formData.description = ''
  ElMessage.info('表单已重置')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 24px;
}

.test-section h3 {
  margin-bottom: 16px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.result {
  margin-top: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.result p {
  margin: 8px 0;
  font-size: 14px;
  color: #606266;
}

.result pre {
  margin: 8px 0 0 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #606266;
}
</style>
</template>
