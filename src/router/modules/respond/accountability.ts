import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/respond/accountability',
  component: Layout,
  // redirect: '/one/1',
  name: '/respond/accountability',
  meta: {
    title: '责任追究处理',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/respond/accountability/handlingMeasures',
      name: '/respond/accountability/handlingMeasures',
      component: () => import('@/views/hegui/respond/accountability/handlingMeasures/index.vue'),
      meta: {
        title: '处理措施管理',
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/respond/accountability/handlingMeasures/detail',
          name: '/respond/accountability/handlingMeasures/detail',
          component: () => import('@/views/hegui/respond/accountability/handlingMeasures/detail.vue'),
          meta: {
            title: '处理措施管理详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/respond/accountability/handlingMeasures/addEdit',
          name: '/respond/accountability/handlingMeasures/addEdit',
          component: () => import('@/views/hegui/respond/accountability/handlingMeasures/addEdit.vue'),
          meta: {
            title: '处理措施管理addEdit',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/respond/accountability/processingResults',
      name: '/respond/accountability/processingResults',
      component: () => import('@/views/hegui/respond/accountability/processingResults/index.vue'),
      meta: {
        title: '处理结果确认',
      },
      children: [
        {
          path: '/respond/accountability/processingResults/detail',
          name: '/respond/accountability/processingResults/detail',
          component: () => import('@/views/hegui/respond/accountability/processingResults/detail.vue'),
          meta: {
            title: '处理措施管理详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        // {
        //   path: '/respond/accountability/rectificationTracking/addEdit',
        //   name: '/respond/accountability/rectificationTracking/addEdit',
        //   component: () => import('@/views/hegui/respond/accountability/rectificationTracking/addEdit.vue'),
        //   meta: {
        //     title: '处理措施管理addEdit',
        //     sidebar: false,
        //     breadcrumb: false,
        //   },
        // },
      ],
    },
    {
      path: '/respond/accountability/rectificationTracking',
      name: '/respond/accountability/rectificationTracking',
      // redirect: '/one/3',
      component: () => import('@/views/hegui/respond/accountability/rectificationTracking/index.vue'),
      meta: {
        title: '整改跟踪',
      },
      children: [
        {
          path: '/respond/accountability/rectificationTracking/detail',
          name: '/respond/accountability/rectificationTracking/detail',
          component: () => import('@/views/hegui/respond/accountability/rectificationTracking/detail.vue'),
          meta: {
            title: '整改跟踪',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/respond/accountability/rectificationTracking/addEdit',
          name: '/respond/accountability/rectificationTracking/addEdit',
          component: () => import('@/views/hegui/respond/accountability/rectificationTracking/addEdit.vue'),
          meta: {
            title: '整改跟踪',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
  ],
}

export default routes
