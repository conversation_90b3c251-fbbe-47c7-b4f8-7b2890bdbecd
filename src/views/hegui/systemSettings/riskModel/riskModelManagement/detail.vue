<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Document } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import riskModelApi from '@/api/riskModel'

const router = useRouter()
const route = useRoute()

// 风险模型详情数据
const modelDetail = ref<any>({})
const loading = ref(false)
const modelId = ref<string | null>(null)

// 获取风险模型详情
async function getModelDetail() {
  if (!modelId.value) {
    return
  }

  try {
    loading.value = true
    const response = await riskModelApi.getRiskModelDetail(Number(modelId.value))
    if (response) {
      modelDetail.value = response
    }
  }
  catch (error) {
    console.error('获取风险模型详情失败:', error)
    ElMessage.error('获取风险模型详情失败')
  }
  finally {
    loading.value = false
  }
}

// 返回列表
function goBack() {
  router.back()
}

// 获取状态标签类型
function getStatusType(isDefault: boolean) {
  return isDefault ? 'success' : 'info'
}

// 获取状态文本
function getStatusText(isDefault: boolean) {
  return isDefault ? '默认模型' : '普通模型'
}

// 获取风险等级标签类型
function getRiskLevelType(level: string) {
  switch (level) {
    case 'HIGH':
      return 'danger'
    case 'MEDIUM':
      return 'warning'
    case 'LOW':
      return 'success'
    default:
      return 'info'
  }
}

// 获取风险等级文本
function getRiskLevelText(level: string) {
  switch (level) {
    case 'HIGH':
      return '高风险'
    case 'MEDIUM':
      return '中风险'
    case 'LOW':
      return '低风险'
    default:
      return '未知'
  }
}

// 获取规则类型文本
function getRuleTypeText(ruleType: string) {
  switch (ruleType) {
    case 'THRESHOLD':
      return '阈值规则'
    case 'PATTERN':
      return '模式规则'
    case 'SCRIPT':
      return '脚本规则'
    default:
      return '未知类型'
  }
}

// 格式化时间
function formatTime(timeObj: any) {
  if (!timeObj || !timeObj.seconds) {
    return '-'
  }
  return new Date(timeObj.seconds * 1000).toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  const id = route.params.id || route.query.id
  if (id) {
    modelId.value = typeof id === 'string' ? id : id[0]
    getModelDetail()
  }
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between" />
          <div class="flex space-x-3">
            <el-button v-debounce="2000" class="!rounded-button whitespace-nowrap" @click="goBack">
              返回
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-card class="!mx-0" shadow="hover">
          <div v-loading="loading" class="flex items-start justify-between">
            <div>
              <h2 class="mb-2 text-2xl text-gray-900 font-bold">
                {{ modelDetail.name || '风险模型详情' }}
              </h2>
              <div class="mb-4 flex items-center space-x-3">
                <el-tag :type="getStatusType(modelDetail.isDefault)" size="small">
                  {{ getStatusText(modelDetail.isDefault) }}
                </el-tag>
                <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800">版本 {{ modelDetail.version || 1 }}</span>
              </div>
            </div>
          </div>
        </el-card>
        <el-row :gutter="20" class="mt-20">
          <el-col :span="18">
            <el-card shadow="hover">
              <div class="mb-6">
                <h3 class="mb-2 text-sm text-gray-500 font-medium">
                  模型描述
                </h3>
                <div class="rounded bg-gray-50 p-4">
                  <p class="text-gray-700">
                    {{ modelDetail.description || '暂无描述' }}
                  </p>
                </div>
              </div>

               <!-- 风险类别信息 -->
              <div v-if="modelDetail.categories && modelDetail.categories.length > 0" class="mb-6">
                <h3 class="mb-4 text-lg text-gray-900 font-medium">
                  风险类别 ({{ modelDetail.categories.length }}个)
                </h3>
                <div class="space-y-4">
                  <div v-for="category in modelDetail.categories" :key="category.id" class="rounded border bg-white p-4">
                    <div class="mb-3 flex items-center justify-between">
                      <div class="flex items-center space-x-3">
                        <h4 class="text-gray-900 font-medium">
                          {{ category.name }}
                        </h4>
                        <el-tag :type="getRiskLevelType(category.level)" size="small">
                          {{ getRiskLevelText(category.level) }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="mb-3">
                      <p class="text-sm text-gray-600">
                        {{ category.description || '暂无描述' }}
                      </p>
                    </div>
                    <div class="mb-3">
                      <p class="text-sm text-gray-500">
                        <strong>入组表达式：</strong>{{ category.expression || '-' }}
                      </p>
                    </div>

                     <!-- 风险规则 -->
                    <div v-if="category.riskRules && category.riskRules.length > 0" class="mt-4">
                      <h5 class="mb-2 text-sm text-gray-700 font-medium">
                        风险规则 ({{ category.riskRules.length }}条)
                      </h5>
                      <div class="space-y-2">
                        <div v-for="rule in category.riskRules" :key="rule.id" class="rounded bg-gray-50 p-3">
                          <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                              <span class="text-gray-900 font-medium">{{ rule.name }}</span>
                              <el-tag size="small" type="info">
                                {{ getRuleTypeText(rule.ruleType) }}
                              </el-tag>
                              <span class="rounded bg-orange-100 px-2 py-1 text-xs text-orange-800">
                                评分: {{ rule.score || 0 }}
                              </span>
                            </div>
                          </div>
                          <div class="mt-2">
                            <p class="text-sm text-gray-600">
                              {{ rule.description || '暂无描述' }}
                            </p>
                          </div>
                          <div v-if="rule.params" class="mt-2">
                            <p class="text-xs text-gray-500">
                              <strong>规则参数：</strong>{{ rule.params }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <div v-loading="loading" class="space-y-6">
              <!-- 基本信息 -->
              <el-card shadow="hover">
                <template #header>
                  <div class="font-bold">
                    基本信息
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">模型名称：</span>
                    <span class="font-medium">{{ modelDetail.name || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">模型状态：</span>
                    <el-tag :type="getStatusType(modelDetail.isDefault)" size="small">
                      {{ getStatusText(modelDetail.isDefault) }}
                    </el-tag>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">模型版本：</span>
                    <span class="font-medium">{{ modelDetail.version || 1 }}</span>
                  </div>
                </div>
              </el-card>

              <!-- 生效时间 -->
              <el-card shadow="hover">
                <template #header>
                  <div class="font-bold">
                    生效时间
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">开始时间：</span>
                    <span class="text-sm font-medium">{{ formatTime(modelDetail.effectiveFrom) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">结束时间：</span>
                    <span class="text-sm font-medium">{{ formatTime(modelDetail.effectiveTo) }}</span>
                  </div>
                </div>
              </el-card>

              <!-- 创建信息 -->
              <el-card shadow="hover">
                <template #header>
                  <div class="font-bold">
                    创建信息
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">创建人：</span>
                    <span class="font-medium">{{ modelDetail.createdBy || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">创建时间：</span>
                    <span class="text-sm font-medium">{{ formatTime(modelDetail.createdAt) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">最后修改：</span>
                    <span class="text-sm font-medium">{{ formatTime(modelDetail.updatedAt) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">修改人：</span>
                    <span class="font-medium">{{ modelDetail.updatedBy || '-' }}</span>
                  </div>
                </div>
              </el-card>

              <!-- 统计信息 -->
              <el-card shadow="hover">
                <template #header>
                  <div class="font-bold">
                    统计信息
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">风险类别数：</span>
                    <span class="text-blue-600 font-medium">{{ modelDetail.categories?.length || 0 }}个</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">风险规则数：</span>
                    <span class="text-green-600 font-medium">
                       {{ modelDetail.categories?.reduce((total: number, cat: any) => total + (cat.riskRules?.length || 0), 0) || 0 }}条
                     </span>
                  </div>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss.scss";
</style>

<style scoped>
  .prose {
    line-height: 1.75;
  }

  .prose p {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .prose h4 {
    font-weight: 600;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
   }
 </style>