---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 03-企业组织架构服务/风险模型管理

## GET 根据ID获取风险模型

GET /whiskerguardorgservice/api/risk/models/{id}

描述：根据ID获取风险模型记录

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |风险模型的ID|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "name": "",
  "description": "",
  "effectiveFrom": {
    "seconds": 0,
    "nanos": 0
  },
  "effectiveTo": {
    "seconds": 0,
    "nanos": 0
  },
  "isDefault": false,
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "categories": [
    {
      "id": 0,
      "name": "",
      "level": "",
      "expression": "",
      "description": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "riskModel": {
        "id": 0,
        "name": "",
        "description": "",
        "effectiveFrom": {
          "seconds": 0,
          "nanos": 0
        },
        "effectiveTo": {
          "seconds": 0,
          "nanos": 0
        },
        "isDefault": false,
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "categories": [
          {
            "id": 0,
            "name": "",
            "level": "",
            "expression": "",
            "description": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "riskModel": {
              "id": 0,
              "name": "",
              "description": "",
              "effectiveFrom": {
                "seconds": 0,
                "nanos": 0
              },
              "effectiveTo": {
                "seconds": 0,
                "nanos": 0
              },
              "isDefault": false,
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "categories": []
            },
            "riskRules": [
              {
                "id": 0,
                "code": "",
                "name": "",
                "ruleType": "",
                "params": "",
                "description": "",
                "score": 0,
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "riskCategory": {}
              }
            ]
          }
        ]
      },
      "riskRules": [
        {
          "id": 0,
          "code": "",
          "name": "",
          "ruleType": "",
          "params": "",
          "description": "",
          "score": 0,
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "riskCategory": {
            "id": 0,
            "name": "",
            "level": "",
            "expression": "",
            "description": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "riskModel": {
              "id": 0,
              "name": "",
              "description": "",
              "effectiveFrom": {
                "seconds": 0,
                "nanos": 0
              },
              "effectiveTo": {
                "seconds": 0,
                "nanos": 0
              },
              "isDefault": false,
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "categories": [
                {
                  "id": 0,
                  "name": "",
                  "level": "",
                  "expression": "",
                  "description": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "riskModel": {
                    "id": 0,
                    "name": "",
                    "description": "",
                    "effectiveFrom": "[Object]",
                    "effectiveTo": "[Object]",
                    "isDefault": false,
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false,
                    "categories": "[Object]"
                  },
                  "riskRules": [
                    "[Object]"
                  ]
                }
              ]
            },
            "riskRules": [
              {
                "id": 0,
                "code": "",
                "name": "",
                "ruleType": "",
                "params": "",
                "description": "",
                "score": 0,
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "riskCategory": {}
              }
            ]
          }
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityRiskModelDTO](#schemaresponseentityriskmodeldto)|

# 数据模型

<h2 id="tocS_RiskModelDTO">RiskModelDTO</h2>

<a id="schemariskmodeldto"></a>
<a id="schema_RiskModelDTO"></a>
<a id="tocSriskmodeldto"></a>
<a id="tocsriskmodeldto"></a>

```json
{
  "id": 0,
  "name": "string",
  "description": "string",
  "effectiveFrom": {
    "seconds": 0,
    "nanos": 0
  },
  "effectiveTo": {
    "seconds": 0,
    "nanos": 0
  },
  "isDefault": true,
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "categories": [
    {
      "id": 0,
      "name": "string",
      "level": "HIGH",
      "expression": "string",
      "description": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "riskModel": {
        "id": 0,
        "name": "string",
        "description": "string",
        "effectiveFrom": {
          "seconds": 0,
          "nanos": 0
        },
        "effectiveTo": {
          "seconds": 0,
          "nanos": 0
        },
        "isDefault": true,
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "categories": [
          {
            "id": null,
            "name": null,
            "level": null,
            "expression": null,
            "description": null,
            "version": null,
            "createdBy": null,
            "createdAt": null,
            "updatedBy": null,
            "updatedAt": null,
            "isDeleted": null,
            "riskModel": null,
            "riskRules": null
          }
        ]
      },
      "riskRules": [
        {
          "id": 0,
          "code": "string",
          "name": "string",
          "ruleType": "THRESHOLD",
          "params": "string",
          "description": "string",
          "score": 0,
          "version": 0,
          "createdBy": "string",
          "createdAt": {
            "seconds": null,
            "nanos": null
          },
          "updatedBy": "string",
          "updatedAt": {
            "seconds": null,
            "nanos": null
          },
          "isDeleted": true,
          "riskCategory": {
            "id": null,
            "name": null,
            "level": null,
            "expression": null,
            "description": null,
            "version": null,
            "createdBy": null,
            "createdAt": null,
            "updatedBy": null,
            "updatedAt": null,
            "isDeleted": null,
            "riskModel": null,
            "riskRules": null
          }
        }
      ]
    }
  ]
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|name|string|true|none||模型名称|
|description|string|false|none||模型描述|
|effectiveFrom|[Instant](#schemainstant)|true|none||生效开始时间|
|effectiveTo|[Instant](#schemainstant)|false|none||生效结束时间|
|isDefault|boolean|true|none||是否默认模型|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|categories|[[RiskCategoryDTO](#schemariskcategorydto)]|false|none||风险类别列表|

<h2 id="tocS_RiskCategoryDTO">RiskCategoryDTO</h2>

<a id="schemariskcategorydto"></a>
<a id="schema_RiskCategoryDTO"></a>
<a id="tocSriskcategorydto"></a>
<a id="tocsriskcategorydto"></a>

```json
{
  "id": 0,
  "name": "string",
  "level": "HIGH",
  "expression": "string",
  "description": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "riskModel": {
    "id": 0,
    "name": "string",
    "description": "string",
    "effectiveFrom": {
      "seconds": 0,
      "nanos": 0
    },
    "effectiveTo": {
      "seconds": 0,
      "nanos": 0
    },
    "isDefault": true,
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "categories": [
      {
        "id": 0,
        "name": "string",
        "level": "HIGH",
        "expression": "string",
        "description": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "riskModel": {
          "id": 0,
          "name": "string",
          "description": "string",
          "effectiveFrom": {},
          "effectiveTo": {},
          "isDefault": true,
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "categories": [
            null
          ]
        },
        "riskRules": [
          {
            "id": null,
            "code": null,
            "name": null,
            "ruleType": null,
            "params": null,
            "description": null,
            "score": null,
            "version": null,
            "createdBy": null,
            "createdAt": null,
            "updatedBy": null,
            "updatedAt": null,
            "isDeleted": null,
            "riskCategory": null
          }
        ]
      }
    ]
  },
  "riskRules": [
    {
      "id": 0,
      "code": "string",
      "name": "string",
      "ruleType": "THRESHOLD",
      "params": "string",
      "description": "string",
      "score": 0,
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "riskCategory": {
        "id": 0,
        "name": "string",
        "level": "HIGH",
        "expression": "string",
        "description": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "riskModel": {
          "id": 0,
          "name": "string",
          "description": "string",
          "effectiveFrom": {},
          "effectiveTo": {},
          "isDefault": true,
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "categories": [
            null
          ]
        },
        "riskRules": [
          {
            "id": null,
            "code": null,
            "name": null,
            "ruleType": null,
            "params": null,
            "description": null,
            "score": null,
            "version": null,
            "createdBy": null,
            "createdAt": null,
            "updatedBy": null,
            "updatedAt": null,
            "isDeleted": null,
            "riskCategory": null
          }
        ]
      }
    }
  ]
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|name|string|true|none||类别名称|
|level|string|true|none||风险等级|
|expression|string|true|none||入组表达式|
|description|string|false|none||描述信息|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|riskModel|[RiskModelDTO](#schemariskmodeldto)|false|none||RiskCategory ➜ RiskModel|
|riskRules|[[RiskRuleDTO](#schemariskruledto)]|false|none||风险规则列表|

#### 枚举值

|属性|值|
|---|---|
|level|HIGH|
|level|MEDIUM|
|level|LOW|

<h2 id="tocS_RiskRuleDTO">RiskRuleDTO</h2>

<a id="schemariskruledto"></a>
<a id="schema_RiskRuleDTO"></a>
<a id="tocSriskruledto"></a>
<a id="tocsriskruledto"></a>

```json
{
  "id": 0,
  "code": "string",
  "name": "string",
  "ruleType": "THRESHOLD",
  "params": "string",
  "description": "string",
  "score": 0,
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "riskCategory": {
    "id": 0,
    "name": "string",
    "level": "HIGH",
    "expression": "string",
    "description": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "riskModel": {
      "id": 0,
      "name": "string",
      "description": "string",
      "effectiveFrom": {
        "seconds": 0,
        "nanos": 0
      },
      "effectiveTo": {
        "seconds": 0,
        "nanos": 0
      },
      "isDefault": true,
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "categories": [
        {
          "id": 0,
          "name": "string",
          "level": "[",
          "expression": "string",
          "description": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "riskModel": {},
          "riskRules": [
            null
          ]
        }
      ]
    },
    "riskRules": [
      {
        "id": 0,
        "code": "string",
        "name": "string",
        "ruleType": "THRESHOLD",
        "params": "string",
        "description": "string",
        "score": 0,
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "riskCategory": {
          "id": 0,
          "name": "string",
          "level": "[",
          "expression": "string",
          "description": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "riskModel": {},
          "riskRules": [
            null
          ]
        }
      }
    ]
  }
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|code|string|true|none||规则编码|
|name|string|true|none||规则名称|
|ruleType|string|true|none||规则类型|
|params|string|false|none||规则参数|
|description|string|false|none||描述信息|
|score|integer|false|none||评分/权重|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|riskCategory|[RiskCategoryDTO](#schemariskcategorydto)|false|none||RiskRule ➜ RiskCategory|

#### 枚举值

|属性|值|
|---|---|
|ruleType|THRESHOLD|
|ruleType|PATTERN|
|ruleType|SCRIPT|

<h2 id="tocS_ResponseEntityRiskModelDTO">ResponseEntityRiskModelDTO</h2>

<a id="schemaresponseentityriskmodeldto"></a>
<a id="schema_ResponseEntityRiskModelDTO"></a>
<a id="tocSresponseentityriskmodeldto"></a>
<a id="tocsresponseentityriskmodeldto"></a>

```json
{
  "id": 0,
  "name": "string",
  "description": "string",
  "effectiveFrom": {
    "seconds": 0,
    "nanos": 0
  },
  "effectiveTo": {
    "seconds": 0,
    "nanos": 0
  },
  "isDefault": true,
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "categories": [
    {
      "id": 0,
      "name": "string",
      "level": "HIGH",
      "expression": "string",
      "description": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "riskModel": {
        "id": 0,
        "name": "string",
        "description": "string",
        "effectiveFrom": {
          "seconds": 0,
          "nanos": 0
        },
        "effectiveTo": {
          "seconds": 0,
          "nanos": 0
        },
        "isDefault": true,
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "categories": [
          {
            "id": null,
            "name": null,
            "level": null,
            "expression": null,
            "description": null,
            "version": null,
            "createdBy": null,
            "createdAt": null,
            "updatedBy": null,
            "updatedAt": null,
            "isDeleted": null,
            "riskModel": null,
            "riskRules": null
          }
        ]
      },
      "riskRules": [
        {
          "id": 0,
          "code": "string",
          "name": "string",
          "ruleType": "THRESHOLD",
          "params": "string",
          "description": "string",
          "score": 0,
          "version": 0,
          "createdBy": "string",
          "createdAt": {
            "seconds": null,
            "nanos": null
          },
          "updatedBy": "string",
          "updatedAt": {
            "seconds": null,
            "nanos": null
          },
          "isDeleted": true,
          "riskCategory": {
            "id": null,
            "name": null,
            "level": null,
            "expression": null,
            "description": null,
            "version": null,
            "createdBy": null,
            "createdAt": null,
            "updatedBy": null,
            "updatedAt": null,
            "isDeleted": null,
            "riskModel": null,
            "riskRules": null
          }
        }
      ]
    }
  ]
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|name|string|true|none||模型名称|
|description|string|false|none||模型描述|
|effectiveFrom|[Instant](#schemainstant)|true|none||生效开始时间|
|effectiveTo|[Instant](#schemainstant)|false|none||生效结束时间|
|isDefault|boolean|true|none||是否默认模型|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|categories|[[RiskCategoryDTO](#schemariskcategorydto)]|false|none||风险类别列表|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
