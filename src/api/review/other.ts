import api from '@/api/index'

export default {
  // 其他审查统计
  getSupplementalStatistic() {
    return api.get(`/whiskerguardcontractservice/api/supplemental/reviews/statistics`, {
    })
  },
  supplementalReview(paging: any, params: any, key: any) {
    switch (key) {
      case 'info': // 获取其他审查
        return api.get(`/whiskerguardcontractservice/api/supplemental/reviews/${params.id}`, {
        })
      case 'create': // 创建其他审查
        return api.post(`/whiskerguardcontractservice/api/supplemental/reviews`, params)
      case 'update': // 更新其他审查
        return api.post(`/whiskerguardcontractservice/api/supplemental/reviews/${params.id}`, params)
      case 'publish': // 批量发布其他审查
        return api.post(`/whiskerguardcontractservice/api/supplemental/reviews/publish/batch`, params)
      case 'delete': // 删除其他审查
        return api.delete(`/whiskerguardcontractservice/api/supplemental/reviews/${params.id}`)
      default: // 分页查询其他审查
        return api.post(`/whiskerguardcontractservice/api/supplemental/reviews/page?page=${paging.page ? paging.page - 1 : 0}&size=${paging.limit ? paging.limit : 10}`, params)
    }
  },

}
