import api from '@/api/index'

export default {
  // 新闻
  news(params: any, key: any) {
    switch (key) {
      case 'info': // 获取指定ID的新闻
        return api.get(`/whiskerguardorgservice/api/news/detail/${params.id}`, {})
      case 'addEdit': // 新增新闻
        return api.post(`/whiskerguardorgservice/api/news/create`, params)
      case 'update': // 更新新闻
        return api.patch(`/whiskerguardorgservice/api/news/partial/${params.id}`, params)
      default: // 获取所有新闻
        return api.post(`/whiskerguardorgservice/api/news/page`, params)
    }
  },
  // 创建标签分类
  createTagCategory(params: any) {
    return api.post(`/whiskerguardorgservice/api/tags/create`, params)
  },
  // 获取所有标签分类
  getTagCategories(params: any) {
    return api.get(`/whiskerguardorgservice/api/tags/list`, {
      params,
    })
  },
  // 更新标签分类
  updateTagCategory(params: any) {
    return api.patch(`/whiskerguardorgservice/api/tags/partial/${params.id}`, params)
  },
  // 删除标签分类
  deleteTagCategory(params: any) {
    return api.delete(`/whiskerguardorgservice/api/tags/delete/${params.id}`, params)
  },

  // 获取所有新闻分类
  getNewsCategories(params: any) {
    return api.get(`/whiskerguardorgservice/api/news/categories/list`, {
      params,
    })
  },
  // 创建新闻分类
  createNewsCategory(params: any) {
    return api.post(`/whiskerguardorgservice/api/news/categories/create`, params)
  },
  // 更新新闻分类
  updateNewsCategory(params: any) {
    return api.patch(`/whiskerguardorgservice/api/news/categories/partial/${params.id}`, params)
  },
  // 删除新闻分类
  deleteNewsCategory(params: any) {
    return api.delete(`/whiskerguardorgservice/api/news/categories/delete/${params.id}`, params)
  },
}
