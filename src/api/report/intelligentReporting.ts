import api from '@/api/index'

export default {
  // 举报受理
  violation(paging: any, params: any, key: any) {
    switch (key) {
      case 'info': // 获取举报受理详情
        return api.get(`/whiskerguardviolationservice/api/violation/details/${params.id}`, {
        })
      // case 'create': // 创建举报受理
      //   return api.post(`/whiskerguardviolationservice/api/violation/reviews`, params)
      // case 'update': // 更新举报受理
      //   return api.post(`/whiskerguardviolationservice/api/violation/reviews/${params.id}`, params)
      // case 'publish': // 批量发布举报受理
      //   return api.post(`/whiskerguardviolationservice/api/violation/reviews/publish/batch`, params)
      // case 'delete': // 删除举报受理
      //   return api.delete(`/whiskerguardviolationservice/api/violation/reviews/${params.id}`)
      default: // 分页查询举报受理
        return api.post(`/whiskerguardviolationservice/api/violation/details/search?page=${paging.page ? paging.page - 1 : 0}&size=${paging.limit ? paging.limit : 10}`, params)
    }
  },
  // 获取举报处理列表
  handling(params: any, key: any) {
    switch (key) {
      case 'info': // 获取举报处理详情
        return api.get(`/whiskerguardviolationservice/api/violation/deals/${params.id}`, {

        })
      default: // 分页查询举报处理
        return api.get(`/whiskerguardviolationservice/api/violation/deals`, {
          params,
        })
    }
  },
  // 创建举报处理
  createViolation(params: any) {
    return api.post(`/whiskerguardviolationservice/api/violation/deals`, params)
  },
  // 新增举报
  createReport(params: any) {
    return api.post(`/whiskerguardviolationservice/api/violation/details`, params)
  },
  // 更新举报
  updateReport(params: any) {
    return api.patch(`/whiskerguardviolationservice/api/violation/details/${params.id}`, params)
  },
}
