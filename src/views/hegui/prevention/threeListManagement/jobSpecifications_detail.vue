<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Check, Close, Delete, Download, Edit } from '@element-plus/icons-vue'
import type { TabsPaneContext } from 'element-plus'
import threeListApi from '@/api/complianceApi/prevention/threeList'
import dictApi from '@/api/modules/system/dict'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const detailData = ref<any>({})
const powersDict = ref<any[]>([])

// 风险等级映射
const riskLevelMap: Record<string, string> = {
  HIGH: '高风险',
  MEDIUM: '中风险',
  LOW: '低风险',
  高危: '高风险',
  中危: '中风险',
  低危: '低风险',
}

// 审批状态映射
const approvalStatusMap: Record<string, string> = {
  APPROVED: '已生效',
  PENDING: '待确认',
  DRAFT: '草稿',
  REJECTED: '已驳回',
}

// 获取风险等级文本
const getRiskLevelText = computed(() => {
  const level = detailData.value.riskControlInfo?.riskLevel || detailData.value.riskLevel
  return riskLevelMap[level] || level || '未知'
})

// 获取审批状态文本
const getApprovalStatusText = computed(() => {
  const status = detailData.value.approvalStatus
  return approvalStatusMap[status] || status || '待确认'
})

// 格式化时间
function formatTime(time: any) {
  if (!time) {
    return ''
  }
  if (typeof time === 'object' && time.seconds) {
    return new Date(time.seconds * 1000).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
  }
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

// 获取八项权力字典数据
async function fetchPowersDict() {
  try {
    const response = await dictApi.dictAll(80)
    if (response) {
      powersDict.value = response
    }
  }
  catch (error) {
    console.error('获取八项权力字典失败:', error)
  }
}

// 获取所有权力项及其识别状态
const getAllPowersWithStatus = computed(() => {
  if (!powersDict.value.length) {
    return []
  }

  const identifiedTypes = detailData.value.riskControlInfo?.riskTypes || []
  return powersDict.value.map(power => ({
    name: power.name,
    value: power.value,
    identified: identifiedTypes.includes(power.value) || identifiedTypes.includes(power.name),
  }))
})

// 获取详情数据
async function getDetailData() {
  const id = route.query.id
  if (!id) {
    ElMessage.error('缺少必要参数')
    return
  }

  try {
    loading.value = true
    const response = await threeListApi.getDutyMainDetail(Number(id))
    if (response) {
      detailData.value = response
    }
  }
  catch (error) {
    console.error('获取详情数据失败:', error)
    ElMessage.error('获取数据失败')
  }
  finally {
    loading.value = false
  }
}

function goBack() {
  router.back()
}
function goAddEdit(_item: any) {
  const id = route.query.id
  if (id) {
    // 编辑清单
    router.push({
      name: '/threeListManagement/jobSpecifications/edit',
      query: { id },
    })
  }
  else {
    // 新增清单
    router.push({
      name: '/threeListManagement/jobSpecifications/edit',
    })
  }
}

const activeName = ref('duties')

function handleClick(_tab: TabsPaneContext, _event: Event) {
  // 标签页切换处理
}

onMounted(() => {
  fetchPowersDict()
  getDetailData()
})
</script>

<template>
  <div v-loading="loading" class="min-h-screen bg-gray-50">
    <!-- 面包屑导航 -->
    <div class="px-6 pt-4">
      <div class="text-sm text-gray-500">
        <span>预防之翼</span>
        <span class="mx-2">></span>
        <span>三张清单管理</span>
        <span class="mx-2">></span>
        <span>重点岗位合规职责清单</span>
        <span class="mx-2">></span>
        <span class="text-gray-700">岗位职责详情</span>
      </div>
    </div>
    <!-- 顶部信息区 -->
    <div class="flex items-center justify-between px-6 pb-6 pt-4">
      <div class="flex items-center">
        <h1 class="mr-4 text-xl font-semibold">
          {{ detailData.postName || '岗位职责详情' }}
        </h1>
        <span class="rounded bg-orange-500 px-3 py-1 text-sm text-white">{{ getApprovalStatusText }}</span>
      </div>
      <div class="flex space-x-3">
        <el-button v-debounce="3000" type="primary" @click="goAddEdit(null)">
          <el-icon class="mr-1">
            <Edit />
          </el-icon>
          编辑
        </el-button>

        <el-button v-debounce="3000" plain class="!rounded-button whitespace-nowrap" @click="goBack">
          <el-icon class="mr-1">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
      </div>
    </div>
    <!-- 基本信息区 -->
    <div class="mb-6 px-6">
      <div class="rounded-lg bg-white p-6 shadow">
        <div class="mb-4 border-l-4 border-blue-500 pl-2 text-lg font-semibold">
          基本信息
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div class="flex">
            <span class="w-24 text-gray-500">序号：</span>
            <span>{{ detailData.id || '-' }}</span>
          </div>
          <div class="flex">
            <span class="w-24 text-gray-500">部门：</span>
            <span>{{ detailData.orgUnitName || '-' }}</span>
          </div>
          <div class="flex">
            <span class="w-24 text-gray-500">岗位名称：</span>
            <span>{{ detailData.postName || '-' }}</span>
          </div>
          <div class="flex">
            <span class="w-24 text-gray-500">创建人：</span>
            <span>{{ detailData.createdBy || '-' }}</span>
          </div>
          <div class="flex">
            <span class="w-24 text-gray-500">风险等级：</span>
            <span class="text-red-500">{{ getRiskLevelText }}</span>
          </div>
          <div class="flex">
            <span class="w-24 text-gray-500">状态：</span>
            <span>{{ getApprovalStatusText }}</span>
          </div>
          <div class="flex">
            <span class="w-24 text-gray-500">创建时间：</span>
            <span>{{ formatTime(detailData.createdAt) }}</span>
          </div>
          <div class="flex">
            <span class="w-24 text-gray-500">最后更新：</span>
            <span>{{ formatTime(detailData.updatedAt) }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 标签页内容区 -->
    <div class="px-6">
      <div class="rounded-lg bg-white px-6 py-4 shadow">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="职责清单 " name="duties">
            <div class="mb-6">
              <h3 class="mb-2 text-base font-bold">
                基本职责
              </h3>
              <div class="rounded bg-gray-50 p-4">
                {{ detailData.basicDuty || '暂无基本职责信息' }}
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="合规要求" name="compliance">
            <div class="mb-6">
              <h3 class="mb-2 text-base font-bold">
                合规职责依据
              </h3>
              <p>{{ detailData.complianceInfo?.complianceBasis || '基于相关法律法规制定的岗位合规要求' }}</p>
            </div>
            <div class="grid grid-cols-2 gap-6">
              <div>
                <h3 class="mb-3 text-base text-green-500 font-bold">
                  正面清单（应当做的）
                </h3>
                <div v-if="detailData.complianceInfo?.positiveComplianceRequirement">
                  <div class="rounded bg-green-50 p-4">
                    <div class="flex items-start">
                      <el-icon class="mr-2 mt-1 text-green-500">
                        <Check />
                      </el-icon>
                      <span>{{ detailData.complianceInfo.positiveComplianceRequirement }}</span>
                    </div>
                  </div>
                </div>
                <div v-else class="py-8 text-center text-gray-500">
                  暂无正面合规要求
                </div>
              </div>
              <div>
                <h3 class="mb-3 text-base text-red-500 font-bold">
                  负面清单（禁止做的）
                </h3>
                <div v-if="detailData.complianceInfo?.negativeComplianceRequirement">
                  <div class="rounded bg-red-50 p-4">
                    <div class="flex items-start">
                      <el-icon class="mr-2 mt-1 text-red-500">
                        <Close />
                      </el-icon>
                      <span>{{ detailData.complianceInfo.negativeComplianceRequirement }}</span>
                    </div>
                  </div>
                </div>
                <div v-else class="py-8 text-center text-gray-500">
                  暂无负面合规要求
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="风险防控" name="risk">
            <div class="mb-6">
              <h3 class="mb-4 text-base font-bold">
                八项权力识别
              </h3>
              <div v-if="getAllPowersWithStatus.length > 0" class="space-y-2">
                <div class="flex flex-wrap gap-2">
                  <el-tag
                    v-for="power in getAllPowersWithStatus"
                    :key="power.value"
                    :type="power.identified ? 'primary' : 'info'"
                    :effect="power.identified ? 'dark' : 'plain'"
                  >
                    {{ power.name }}
                    <el-icon v-if="power.identified" class="ml-1">
                      <Check />
                    </el-icon>
                  </el-tag>
                </div>
              </div>
              <div v-else class="py-4 text-center text-gray-500">
                暂无权力识别信息
              </div>
            </div>
            <h3 class="mb-4 text-base font-bold">
              防控措施
            </h3>
            <div v-if="detailData.riskControlInfo?.controlMeasures" class="space-y-4">
              <div class="border border-gray-200 rounded p-4">
                <div class="mb-2 flex items-center">
                  <el-icon class="mr-2 text-blue-500">
                    <Check />
                  </el-icon>
                  <span class="font-medium">防控措施</span>
                </div>
                <p class="whitespace-pre-line text-gray-600">
                  {{ detailData.riskControlInfo.controlMeasures }}
                </p>
              </div>
            </div>
            <div v-else class="py-8 text-center text-gray-500">
              暂无防控措施信息
            </div>
          </el-tab-pane>
          <el-tab-pane label="更新记录" name="history">
            <div class="space-y-4">
              <!-- 创建记录 -->
              <div v-if="detailData.createdAt" class="border border-gray-200 rounded p-4">
                <div class="mb-2 flex items-center justify-between">
                  <span class="font-medium">创建记录</span>
                  <span class="text-sm text-gray-500">{{ formatTime(detailData.createdAt) }}</span>
                </div>
                <p class="text-gray-600">
                  创建了职责清单
                </p>
                <p class="text-sm text-gray-500">
                  操作人：{{ detailData.createdBy || '系统' }}
                </p>
              </div>
              <!-- 更新记录 -->
              <div v-if="detailData.updatedAt && detailData.updatedAt !== detailData.createdAt" class="border border-gray-200 rounded p-4">
                <div class="mb-2 flex items-center justify-between">
                  <span class="font-medium">更新记录</span>
                  <span class="text-sm text-gray-500">{{ formatTime(detailData.updatedAt) }}</span>
                </div>
                <p class="text-gray-600">
                  更新了职责清单信息
                </p>
                <p class="text-sm text-gray-500">
                  操作人：{{ detailData.updatedBy || '系统' }}
                </p>
              </div>
              <!-- 无记录提示 -->
              <div v-if="!detailData.createdAt && !detailData.updatedAt" class="py-8 text-center text-gray-500">
                暂无更新记录
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

  <style scoped>
  .timeline {
  position: relative;
  padding-left: 20px;
  }
  .timeline-item {
  position: relative;
  padding-bottom: 20px;
  }
  .timeline-dot {
  position: absolute;
  left: -8px;
  top: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #1890FF;
  }
  .timeline-content {
  margin-left: 20px;
  }
  .timeline-date {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
  }
  .timeline-card {
  background-color: #f9f9f9;
  padding: 12px 16px;
  border-radius: 4px;
  border-left: 3px solid #1890FF;
  }
  .timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: -2px;
  top: 12px;
  height: calc(100% - 12px);
  width: 2px;
  background-color: #e8e8e8;
  }
  </style>
