<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import continuousApi from '@/api/problemTask/continuous'
import dictApi from '@/api/modules/system/dict'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import DepartPerson from '@/components/departPerson/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import { disablePastDates } from '@/utils/dateUtils'

const inputTagVisible = ref(false)
const inputTagValue = ref('')
const tagInput = ref()
const activeCollapse = ref(['1', '2', '3', '4'])
const loading = ref(false)
const router = useRouter()
const route = useRoute()
const reportId = ref(route.query.id ? Number(route.query.id) : null)

// 表单引用
const formRef = ref<FormInstance>()

// 表单校验规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入报告标题', trigger: 'blur' },
    { min: 2, max: 100, message: '报告标题长度在 2 到 100 个字符', trigger: 'blur' },
  ],
  reportType: [
    { required: true, message: '请选择报告类型', trigger: 'change' },
  ],
  reportCycle: [
    { required: true, message: '请选择报告周期', trigger: 'change' },
  ],
  startDate: [
    { required: true, message: '请选择覆盖开始时间', trigger: 'change' },
  ],
  endDate: [
    { required: true, message: '请选择覆盖结束时间', trigger: 'change' },
  ],
  summary: [
    { required: true, message: '请输入报告摘要', trigger: 'blur' },
    { min: 10, max: 500, message: '报告摘要长度在 10 到 500 个字符', trigger: 'blur' },
  ],
  orgId: [
    { required: true, message: '请选择编制部门', trigger: 'change' },
  ],
  employeeId: [
    { required: true, message: '请选择编制人', trigger: 'change' },
  ],
  level: [
    { required: true, message: '请选择保密级别', trigger: 'change' },
  ],
}

// 字典选项
const reportTypeOptions = ref([
  { value: 'monthly', label: '月度报告' },
  { value: 'quarterly', label: '季度报告' },
  { value: 'annual', label: '年度报告' },
])
const _reportCycleOptions = ref([
  { value: 'monthly', label: '月度' },
  { value: 'quarterly', label: '季度' },
  { value: 'annual', label: '年度' },
])
const levelOptions = ref([
  { value: '', label: '' },
])

// 附件类型定义
interface AttachmentItem {
  id?: number | null
  relatedId?: number
  relatedType?: number
  fileName: string
  filePath: string
  fileType: string
  fileSize?: string
  fileDesc?: string
  metadata?: string
  version?: number
  createdBy?: string
  createdAt?: any
  updatedBy?: string
  updatedAt?: any
  isDeleted?: boolean
}

const form = reactive({
  // 接口字段 - 完全按照API文档定义
  id: null,
  title: '',
  reportCode: '',
  reportType: '',
  reportCycle: '',
  startDate: '',
  endDate: '',
  employeeId: '',
  orgId: null,
  establishDate: '',
  level: '',
  summary: '',
  improveSummary: '',
  improveInvest: '',
  improveProcess: '',
  improveAchievement: '',
  metadata: '',
  version: 0,
  createdBy: '',
  createdAt: null,
  updatedBy: '',
  updatedAt: null,
  isDeleted: false,
  attachmentList: [] as AttachmentItem[],

  // 页面显示用的字段（不提交到接口）
  tags: ['合规', '改进', '年度报告'],
  creator: '张合规',
  createDate: '2023-06-15',
})
function handleAttachmentUploadSuccess(_fileInfo: any) {
  // 附件上传成功，循环数据添加attachmentType字段
  form.attachmentList.forEach((item) => {
    if (!item.relatedType) {
      item.relatedType = 3
    }
  })
}
// 保存草稿
async function saveDraft() {
  // 草稿保存进行基本校验，但不阻止保存
  if (formRef.value) {
    try {
      await formRef.value.validateField(['title'])
    }
    catch (error) {
      // 标题校验失败，但仍允许保存草稿
      ElMessage.warning('建议完善报告标题后再保存')
    }
  }

  try {
    loading.value = true
    const submitData = buildSubmitData()

    if (reportId.value) {
      // 编辑模式：调用更新接口
      await continuousApi.updateReport(submitData)
      ElMessage.success('保存成功')
      router.back()
    }
    else {
      // 新增模式：调用创建接口
      await continuousApi.createReport(submitData)
      ElMessage.success('保存成功')
      router.back()
    }
  }
  catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
  finally {
    loading.value = false
  }
}

// 获取字典数据
async function fetchDictOptions() {
  try {
    // 获取报告类型选项 (31)
    const typeResponse = await dictApi.dictAll(92)
    if (typeResponse && Array.isArray(typeResponse)) {
      reportTypeOptions.value = typeResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }

    // 获取保密级别选项 (41)
    const levelResponse = await dictApi.dictAll(41)
    if (levelResponse && Array.isArray(levelResponse)) {
      levelOptions.value = levelResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
    ElMessage.error('获取字典数据失败')
  }
}

// 生成报告编号
async function generateReportCode() {
  try {
    const response = await dictApi.getCode('CONTINUOUS_REPORT')
    if (response) {
      form.reportCode = response
    }
  }
  catch (error) {
    console.error('获取报告编号失败:', error)
    ElMessage.error('获取报告编号失败')
  }
}

// 获取报告详情
async function fetchReportDetail() {
  if (!reportId.value) {
    return
  }

  try {
    loading.value = true
    const response = await continuousApi.getReportDetail(reportId.value)
    if (response) {
      // 将接口返回的数据填充到表单中，并处理日期格式
      Object.assign(form, {
        ...response,
        startDate: response.startDate ? new Date(response.startDate).toISOString().split('T')[0] : '',
        endDate: response.endDate ? new Date(response.endDate).toISOString().split('T')[0] : '',
      })
    }
  }
  catch (error) {
    console.error('获取报告详情失败:', error)
    ElMessage.error('获取报告详情失败')
  }
  finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  fetchDictOptions()
  if (reportId.value) {
    fetchReportDetail()
  }
  else {
    generateReportCode()
  }
})
// 处理返回按钮点击事件
function handleBack() {
  router.back()
}

// 测试表单校验
function testValidation() {
  if (formRef.value) {
    formRef.value.validate((valid, fields) => {
      if (valid) {
        ElMessage.success('表单校验通过')
      }
      else {
        console.log('校验失败的字段:', fields)
        ElMessage.error('表单校验失败，请检查必填字段')
      }
    })
  }
}
// 提交审核
async function submitReview() {
  // 提交审核需要完整校验
  if (!formRef.value) { return }

  try {
    await formRef.value.validate()

    // 额外校验日期逻辑
    if (form.startDate && form.endDate && new Date(form.startDate) > new Date(form.endDate)) {
      ElMessage.error('覆盖开始时间不能晚于结束时间')
      return
    }

    loading.value = true
    const submitData = buildSubmitData()

    if (reportId.value) {
      // 编辑模式：调用更新接口
      await continuousApi.updateReport(submitData)
      ElMessage.success('报告更新并提交审核成功')
      router.back()
    }
    else {
      // 新增模式：调用创建接口
      await continuousApi.createReport(submitData)
      ElMessage.success('提交审核成功')
      router.back()
    }
  }
  catch (error) {
    if (error === false) {
      // 表单校验失败
      ElMessage.error('请完善必填信息')
    }
    else {
      console.error('提交审核失败:', error)
      ElMessage.error('提交审核失败')
    }
  }
  finally {
    loading.value = false
  }
}

// 构建提交数据
function buildSubmitData() {
  return {
    id: form.id,
    title: form.title,
    reportCode: form.reportCode,
    reportType: form.reportType,
    reportCycle: form.reportCycle,
    startDate: form.startDate ? new Date(`${form.startDate}T12:04:09Z`).toISOString() : null,
    endDate: form.endDate ? new Date(`${form.endDate}T12:04:09Z`).toISOString() : null,
    employeeId: form.employeeId,
    orgId: form.orgId,
    establishDate: form.establishDate,
    level: form.level,
    summary: form.summary,
    improveSummary: form.improveSummary,
    improveInvest: form.improveInvest,
    improveProcess: form.improveProcess,
    improveAchievement: form.improveAchievement,
    metadata: form.metadata,
    version: form.version,
    createdBy: form.createdBy,
    createdAt: form.createdAt,
    updatedBy: form.updatedBy,
    updatedAt: form.updatedAt,
    isDeleted: form.isDeleted,
    attachmentList: form.attachmentList,
  }
}

function removeTag(tag: string) {
  form.tags = form.tags.filter(t => t !== tag)
}
function showTagInput() {
  inputTagVisible.value = true
  nextTick(() => {
    tagInput.value.focus()
  })
}
function addTag() {
  if (inputTagValue.value) {
    form.tags.push(inputTagValue.value)
    inputTagValue.value = ''
  }
  inputTagVisible.value = false
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{ reportId ? '编辑优化报告' : '新增优化报告' }}
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <!-- <el-button

              type="primary"
              class="!rounded-button whitespace-nowrap"
              :loading="loading"
              @click="saveDraft"
            >
              保存草稿
            </el-button> -->
            <el-button

              type="primary"
              class="!rounded-button whitespace-nowrap"
              :loading="loading"
              @click="submitReview"
            >
              提交审核
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="handleBack">
              取消
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <el-form ref="formRef" :model="form" :rules="formRules" label-width="120px" label-position="right">
        <el-row :gutter="20" class="!mx-0">
          <el-col :span="24">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-18 fw-800">
                  基本信息
                </div>
              </template>
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <el-form-item label="报告标题" prop="title" required>
                    <el-input v-model="form.title" placeholder="请输入报告标题" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="报告编号">
                    <el-input v-model="form.reportCode" placeholder="自动生成" readonly />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="报告类型" prop="reportType" required>
                    <el-select v-model="form.reportType" placeholder="请选择报告类型" class="w-full">
                      <el-option
                        v-for="option in reportTypeOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="报告周期" prop="reportCycle" required>
                    <el-date-picker
                      v-model="form.reportCycle"
                      :disabled-date="disablePastDates"
                      type="date"
                      placeholder="选择报告周期"
                      class="w-full"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="覆盖开始时间" prop="startDate" required>
                    <el-date-picker
                      v-model="form.startDate"
                      :disabled-date="disablePastDates"
                      type="date"
                      placeholder="选择开始日期"
                      class="w-full"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="覆盖结束时间" prop="endDate" required>
                    <el-date-picker
                      v-model="form.endDate"
                      :disabled-date="disablePastDates"
                      type="date"
                      placeholder="选择结束日期"
                      class="w-full"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </div>
                <el-form-item label="编制部门" prop="orgId" required>
                  <DepartmentTreeSelect v-model="form.orgId" placeholder="请选择编制部门" />
                </el-form-item>
                <el-form-item label="编制人" prop="employeeId" required>
                  <DepartPerson v-model="form.employeeId" placeholder="请选择编制人" />
                </el-form-item>

                <el-form-item label="编制日期">
                  <el-date-picker
                    v-model="form.establishDate"
                    :disabled-date="disablePastDates"
                    type="date"
                    placeholder="选择编制日期"
                    class="w-full"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
                <el-form-item label="保密级别" prop="level" required>
                  <el-select v-model="form.level" placeholder="请选择保密级别" class="w-full">
                    <el-option
                      v-for="option in levelOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
                <!-- <el-form-item label="报告标签">
                    <el-tag v-for="tag in form.tags" :key="tag" closable class="mr-2" @close="removeTag(tag)">
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-if="inputTagVisible" ref="tagInput" v-model="inputTagValue" size="small" class="w-28"
                      @keyup.enter="addTag" @blur="addTag"
                    />
                    <el-button v-else size="small" @click="showTagInput">
                      + 添加标签
                    </el-button>
                  </el-form-item> -->
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  报告摘要
                </div>
              </template>
              <el-form-item label="报告摘要" prop="summary" required>
                <el-input
                  v-model="form.summary" type="textarea" :rows="6" placeholder="请输入报告摘要，建议300-500字"
                  show-word-limit maxlength="500"
                />
              </el-form-item>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div v-if="false" class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    一、改进概况
                  </div>
                  <el-button type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                    导入改进数据
                  </el-button>
                </div>
              </template>
              <el-collapse v-model="activeCollapse">
                <el-collapse-item title="改进工作概述" name="1">
                  <el-input v-model="form.improveSummary" type="textarea" :rows="4" placeholder="请输入改进工作概述" />
                </el-collapse-item>
                <el-collapse-item title="改进资源投入" name="2">
                  <el-input v-model="form.improveInvest" type="textarea" :rows="4" placeholder="请输入改进资源投入情况" />
                </el-collapse-item>
                <el-collapse-item title="改进进度总览" name="3">
                  <el-input v-model="form.improveProcess" type="textarea" :rows="4" placeholder="请输入改进进度总览" />
                </el-collapse-item>
                <el-collapse-item title="主要成果一览" name="4">
                  <el-input v-model="form.improveAchievement" type="textarea" :rows="4" placeholder="请输入主要成果一览" />
                </el-collapse-item>
              </el-collapse>
            </el-card>

            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    二、经验教训分析
                  </div>
                  <div v-if="false" class="flex space-x-2">
                    <el-button type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      选择经验教训
                    </el-button>
                    <el-button type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      导入分析数据
                    </el-button>
                  </div>
                </div>
              </template>
              <el-input v-model="form.metadata" type="textarea" :rows="6" placeholder="请输入经验教训分析相关内容" />
            </el-card>

            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  八、附件
                </div>
              </template>
              <UploadMbb
                v-model="form.attachmentList"
                :max="10"
                :size="50"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.zip,.rar"
                tip-text="支持 PDF、DOC、XLS、JPG、PNG、ZIP、RAR 格式文件，大小不超过 50MB"
                service-name="whiskerguardregulatoryservice"
                category-name="optimizationReport"
                :auto-upload="false"
                :use-file-path="true"
                attachment-type="optimizationReport"
                @upload-success="handleAttachmentUploadSuccess"
              />
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-menu-vertical {
    border-right: none;
  }

  .el-menu-item.is-active {
    background-color: rgb(64 158 255 / 10%) !important;
  }

  .el-collapse-item__header {
    font-weight: 500;
  }

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  .el-textarea__inner {
    min-height: 120px;
  }
</style>
