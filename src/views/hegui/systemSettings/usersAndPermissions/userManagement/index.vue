<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowDown,
  ArrowRight,
  Bell,
  Check,
  Close,
  DataAnalysis,
  Delete,
  Document,
  Download,
  Edit,
  Filter,
  Monitor,
  OfficeBuilding,
  Plus,
  Refresh,
  Search,
  Setting,
  Upload,
  User,
  View,
} from '@element-plus/icons-vue'
import userApi from '@/api/organizational/user'
import organizationalApi from '@/api/organizational/index'
import roleTree from '@/api/permissions/role'
import useUserStore from '@/store/modules/user'
import ImportComponent from '@/components/import/importV1.vue'

// 获取用户store
const userStore = useUserStore()

// 组织架构树数据 - 从store中获取
const orgTreeData = ref<any[]>([])
// 组织架构树的ref
const orgTreeRef = ref()

// 初始化组织架构数据
function initOrgTreeData() {
  const orgTree = userStore.getOrgTree()
  if (orgTree && orgTree.length > 0) {
    orgTreeData.value = orgTree
  }
  else {
    // 如果store中没有数据，设置为空数组
    orgTreeData.value = []
  }
}

const treeProps = {
  children: 'children',
  label: 'name',
}

// 用户筛选条件
const filter = ref({
  status: '',
  userType: '',
  keyword: '',
  orgUnitId: '', // 添加组织架构ID过滤
})

// 用户列表数据
const userList = ref<any[]>([])
const loading = ref(false)

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 选中的用户
const selectedUsers = ref<any[]>([])

// 用户统计数据
const userStatistics = ref({
  totalUsers: 0,
  roleStatistics: {
    adminUsers: 0,
    regularUsers: 0,
  },
  statusStatistics: {
    activeUsers: 0,
    inactiveUsers: 0,
  },
  departmentStatistics: [] as any[],
})

// 部门、岗位、角色选项数据
const departmentOptions = ref<any[]>([])
const positionOptions = ref<any[]>([])
const roleOptions = ref<any[]>([])

// 弹窗相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const isView = ref(false)
const formLoading = ref(false)

// 导入弹窗状态
const importDialogVisible = ref(false)

// 用户表单数据
const userForm = ref({
  id: null,
  username: '',
  password: '',
  realName: '',
  email: '',
  phone: '',
  gender: 'UNKNOWN',
  type: 'EMPLOYEE',
  birthDate: '',
  idCard: '',
  employeeNo: '',
  status: 'ACTIVE',
  hireDate: '',
  leaveDate: '',
  wechatOpenId: '',
  wechatUnionId: '',
  positionList: [] as any[],
  orgUnitList: [] as any[],
  roleList: [] as any[],
  employeeOrgList: [] as any[],
  // 用于表单绑定的ID
  orgUnitIds: null,
  positionIds: null,
  roleIds: [] as any[],
})

// 密码强度验证函数
function _validatePassword(rule: any, value: string, callback: any) {
  // 如果是编辑模式且密码为空，则跳过验证
  if (isEdit.value && !value) {
    callback()
    return
  }

  // 新增模式下密码必填
  if (!isEdit.value && !value) {
    callback(new Error('请输入密码'))
    return
  }

  // 如果有值，则进行格式验证
  if (value) {
    // 至少8个字符
    if (value.length < 8) {
      callback(new Error('密码至少需要8个字符'))
      return
    }

    // 不能包含空格
    if (/\s/.test(value)) {
      callback(new Error('密码不能包含空格'))
      return
    }

    // 至少一个大写字母
    if (!/[A-Z]/.test(value)) {
      callback(new Error('密码至少需要一个大写字母'))
      return
    }

    // 至少一个小写字母
    if (!/[a-z]/.test(value)) {
      callback(new Error('密码至少需要一个小写字母'))
      return
    }

    // 至少一个数字
    if (!/\d/.test(value)) {
      callback(new Error('密码至少需要一个数字'))
      return
    }

    // 至少一个特殊字符
    if (!/[@#$%^&+=]/.test(value)) {
      callback(new Error('密码至少需要一个特殊字符(@#$%^&+=)'))
      return
    }
  }

  callback()
}

// 自动生成密码函数
function generatePassword() {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const numbers = '0123456789'
  const specialChars = '@#$%^&+='

  let password = ''

  // 确保至少包含一个大写字母
  password += uppercase[Math.floor(Math.random() * uppercase.length)]

  // 确保至少包含一个小写字母
  password += lowercase[Math.floor(Math.random() * lowercase.length)]

  // 确保至少包含一个数字
  password += numbers[Math.floor(Math.random() * numbers.length)]

  // 确保至少包含一个特殊字符
  password += specialChars[Math.floor(Math.random() * specialChars.length)]

  // 生成剩余的8个字符，总长度为12位
  const allChars = uppercase + lowercase + numbers + specialChars
  for (let i = 0; i < 8; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)]
  }

  // 使用更安全的打乱方法（Fisher-Yates洗牌算法）
  const passwordArray = password.split('')
  for (let i = passwordArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[passwordArray[i], passwordArray[j]] = [passwordArray[j], passwordArray[i]]
  }

  return passwordArray.join('')
}

// 处理生成密码
function handleGeneratePassword() {
  userForm.value.password = generatePassword()
  // 触发表单验证
  if (userFormRef.value) {
    userFormRef.value.validateField('password')
  }
}

// 表单验证规则
const formRules = computed(() => {
  const baseRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9_()（）]+$/, message: '用户名只能包含字母、数字、下划线和括号', trigger: 'blur' },
    ],
    realName: [
      { required: true, message: '请输入真实姓名', trigger: 'blur' },
      { min: 2, max: 10, message: '真实姓名长度在 2 到 10 个字符', trigger: 'blur' },
    ],
    email: [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
    ],
    phone: [
      { required: true, message: '请输入手机号码', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
    ],
    employeeNo: [
      { required: true, message: '请输入员工编号', trigger: 'blur' },
    ],
    orgUnitIds: [
      { required: true, message: '请选择部门', trigger: 'change' },
    ],
    positionIds: [
      { required: true, message: '请选择岗位', trigger: 'change' },
    ],
    roleIds: [
      { required: true, message: '请选择角色', trigger: 'change' },
    ],
    idCard: [
      { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号', trigger: 'blur' },
    ],
  }

  // 密码校验规则：新增时必填，编辑时不校验（因为字段被禁用）
  const passwordRules = isEdit.value
    ? [] // 编辑模式：不校验密码字段
    : [
        // 新增模式：密码必填且需要符合格式要求
        { required: true, message: '请输入密码', trigger: 'blur' },
        { validator: _validatePassword, trigger: 'blur' },
      ]

  return {
    ...baseRules,
    password: passwordRules,
  }
})

// 表单引用
const userFormRef = ref()

// 最近活动数据
const recentActivities = ref([
  {
    username: 'admin',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    action: '登录系统',
    time: '10分钟前',
  },
  {
    username: 'zhangsan',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    action: '修改了个人信息',
    time: '25分钟前',
  },
  {
    username: 'lisi',
    avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
    action: '创建了新用户',
    time: '1小时前',
  },
  {
    username: 'wangwu',
    avatar: 'https://cube.elemecdn.com/d/e9/c1d93af380ef5d0366245e2b31c13png.png',
    action: '更新了合规文档',
    time: '2小时前',
  },
  {
    username: 'zhaoliu',
    avatar: 'https://cube.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549png.png',
    action: '提交了审批',
    time: '3小时前',
  },
])

// 饼图实例
const pieChart = ref()
let pieChartInstance: echarts.ECharts | null = null

// 获取部门列表
async function getDepartmentList() {
  try {
    const response = await organizationalApi.organizationalUnitApi({ page: 0, size: 1000 }, {}, 'list')
    if (response && response.content) {
      departmentOptions.value = response.content.map((item: any) => ({
        id: item.id,
        name: item.name,
        label: item.name,
        value: item.id,
      }))
    }
  }
  catch (error) {
    // 获取部门列表失败
  }
}

// 获取岗位列表
async function getPositionList() {
  try {
    const response = await organizationalApi.positionApi({ page: 0, size: 1000 }, {}, 'list')
    if (response && response.content) {
      positionOptions.value = response.content.map((item: any) => ({
        id: item.id,
        name: item.name,
        label: item.name,
        value: item.id,
      }))
    }
  }
  catch (error) {
    // 获取岗位列表失败
  }
}

// 根据部门ID获取岗位列表
async function getPositionsByDeptId(deptId: number | null) {
  try {
    if (!deptId) {
      positionOptions.value = []
      return
    }

    // 调用接口获取岗位
    try {
      const response = await roleTree.getDeptByPositionId({ id: deptId })
      if (response && Array.isArray(response)) {
        positionOptions.value = response.map((item: any) => ({
          id: item.id,
          name: item.name,
          label: item.name,
          value: item.id,
        }))
      }
      else {
        positionOptions.value = []
      }
    }
    catch (error) {
      positionOptions.value = []
    }
  }
  catch (error) {
    positionOptions.value = []
  }
}

// 处理部门选择变化
function handleDepartmentChange(selectedDeptId: number | null) {
  // 清空当前选中的岗位
  userForm.value.positionIds = null

  // 根据选中的部门获取对应的岗位
  getPositionsByDeptId(selectedDeptId)
}

// 获取角色列表
async function getRoleList() {
  try {
    const response = await organizationalApi.roleApi({ page: 0, size: 1000 }, {}, 'list')
    if (response && response.content) {
      roleOptions.value = response.content.map((item: any) => ({
        id: item.id,
        name: item.name,
        label: item.name,
        value: item.id,
      }))
    }
  }
  catch (error) {
    // 获取角色列表失败
  }
}

// 获取用户列表
async function getUserList() {
  try {
    loading.value = true
    const paging = {
      page: pagination.value.currentPage - 1, // 后端从0开始
      size: pagination.value.pageSize,
    }
    // 构建查询参数
    const queryParams: any = {}
    queryParams.page = paging.page
    queryParams.size = paging.size

    if (filter.value.keyword) {
      queryParams.keyword = filter.value.keyword
    }
    if (filter.value.status) {
      queryParams.status = filter.value.status
    }
    if (filter.value.userType) {
      queryParams.type = filter.value.userType
    }
    if (filter.value.orgUnitId) {
      queryParams.orgUnitId = filter.value.orgUnitId
    }

    const response = await userApi.userApi(paging, queryParams, 'list')

    if (response && response.content) {
      userList.value = response.content.map((user: any) => ({
        ...user,
        // 格式化状态显示
        statusText: user.status === 'ACTIVE' ? '启用' : user.status === 'INACTIVE' ? '禁用' : '冻结',
        // 格式化性别显示
        genderText: user.gender === 'MALE' ? '男' : user.gender === 'FEMALE' ? '女' : '未知',
        // 格式化最后登录时间
        lastLoginText: user.lastLoginTime ? user.lastLoginTime : '从未登录',
        // 格式化部门显示
        departmentText: user.orgUnitList && user.orgUnitList.length > 0
          ? user.orgUnitList.map((dept: any) => dept.name).join(', ')
          : '未分配',
        // 格式化岗位显示
        positionText: user.positionList && user.positionList.length > 0
          ? user.positionList.map((pos: any) => pos.name).join(',  ')
          : '未分配',
        // 格式化角色显示
        roleText: user.roleList && user.roleList.length > 0
          ? user.roleList.map((role: any) => role.name).join(', ')
          : '未分配',
      }))

      // 这里需要从响应头或其他地方获取总数，暂时使用数据长度
      pagination.value.total = response.totalElements
    }
  }
  catch (error) {
    ElMessage.error('获取用户列表失败')
  }
  finally {
    loading.value = false
  }
}

// 获取用户统计信息
async function getUserStatistics() {
  try {
    const response = await organizationalApi.getUserStatistics()
    if (response) {
      userStatistics.value = {
        totalUsers: response.totalUsers || 0,
        roleStatistics: {
          adminUsers: response.roleStatistics?.adminUsers || 0,
          regularUsers: response.roleStatistics?.regularUsers || 0,
        },
        statusStatistics: {
          activeUsers: response.statusStatistics?.activeUsers || 0,
          inactiveUsers: response.statusStatistics?.inactiveUsers || 0,
        },
        departmentStatistics: response.departmentStatistics || [] as any[],
      }

      // 更新饼图数据
      updatePieChart()
    }
  }
  catch (error) {
    // 使用默认数据，避免页面显示异常
    userStatistics.value = {
      totalUsers: 0,
      roleStatistics: {
        adminUsers: 0,
        regularUsers: 0,
      },
      statusStatistics: {
        activeUsers: 0,
        inactiveUsers: 0,
      },
      departmentStatistics: [] as any[],
    }
  }
}

// 更新饼图数据
function updatePieChart() {
  if (!pieChartInstance || !userStatistics.value.departmentStatistics.length) {
    return
  }

  const chartData = userStatistics.value.departmentStatistics.map((dept: any) => ({
    value: dept.employeeCount,
    name: dept.departmentName,
  }))

  const option = {
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '部门分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: chartData,
      },
    ],
  }

  pieChartInstance.setOption(option)
}

// 搜索用户
function handleSearch() {
  // 将空字符串转换为null
  const processedFilter = {
    status: filter.value.status === '' ? null : filter.value.status,
    userType: filter.value.userType === '' ? null : filter.value.userType,
    keyword: filter.value.keyword === '' ? null : filter.value.keyword,
    orgUnitId: filter.value.orgUnitId === '' ? null : filter.value.orgUnitId,
  }

  // 临时保存原始filter值
  const originalFilter = { ...filter.value }
  // 使用处理后的filter值
  filter.value = processedFilter

  pagination.value.currentPage = 1
  getUserList()

  // 恢复原始filter值以保持UI状态
  filter.value = originalFilter
}

// 重置筛选条件
function handleReset() {
  filter.value = {
    status: '',
    userType: '',
    keyword: '',
    orgUnitId: '', // 重置组织架构ID
  }
  // 清除组织架构树的高亮状态
  if (orgTreeRef.value) {
    orgTreeRef.value.setCurrentKey(null)
  }
  pagination.value.currentPage = 1
  getUserList()
}

// 分页变化
function handlePageChange() {
  getUserList()
}

// 初始化饼图
function initPieChart() {
  if (!pieChart.value) { return }

  pieChartInstance = echarts.init(pieChart.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '部门分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 320, name: '技术部' },
          { value: 240, name: '产品部' },
          { value: 149, name: '市场部' },
          { value: 100, name: '财务部' },
          { value: 59, name: '人力资源部' },
        ],
      },
    ],
  }

  pieChartInstance.setOption(option)
}

// 处理组织架构树节点点击
function handleNodeClick(data: any) {
  // 将选中的组织架构ID传入到过滤条件中
  filter.value.orgUnitId = data.id
  // 重置分页到第一页
  pagination.value.currentPage = 1

  // 将空字符串转换为null
  const processedFilter = {
    status: filter.value.status === '' ? null : filter.value.status,
    userType: filter.value.userType === '' ? null : filter.value.userType,
    keyword: filter.value.keyword === '' ? null : filter.value.keyword,
    orgUnitId: filter.value.orgUnitId === '' ? null : filter.value.orgUnitId,
  }

  // 临时保存原始filter值
  const originalFilter = { ...filter.value }
  // 使用处理后的filter值
  filter.value = processedFilter

  // 重新获取用户列表
  getUserList()

  // 恢复原始filter值以保持UI状态
  filter.value = originalFilter
}

// 处理表格选择变化
function handleSelectionChange(val: any[]) {
  selectedUsers.value = val
}

// 查看用户详情
async function _handleView(row: any) {
  try {
    dialogTitle.value = '查看用户详情'
    isEdit.value = false
    isView.value = true

    // 调用详情接口获取完整用户信息
    const response = await userApi.userApi({}, { id: row.id }, 'detail')

    if (response) {
      // 填充表单数据
      userForm.value = {
        id: response.id,
        username: response.username || '',
        password: '', // 查看时不显示密码
        realName: response.realName || '',
        email: response.email || '',
        phone: response.phone || '',
        gender: response.gender || 'UNKNOWN',
        type: response.type || 'EMPLOYEE',
        birthDate: response.birthDate || '',
        idCard: response.idCard || '',
        employeeNo: response.employeeNo || '',
        status: response.status || 'ACTIVE',
        hireDate: response.hireDate || '',
        leaveDate: response.leaveDate || '',
        wechatOpenId: response.wechatOpenId || '',
        wechatUnionId: response.wechatUnionId || '',
        positionList: response.positionList || [],
        orgUnitList: response.orgUnitList || [],
        roleList: response.roleList || [],
        employeeOrgList: response.employeeOrgList || [],
        // 提取ID用于表单绑定
        // 优先从 employeeOrgList 获取，如果没有则从旧格式获取
        orgUnitIds: (() => {
          if (response.employeeOrgList && response.employeeOrgList.length > 0 && response.employeeOrgList[0].orgUnit) {
            return response.employeeOrgList[0].orgUnit.id
          }
          return (response.orgUnitList && response.orgUnitList.length > 0) ? response.orgUnitList[0].id : null
        })(),
        positionIds: (() => {
          if (response.employeeOrgList && response.employeeOrgList.length > 0 && response.employeeOrgList[0].position) {
            return response.employeeOrgList[0].position.id
          }
          return (response.positionList && response.positionList.length > 0) ? response.positionList[0].id : null
        })(),
        roleIds: (response.roleList || []).map((item: any) => item.id),
      }

      // 如果有部门ID，加载对应的岗位选项
      if (userForm.value.orgUnitIds) {
        await getPositionsByDeptId(userForm.value.orgUnitIds)
      }

      dialogVisible.value = true
    }
  }
  catch (error) {
    ElMessage.error('获取用户详情失败')
  }
}

// 新增用户
function handleAdd() {
  dialogTitle.value = '新增用户'
  isEdit.value = false
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑用户
async function handleEdit(row: any) {
  try {
    dialogTitle.value = '编辑用户'
    isEdit.value = true
    isView.value = false

    // 调用详情接口获取完整用户信息
    const response = await userApi.userApi({}, { id: row.id }, 'detail')

    if (response) {
      // 填充表单数据
      userForm.value = {
        id: response.id,
        username: response.username || '',
        password: '', // 编辑时密码字段为空，用户可选择是否修改
        realName: response.realName || '',
        email: response.email || '',
        phone: response.phone || '',
        gender: response.gender || 'UNKNOWN',
        type: response.type || 'EMPLOYEE',
        birthDate: response.birthDate || '',
        idCard: response.idCard || '',
        employeeNo: response.employeeNo || '',
        status: response.status || 'ACTIVE',
        hireDate: response.hireDate || '',
        leaveDate: response.leaveDate || '',
        wechatOpenId: response.wechatOpenId || '',
        wechatUnionId: response.wechatUnionId || '',
        positionList: response.positionList || [],
        orgUnitList: response.orgUnitList || [],
        roleList: response.roleList || [],
        employeeOrgList: response.employeeOrgList || [],
        // 提取ID用于表单绑定
        // 优先从 employeeOrgList 获取，如果没有则从旧格式获取
        orgUnitIds: (() => {
          if (response.employeeOrgList && response.employeeOrgList.length > 0 && response.employeeOrgList[0].orgUnit) {
            return response.employeeOrgList[0].orgUnit.id
          }
          return (response.orgUnitList && response.orgUnitList.length > 0) ? response.orgUnitList[0].id : null
        })(),
        positionIds: (() => {
          if (response.employeeOrgList && response.employeeOrgList.length > 0 && response.employeeOrgList[0].position) {
            return response.employeeOrgList[0].position.id
          }
          return (response.positionList && response.positionList.length > 0) ? response.positionList[0].id : null
        })(),
        roleIds: (response.roleList || []).map((item: any) => item.id),
      }

      // 如果有部门ID，加载对应的岗位选项
      if (userForm.value.orgUnitIds) {
        await getPositionsByDeptId(userForm.value.orgUnitIds)
      }

      dialogVisible.value = true
    }
  }
  catch (error) {
    ElMessage.error('获取用户详情失败')
  }
}

// 重置表单
function resetForm() {
  userForm.value = {
    id: null,
    username: '',
    password: '',
    realName: '',
    email: '',
    phone: '',
    gender: 'UNKNOWN',
    type: 'EMPLOYEE',
    birthDate: '',
    idCard: '',
    employeeNo: '',
    status: 'ACTIVE',
    hireDate: '',
    leaveDate: '',
    wechatOpenId: '',
    wechatUnionId: '',
    positionList: [],
    orgUnitList: [],
    roleList: [],
    employeeOrgList: [],
    // 重置ID
    orgUnitIds: null,
    positionIds: null,
    roleIds: [],
  }
  if (userFormRef.value) {
    userFormRef.value.clearValidate()
  }
}

// 关闭弹窗
function handleDialogClose() {
  dialogVisible.value = false
  isView.value = false
  resetForm()
}

// 保存用户
async function handleSave() {
  if (!userFormRef.value) { return }

  try {
    const valid = await userFormRef.value.validate()
    if (!valid) { return }

    formLoading.value = true

    let formData: any = { ...userForm.value }

    // 如果是编辑模式，则不传递密码字段（因为密码字段在编辑时被禁用）
    if (isEdit.value) {
      const { password, ...formDataWithoutPassword } = formData
      formData = formDataWithoutPassword
    }

    // 构建 employeeOrgList 格式
    if (formData.orgUnitIds && formData.positionIds) {
      formData.employeeOrgList = [{
        orgUnit: { id: formData.orgUnitIds },
        position: { id: formData.positionIds },
      }]
    }
    else {
      formData.employeeOrgList = []
    }

    formData.roleList = formData.roleIds.map((id: string) => ({ id }))

    // 删除临时的ID字段和旧格式字段
    const { orgUnitIds, positionIds, roleIds, orgUnitList, positionList, ...finalFormData } = formData

    if (isEdit.value) {
      // 编辑用户
      await userApi.userApi({}, finalFormData, 'update')
      ElMessage.success('用户更新成功')
    }
    else {
      // 新增用户
      await userApi.userApi({}, { ...finalFormData, tenantId: 1 }, 'create')
      ElMessage.success('用户创建成功')
    }

    handleDialogClose()
    getUserList() // 刷新列表
  }
  catch (error) {
    ElMessage.error(isEdit.value ? '用户更新失败' : '用户创建失败')
  }
  finally {
    formLoading.value = false
  }
}

// 切换用户状态
async function _handleToggleStatus(row: any) {
  const action = row.status === 'ACTIVE' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 "${row.realName || row.username}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 调用API更新用户状态
    const updateData = {
      ...row,
      status: row.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE',
    }

    await userApi.userApi({}, updateData, 'update')

    // 更新本地数据
    row.status = updateData.status
    row.statusText = updateData.status === 'ACTIVE' ? '启用' : '禁用'

    ElMessage.success(`${action}成功`)
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除用户
async function handleDelete(row: any) {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.realName || row.username}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 调用删除API
    await userApi.userApi({}, { id: row.id }, 'delete')
    ElMessage.success('用户删除成功')
    getUserList() // 刷新列表
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除用户失败')
    }
  }
}

// 批量操作
async function handleBatchAction(action: string) {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要操作的用户')
    return
  }

  const actionText = action === 'enable' ? '启用' : action === 'disable' ? '禁用' : '删除'

  try {
    await ElMessageBox.confirm(
      `确定要${actionText}选中的 ${selectedUsers.value.length} 个用户吗？`,
      `确认批量${actionText}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 这里应该调用批量操作的API
    ElMessage.success(`批量${actionText}成功`)
    selectedUsers.value = []
    getUserList()
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`批量${actionText}失败`)
    }
  }
}

// 显示导入弹窗
function showImportDialog() {
  importDialogVisible.value = true
}

// 处理导入数据
function handleImportData(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return userApi.importEmployees(formData)
}

// 导入成功回调
function handleImportSuccess(result: any) {
  ElMessage.success(`导入成功！共处理 ${result.successCount + result.failureCount} 行，成功 ${result.successCount} 行`)
  // 刷新用户列表
  getUserList()
}

// 导入失败回调
function handleImportError() {
  // 导入失败处理
}

// 页面加载完成后初始化图表和数据
onMounted(() => {
  nextTick(() => {
    initPieChart()
  })
  // 初始化组织架构数据
  initOrgTreeData()
  getUserList()
  getUserStatistics()
  getDepartmentList()
  getPositionList()
  getRoleList()
})

// 窗口大小变化时重新调整图表大小
window.addEventListener('resize', () => {
  if (pieChartInstance) {
    pieChartInstance.resize()
  }
})
</script>

<template>
  <div class="min-h-screen flex flex-col bg-gray-50">
    <div class="flex flex-1 overflow-hidden">
      <!-- 主内容区域 -->
      <main class="flex-1 overflow-auto p-6">
        <!-- 页面标题和操作按钮 -->
        <div class="mb-6 flex items-center justify-between">
          <h1 class="text-xl text-gray-800 font-bold">
            用户管理
          </h1>
          <div class="flex space-x-3">
            <el-button v-auth="['userManagement/index/add']" type="primary" class="!rounded-button whitespace-nowrap" @click="handleAdd">
              <!-- <el-icon class="mr-1">
                <Plus />
              </el-icon> -->
              新增用户
            </el-button>
            <el-button v-auth="['userManagement/index/import']" class="!rounded-button whitespace-nowrap" @click="showImportDialog">
              <!-- <el-icon class="mr-1">
                <Upload />
              </el-icon> -->
              导入用户
            </el-button>
            <!-- <el-button v-auth="['userManagement/index/export']" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <Download />
              </el-icon>
              导出
            </el-button> -->
          </div>
        </div>

        <div class="flex space-x-4">
          <!-- 左侧组织架构树 -->
          <div class="w-64 rounded-md bg-white p-4 shadow-sm">
            <div class="mb-4 flex items-center justify-between">
              <span class="text-gray-700 font-medium">组织架构</span>
              <el-button v-auth="['userManagement/index/refreshOrg']" text size="small">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
            <el-scrollbar class="h-[calc(100vh-280px)]">
              <el-tree
                ref="orgTreeRef"
                v-loading="loading"
                :data="orgTreeData"
                node-key="id"
                :props="treeProps"
                :expand-on-click-node="false"
                :highlight-current="true"
                default-expand-all
                @node-click="handleNodeClick"
              >
                <template #default="{ node, data }">
                  <span class="flex items-center">
                    <el-icon v-if="data.type === 'dept'" class="mr-1"><OfficeBuilding /></el-icon>
                    <el-icon v-else class="mr-1"><User /></el-icon>
                    <span :title="node.label">{{ node.label }}</span>
                  </span>
                </template>
              </el-tree>
            </el-scrollbar>
          </div>

          <!-- 中间用户列表 -->
          <div class="flex-1 overflow-hidden rounded-md bg-white shadow-sm">
            <!-- 筛选条件 -->
            <div class="border-b border-gray-100 p-4">
              <div class="flex items-center space-x-4">
                <el-select v-model="filter.status" placeholder="用户状态" class="w-32" @change="handleSearch">
                  <el-option label="全部" value="" />
                  <el-option label="启用" value="ACTIVE" />
                  <el-option label="禁用" value="INACTIVE" />
                  <el-option label="冻结" value="FROZEN" />
                </el-select>
                <el-select v-model="filter.userType" placeholder="用户类型" class="w-32" @change="handleSearch">
                  <el-option label="全部" value="" />
                  <el-option label="普通用户" :value="1" />
                  <el-option label="管理员" :value="2" />
                </el-select>
                <el-input
                  v-model="filter.keyword"
                  placeholder="搜索用户名、真实姓名、员工编号..."
                  class="max-w-xs flex-1"
                  clearable
                />
                <el-button
                  v-debounce="2000"
                  :loading="loading"
                  @click="handleSearch"
                >
                  查询
                </el-button>
                <el-button
                  v-debounce="2000"
                  :loading="loading"
                  @click="handleReset"
                >
                  重置
                </el-button>
              </div>
            </div>

            <!-- 用户表格 -->
            <el-table
              v-loading="loading"
              :data="userList"
              style="width: 100%"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="50" />
              <el-table-column prop="id" label="用户ID" width="120" />
              <el-table-column prop="username" label="用户名" width="150" />
              <el-table-column prop="realName" label="真实姓名" width="120" />
              <el-table-column prop="employeeNo" label="员工编号" width="120" />
              <el-table-column prop="phone" label="手机号码" width="150" />
              <el-table-column prop="email" label="邮箱" width="200" />
              <el-table-column prop="genderText" label="性别" width="80" />
              <el-table-column prop="departmentName" label="部门" width="150" />
              <el-table-column prop="positionName" label="岗位" width="150" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.status === 'ACTIVE' ? 'success' : row.status === 'INACTIVE' ? 'info' : 'warning'" size="small">
                    {{ row.statusText }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="lastLoginText" label="最后登录时间" width="180" />
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="{ row }">
                  <div class="flex justify-between">
                    <el-button v-auth="['userManagement/index/edit']" type="primary" plain size="small" @click="handleEdit(row)">
                      编辑
                    </el-button>
                    <el-button v-auth="['userManagement/index/delete']" size="small" type="danger" plain @click="handleDelete(row)">
                      删除
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="flex items-center justify-between p-4">
              <div v-if="selectedUsers.length > 0" class="flex items-center space-x-3">
                <span class="text-sm text-gray-500">已选择 {{ selectedUsers.length }} 项</span>
                <el-button v-auth="['userManagement/index/batchEnable']" size="small" class="!rounded-button whitespace-nowrap" @click="handleBatchAction('enable')">
                  <el-icon class="mr-1">
                    <Check />
                  </el-icon>
                  批量启用
                </el-button>
                <el-button v-auth="['userManagement/index/batchDisable']" size="small" class="!rounded-button whitespace-nowrap" @click="handleBatchAction('disable')">
                  <el-icon class="mr-1">
                    <Close />
                  </el-icon>
                  批量禁用
                </el-button>
                <el-button v-auth="['userManagement/index/batchDelete']" size="small" class="!rounded-button whitespace-nowrap" @click="handleBatchAction('delete')">
                  <el-icon class="mr-1">
                    <Delete />
                  </el-icon>
                  批量删除
                </el-button>
                <el-button v-auth="['userManagement/index/cancelSelection']" size="small" text @click="selectedUsers = []">
                  取消选择
                </el-button>
              </div>
              <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                class="ml-auto"
                @current-change="handlePageChange"
                @size-change="handlePageChange"
              />
            </div>
          </div>

          <!-- 右侧用户概览 -->
          <div v-if="false" class="w-72 rounded-md bg-white p-4 shadow-sm">
            <h3 class="mb-4 text-lg text-gray-800 font-bold">
              用户概览
            </h3>

            <!-- 统计卡片 -->
            <div class="grid grid-cols-2 mb-6 gap-3">
              <div class="rounded-md bg-blue-50 p-3">
                <div class="mb-1 text-sm text-gray-500">
                  总用户数
                </div>
                <div class="text-xl text-blue-600 font-bold">
                  {{ userStatistics.totalUsers }}
                </div>
              </div>
              <div class="rounded-md bg-green-50 p-3">
                <div class="mb-1 text-sm text-gray-500">
                  管理员
                </div>
                <div class="text-xl text-green-600 font-bold">
                  {{ userStatistics.roleStatistics.adminUsers }}
                </div>
              </div>
              <div class="rounded-md bg-purple-50 p-3">
                <div class="mb-1 text-sm text-gray-500">
                  普通用户
                </div>
                <div class="text-xl text-purple-600 font-bold">
                  {{ userStatistics.roleStatistics.regularUsers }}
                </div>
              </div>
              <div class="rounded-md bg-yellow-50 p-3">
                <div class="mb-1 text-sm text-gray-500">
                  非活跃用户
                </div>
                <div class="text-xl text-yellow-600 font-bold">
                  {{ userStatistics.statusStatistics.inactiveUsers }}
                </div>
              </div>
            </div>

            <!-- 部门分布饼图 -->
            <div class="mb-6">
              <div class="mb-3 text-sm text-gray-700 font-medium">
                部门分布
              </div>
              <div ref="pieChart" class="h-48" />
            </div>

            <!-- 最近活动 -->
            <div v-if="false">
              <div class="mb-3 text-sm text-gray-700 font-medium">
                最近活动
              </div>
              <el-scrollbar class="h-48">
                <div class="space-y-3">
                  <div v-for="(item, index) in recentActivities" :key="index" class="flex items-start">
                    <el-avatar :size="32" :src="item.avatar" class="mr-3" />
                    <div>
                      <div class="text-sm text-gray-800 font-medium">
                        {{ item.username }}
                      </div>
                      <div class="text-xs text-gray-500">
                        {{ item.action }}
                      </div>
                      <div class="text-xs text-gray-400">
                        {{ item.time }}
                      </div>
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- 用户新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="formRules"
        label-width="120px"
        class="max-h-[60vh] overflow-y-auto pr-2.5"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="userForm.username"
                placeholder="请输入用户名"
                :disabled="isEdit || isView"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <div class="flex space-x-2">
                <el-input
                  v-model="userForm.password"
                  :disabled="isEdit"
                  type="password"
                  :placeholder="isEdit ? '******' : '请输入密码'"
                  show-password
                  class="flex-1"
                />
                <el-button
                  type="primary"
                  size="default"
                  :disabled="isEdit"
                  @click="handleGeneratePassword"
                >
                  生成密码
                </el-button>
              </div>
              <div class="mt-1 text-xs text-gray-500">
                <span v-if="isEdit" />
                <span v-else>密码要求：至少8个字符，包含大小写字母、数字和特殊字符(@#$%^&+=)，不能包含空格</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input
                v-model="userForm.realName"
                placeholder="请输入真实姓名"
                :disabled="isView"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="员工编号" prop="employeeNo">
              <el-input
                v-model="userForm.employeeNo"
                placeholder="请输入员工编号"
                :disabled="isView"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="userForm.email"
                placeholder="请输入邮箱地址"
                :disabled="isView"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="userForm.phone"
                placeholder="请输入手机号码"
                :disabled="isView"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户类型">
              <el-select
                v-model="userForm.userType"
                placeholder="请选择用户类型"
                style="width: 100%"
              >
                <el-option label="内部用户" value="1" />
                <el-option label="外部用户" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row> -->

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="部门" prop="orgUnitIds">
              <el-select
                v-model="userForm.orgUnitIds"
                placeholder="请选择部门"
                :disabled="isView"
                style="width: 100%"
                @change="handleDepartmentChange"
              >
                <el-option
                  v-for="dept in departmentOptions"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位" prop="positionIds">
              <el-select
                v-model="userForm.positionIds"
                placeholder="请选择岗位"
                :disabled="isView"
                style="width: 100%"
              >
                <el-option
                  v-for="position in positionOptions"
                  :key="position.id"
                  :label="position.name"
                  :value="position.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="角色" prop="roleIds">
              <el-select
                v-model="userForm.roleIds"
                placeholder="请选择角色"
                :disabled="isView"
                multiple
                style="width: 100%"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="role in roleOptions"
                  :key="role.id"
                  :label="role.name"
                  :value="role.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 可选字段区域 -->

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别">
              <el-select v-model="userForm.gender" placeholder="请选择性别" style="width: 100%">
                <el-option label="未知" value="UNKNOWN" />
                <el-option label="男" value="MALE" />
                <el-option label="女" value="FEMALE" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-select v-model="userForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="启用" value="ACTIVE" />
                <el-option label="禁用" value="INACTIVE" />
                <el-option label="锁定" value="LOCKED" />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="用户类型">
              <el-select v-model="userForm.type" placeholder="请选择用户类型" style="width: 100%">
                <el-option label="员工" value="EMPLOYEE" />
                <el-option label="管理员" value="ADMIN" />
              </el-select>
            </el-form-item> -->
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生日">
              <el-date-picker
                v-model="userForm.birthDate"
                type="date"
                placeholder="请选择生日"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input
                v-model="userForm.idCard"
                placeholder="请输入身份证号"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="入职日期">
              <el-date-picker
                v-model="userForm.hireDate"
                type="date"
                placeholder="请选择入职日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="离职日期">
              <el-date-picker
                v-model="userForm.leaveDate"
                type="date"
                placeholder="请选择离职日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="微信OpenID">
              <el-input
                v-model="userForm.wechatOpenId"
                placeholder="请输入微信OpenID"
                :disabled="isView"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="微信UnionID">
              <el-input
                v-model="userForm.wechatUnionId"
                placeholder="请输入微信UnionID"
                :disabled="isView"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button v-auth="['userManagement/dialog/cancel']" @click="handleDialogClose">
            {{ isView ? '关闭' : '取消' }}
          </el-button>
          <el-button v-if="!isView" v-auth="['userManagement/dialog/confirm']" type="primary" :loading="formLoading" @click="handleSave">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入弹窗 -->
    <ImportComponent
      v-model:visible="importDialogVisible"
      title="导入用户"
      :download-template-api="userApi.downloadEmployeeTemplate"
      :import-data-api="handleImportData"
      template-file-name="企业员工信息模板.xlsx"
      accept-file-types=".xlsx,.xls"
      :max-file-size="10"
      :show-download-template="true"
      @success="handleImportSuccess"
      @error="handleImportError"
    />
  </div>
</template>

<style scoped>
.el-menu {
  border-right: none;
}

.el-tree {
  background: transparent;
}

.el-table {
  --el-table-header-bg-color: #f5f7fa;
}

.el-pagination {
  justify-content: flex-end;
}

:deep(.el-input-group__append) {
  background-color: var(--el-color-primary);
  color: white;
}

:deep(.el-input-group__append) .el-icon {
  color: white;
}
</style>
