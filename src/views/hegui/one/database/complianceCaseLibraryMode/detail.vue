<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'

const tabsList = ref([
  {
    id: 1,
    name: '基本信息',
  },
  // {
  //   id: 2,
  //   name: '关联信息',
  // },
  // {
  //   id: 3,
  //   name: '风险分析',
  // },
])

const activeName = ref(1)
const router = useRouter()
const route = useRoute()

// 案例详情数据
const caseDetail = ref({
  id: '',
  caseName: '',
  caseCode: '',
  areaType: '',
  caseSource: '',
  level: '',
  summary: '',
  keyword: '',
  backgroundDesc: '',
  occurDate: '',
  mediaUrl: '',
  violation: '',
  riskAnalysis: '',
  preventionControl: '',
  learningPoints: '',
  trainingUrl: '',
  createdBy: '',
  createdAt: null,
  updatedBy: '',
  updatedAt: null,
})

// 风险等级映射
const riskLevelMap: any = {
  GENERAL: { text: '一般风险', type: 'info' },
  MAJOR: { text: '重大风险', type: 'danger' },
  TYPICAL: { text: '典型风险', type: 'warning' },
  SAFE: { text: '安全', type: 'success' },
}

// 案例来源映射
const caseSourceMap: any = {
  REGULATORY_PENALTIES: '监管处罚',
  JUDICIAL_PRECEDENTS: '司法判例',
  INDUSTRY: '行业案例',
  INTERNAL: '内部案例',
}

// 领域类型映射
const areaTypeMap: any = {
  INDUSTRY_REGULATION: '行业监管',
  CORPORATE_GOVERNANCE: '公司治理',
  BUSINESS_OPERATIONS: '业务运营',
  FINANCE_TAXATION: '财务税务',
}

// 获取案例详情
async function getCaseDetail() {
  try {
    const id = route.query.id
    if (!id) {
      ElMessage.error('案例ID不能为空')
      return
    }
    const response = await systemApi.caseSystem({ id }, 'info')
    if (response) {
      caseDetail.value = response
    }
  }
  catch (error) {
    ElMessage.error('获取案例详情失败')
  }
}

// 删除案例
async function deleteCase() {
  try {
    await ElMessageBox.confirm(
      `确定要删除「${caseDetail.value.caseName}」吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await systemApi.caseSystem({ id: caseDetail.value.id }, 'delete')
    ElMessage.success('删除成功')
    router.push({ name: '/database/cases' })
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

function goAddEdit() {
  router.push({
    name: '/database/cases/addEdit',
    query: { id: caseDetail.value.id },
  })
}

// 格式化日期
function _formatDate(timestamp: any) {
  if (!timestamp) {
    return '-'
  }
  if (timestamp.seconds) {
    return new Date(timestamp.seconds * 1000).toLocaleDateString()
  }
  return new Date(timestamp).toLocaleDateString()
}

// Tab点击处理
function handleClick(_tab: any, _event: Event) {
  // Tab切换逻辑
}

// 页面初始化
onMounted(() => {
  getCaseDetail()
})
</script>

<template>
  <div class="absolute-container">
    <!-- <page-header title="" content="">
      <template #content>
        <div class="flex aic jcsb">
          <div class="flex aic">
            <div class="f-20 fw-600 mr-16">
              数据安全管理义务
            </div>
            <el-tag type="success">生效中</el-tag>
          </div>
          <div>
            <el-button type="primary" @click="goAddEdit(null)">
              <svg-icon name="ep:edit" />
              <span class="ml-8">编辑</span>
            </el-button>
            <el-button>
              <svg-icon name="ep:share" />
              <span class="ml-8">导出</span>
            </el-button>
            <el-button>
              <svg-icon name="ep:share" />
              <span class="ml-8">编辑</span>
            </el-button>
            <el-button>
              <svg-icon name="ep:paperclip" />
              <span class="ml-8">关联制度</span>
            </el-button>
          </div>

        </div>
      </template>
    </page-header> -->
    <PageMain style="background-color: transparent;">
      <el-card shadow="hover" class="action-card">
        <div class="aic jcsb flex">
          <div class="f-30 mr-16 flex-1 truncate fw-600" :title="caseDetail.caseName">
            {{ caseDetail.caseName }}
          </div>
          <div class="flex-shrink-0">
            <el-button type="primary" @click="goAddEdit()">
              <svg-icon name="ep:edit" />
              <span class="ml-8">编辑</span>
            </el-button>
            <!-- <el-button>
              <svg-icon name="ep:share" />
              <span class="ml-8">分享</span>
            </el-button>
            <el-button>
              <svg-icon name="ep:download" />
              <span class="ml-8">下载</span>
            </el-button> -->
            <el-button type="danger" @click="deleteCase()">
              <svg-icon name="ep:delete" />
              <span class="ml-8">删除</span>
            </el-button>
          </div>
        </div>
        <div class="mt-24">
          <el-tag class="mr-24" style="background: #e6f4ff;border: 1px solid #91caff;border-radius: 4px;">
            {{ caseSourceMap[caseDetail.caseSource] || caseDetail.caseSource || '未知来源' }}
          </el-tag>
          <el-tag class="mr-24" :type="riskLevelMap[caseDetail.level]?.type || 'info'">
            {{ riskLevelMap[caseDetail.level]?.text || caseDetail.level || '未知风险' }}
          </el-tag>
        </div>
        <div class="f-14 mt-24">
          <el-row>
            <el-col :span="8">
              <span class="c-[#999]">案例编号：</span>
              <span>{{ caseDetail.caseCode || '未设置' }}</span>
            </el-col>
            <el-col :span="8">
              <span class="c-[#999]">发生时间：</span>
              <span>{{ caseDetail.occurDate || '未设置' }}</span>
            </el-col>
            <el-col :span="8">
              <span class="c-[#999]">领域类型：</span>
              <span>{{ areaTypeMap[caseDetail.areaType] || caseDetail.areaType || '未设置' }}</span>
            </el-col>
          </el-row>
        </div>
        <div class="f-14 mt-24">
          <el-row>
            <el-col :span="8">
              <span class="c-[#999]">来源机构：</span>
              <span>中国银保监会</span>
            </el-col>
            <el-col :span="8">
              <span class="c-[#999]">处罚日期：</span>
              <span>2023-01-15</span>
            </el-col>
            <el-col :span="8">
              <span class="c-[#999]">影响范围：</span>
              <span>全国性</span>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="card mt-24 p-24">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane v-for="i, j in tabsList" :key="j" :label="i.name" :name="i.id">
            <div v-if="activeName === 1">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>案例概述</span>
                  </div>
                </template>
                <div>
                  <div class="f-14 fw-400">
                    {{ caseDetail.summary || '暂无案例概述' }}
                  </div>
                  <div v-if="caseDetail.keyword" class="mt-24">
                    <span>关键词：</span>
                    <el-tag type="info" class="mr-16">
                      {{ caseDetail.keyword }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
              <el-card class="mt-24">
                <template #header>
                  <div class="card-header">
                    <span>详细内容</span>
                  </div>
                </template>
                <div v-if="caseDetail.backgroundDesc">
                  <div class="f-20 fw-600">
                    案例背景
                  </div>
                  <div class="f-14 mt-12">
                    {{ caseDetail.backgroundDesc }}
                  </div>
                </div>
                <div v-if="caseDetail.violation" class="mt-24">
                  <div class="f-20 fw-600">
                    违规点分析
                  </div>
                  <div class="f-14 mt-12">
                    {{ caseDetail.violation }}
                  </div>
                </div>
                <div v-if="caseDetail.riskAnalysis" class="mt-24">
                  <div class="f-20 fw-600">
                    风险分析
                  </div>
                  <div class="f-14 mt-12">
                    {{ caseDetail.riskAnalysis }}
                  </div>
                </div>
                <div v-if="caseDetail.preventionControl" class="mt-24">
                  <div class="f-20 fw-600">
                    防控建议
                  </div>
                  <div class="f-14 mt-12">
                    {{ caseDetail.preventionControl }}
                  </div>
                </div>
              </el-card>
            </div>
            <div v-if="activeName === 2" />
            <div v-if="activeName === 3" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }
</style>
