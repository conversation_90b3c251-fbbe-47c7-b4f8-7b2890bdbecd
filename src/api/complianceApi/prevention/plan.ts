import api from '@/api/index'

export default {
  // 培训计划列表 - 多条件分页查询
  getLearningPlanList: (data: any) => {
    return api.post('/whiskerguardtrainingservice/api/training/plans/query', data)
  },

  // 创建培训计划
  createTrainingPlan: (data: any) => {
    return api.post('/whiskerguardtrainingservice/api/training/plans', data)
  },
  // 详情
  getTrainingPlanDetail: (id: any) => {
    return api.get(`/whiskerguardtrainingservice/api/training/plans/${id}/withCourses`)
  },
  // 更新
  updateTrainingPlan: (id: any, data: any) => {
    return api.patch(`/whiskerguardtrainingservice/api/training/plans/${id}`, data)
  },
  // 删除
  deleteTrainingPlan: (id: any) => {
    return api.delete(`/whiskerguardtrainingservice/api/training/plans/${id}`)
  },
}
