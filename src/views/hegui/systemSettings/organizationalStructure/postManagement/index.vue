<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check,
  Close,
  Delete,
  Download,
  Edit,
  Plus,
  Search,
  Setting,
  Upload,
  User,
  View,
} from '@element-plus/icons-vue'
import organizationalApi from '@/api/organizational/index'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'

// 筛选条件
const filter = ref({
  type: '',
  departments: '',
  keyword: '',
})

// 表格数据
const jobList = ref([])
const loading = ref(false)

// 弹窗状态
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)

// 表单数据
const formData = ref({
  id: null,
  tenantId: 0,
  code: '',
  name: '',
  level: 1,
  category: 'MANAGEMENT',
  description: '',
  metadata: '',
  version: 0,
  isDeleted: false,
  orgUnitId: null, // 添加部门字段
})

// 表单验证规则
const formRules = {
  code: [{ required: true, message: '请输入岗位编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入岗位名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择岗位分类', trigger: 'change' }],
}

// 表单引用
const formRef = ref()

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 选择
const selectedJobs = ref([])

// 图表
const pieChart = ref<HTMLElement>()
const barChart = ref<HTMLElement>()

// API调用方法
async function fetchPositions() {
  try {
    loading.value = true
    const paging = {
      page: currentPage.value - 1, // 后端从0开始
      size: pageSize.value,
    }
    const params = {}
    if (filter.value.departments) {
      params.orgUnitId = filter.value.departments
    }
    if (filter.value.type) {
      params.category = filter.value.type
    }
    if (filter.value.keyword) {
      params.keyword = filter.value.keyword
    }
    const response = await organizationalApi.positionApi(paging, params, null)
    // API返回的是数组，不是分页对象
    jobList.value = response.content || []
    // 暂时设置总数为当前页数据长度，实际应该从响应头或其他地方获取
    total.value = response.totalElements || 0
  }
  catch (error) {
    console.error('获取岗位列表失败:', error)
    ElMessage.error('获取岗位列表失败')
  }
  finally {
    loading.value = false
  }
}

// 重置表单
function resetForm() {
  formData.value = {
    id: null,
    tenantId: 1,
    code: '',
    name: '',
    level: 1,
    category: 'MANAGEMENT',
    description: '',
    orgUnitId: null,
  }
  formRef.value?.clearValidate()
}

// 新增岗位
function addPosition() {
  resetForm()
  dialogTitle.value = '新增岗位'
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑岗位
function editJob(job: any) {
  resetForm()
  formData.value = { ...job }
  dialogTitle.value = '编辑岗位'
  isEdit.value = true
  dialogVisible.value = true
}

// 保存岗位
async function savePosition() {
  try {
    await formRef.value?.validate()
    loading.value = true

    const apiKey = isEdit.value ? 'update' : 'create'
    await organizationalApi.positionApi({}, formData.value, apiKey)

    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    dialogVisible.value = false
    await fetchPositions()
  }
  catch (error) {
    console.error('保存岗位失败:', error)
    ElMessage.error('保存岗位失败')
  }
  finally {
    loading.value = false
  }
}

// 删除岗位
async function deleteJob(row: any) {
  try {
    await ElMessageBox.confirm('确定要删除这个岗位吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await organizationalApi.positionApi({}, { id: row.id }, 'delete')
    ElMessage.success('删除成功')
    await fetchPositions()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除岗位失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 方法
function getTagType(category: string) {
  const types = {
    MANAGEMENT: 'success',
    TECHNICAL: 'warning',
    BUSINESS: 'success',
    SUPPORT: 'info',
  }
  return types[category] || 'info'
}

function getCategoryName(category: string) {
  const names = {
    MANAGEMENT: '管理岗',
    TECHNICAL: '技术岗',
    BUSINESS: '业务岗',
    SUPPORT: '支持岗',
  }
  return names[category] || category
}

function resetFilters() {
  filter.value = {
    type: '',
    departments: '',
    keyword: '',
  }
  fetchPositions()
}

function applyFilters() {
  currentPage.value = 1
  fetchPositions()
}

function handleSelectionChange(val: any[]) {
  selectedJobs.value = val
}

function clearSelection() {
  selectedJobs.value = []
}

function viewJob(job: any) {
  console.log('查看岗位:', job)
}

function manageEmployees(job: any) {
  console.log('管理人员:', job)
}

function toggleStatus(job: any) {
  job.isDeleted = !job.isDeleted
  console.log('切换状态:', job)
}

function batchEnable() {
  console.log('批量启用:', selectedJobs.value)
}

function batchDisable() {
  console.log('批量禁用:', selectedJobs.value)
}

function batchDelete() {
  console.log('批量删除:', selectedJobs.value)
}

function batchSetDepartment() {
  console.log('批量设置部门:', selectedJobs.value)
}

// 分页变化
function handlePageChange() {
  fetchPositions()
}

function handleSizeChange() {
  currentPage.value = 1
  fetchPositions()
}

// 初始化图表
function initCharts() {
  if (pieChart.value && barChart.value) {
    const pieChartInstance = echarts.init(pieChart.value)
    const barChartInstance = echarts.init(barChart.value)

    pieChartInstance.setOption({
      animation: false,
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: ['管理岗', '专业岗', '技术岗', '操作岗'],
      },
      series: [
        {
          name: '岗位类型',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 12, name: '管理岗' },
            { value: 18, name: '专业岗' },
            { value: 8, name: '技术岗' },
            { value: 4, name: '操作岗' },
          ],
        },
      ],
    })

    barChartInstance.setOption({
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01],
      },
      yAxis: {
        type: 'category',
        data: ['财务部', '技术部', '人力资源部', '市场部', '行政部'],
      },
      series: [
        {
          name: '岗位数量',
          type: 'bar',
          data: [6, 8, 4, 3, 2],
          itemStyle: {
            color: '#1E88E5',
          },
        },
      ],
    })

    window.addEventListener('resize', () => {
      pieChartInstance.resize()
      barChartInstance.resize()
    })
  }
}

onMounted(() => {
  fetchPositions()
  nextTick(() => {
    initCharts()
  })
})
</script>

<template>
  <div class="absolute-container" style="">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              岗位管理
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'postManagement/index/add'" type="primary" class="!rounded-button whitespace-nowrap" @click="addPosition">
              <!-- <el-icon class="mr-1">
                <Plus />
              </el-icon> -->
              新增岗位
            </el-button>
            <el-button v-auth="'postManagement/index/import'" class="!rounded-button whitespace-nowrap">
              <!-- <el-icon class="mr-1">
                <Upload />
              </el-icon> -->
              导入岗位
            </el-button>
            <el-button v-auth="'postManagement/index/export'" class="!rounded-button whitespace-nowrap">
              <!-- <el-icon class="mr-1">
                <Download />
              </el-icon> -->
              导出
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <div class="flex items-center space-x-4">
                <el-select v-model="filter.type" placeholder="岗位类型" clearable class="w-40">
                  <el-option label="管理岗" value="MANAGEMENT" />
                  <el-option label="专业岗" value="PROFESSIONAL" />
                  <el-option label="技术岗" value="TECHNICAL" />
                  <el-option label="操作岗" value="OPERATIONAL" />
                </el-select>

                <DepartmentTreeSelect
                  v-model="filter.departments"
                  placeholder="所属部门"
                  clearable
                  width="12rem"
                />

                <el-button v-auth="'postManagement/index/reset'" class="!rounded-button whitespace-nowrap" @click="resetFilters">
                  重置
                </el-button>
                <el-button v-auth="'postManagement/index/filter'" type="primary" class="!rounded-button whitespace-nowrap" @click="applyFilters">
                  筛选
                </el-button>

                <div class="flex-1" />

                <el-input v-model="filter.keyword" placeholder="搜索岗位名称、编码..." class="w-64">
                  <template #prefix>
                    <el-icon class="el-input__icon">
                      <Search />
                    </el-icon>
                  </template>
                </el-input>
              </div>
              <!-- </div> -->

              <!-- 岗位列表区 -->
              <div class="overflow-hidden rounded-lg bg-white shadow-sm">
                <el-table :data="jobList" :loading="loading" style="width: 100%;" @selection-change="handleSelectionChange">
                  <el-table-column type="selection" width="50" />
                  <el-table-column prop="code" label="岗位编码" width="120" sortable />
                  <el-table-column prop="name" label="岗位名称" width="180" sortable />
                  <el-table-column prop="category" label="岗位类型" width="120">
                    <template #default="{ row }">
                      <el-tag v-if="row.category" :type="getTagType(row.category)" size="small">
                        {{ getCategoryName(row.category) }}
                      </el-tag>
                      <span v-else class="text-gray-400">-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="orgUnitName" label="所属部门" width="150" />
                  <el-table-column prop="description" label="描述" width="200" show-overflow-tooltip />
                  <el-table-column prop="level" label="岗位等级" width="100" />
                  <el-table-column prop="version" label="版本" width="80" align="center" />

                  <el-table-column prop="createdAt" label="创建时间" width="120" />
                  <el-table-column label="操作" width="220">
                    <template #default="{ row }">
                      <!-- <el-button v-auth="'postManagement/index/view'" type="text" size="small" @click="viewJob(row)">
                        <el-icon>
                          <View />
                        </el-icon>
                      </el-button> -->
                      <el-button v-auth="'postManagement/index/edit'" type="primary" plain @click="editJob(row)">
                        编辑
                      </el-button>
                      <!-- <el-button v-auth="'postManagement/index/manageEmployees'" type="text" size="small" @click="manageEmployees(row)">
                        <el-icon>
                          <User />
                        </el-icon>
                      </el-button> -->
                      <!-- <el-button v-auth="'postManagement/index/toggleStatus'" type="text" size="small" @click="toggleStatus(row)">
                        <el-icon v-if="!row.isDeleted">
                          <Close />
                        </el-icon>
                        <el-icon v-else>
                          <Check />
                        </el-icon>
                      </el-button> -->
                      <el-button v-auth="'postManagement/index/delete'" type="danger" plain @click="deleteJob(row)">
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="flex items-center justify-between border-t border-gray-200 p-4">
                  <div>
                    <el-button v-if="selectedJobs.length > 0" type="text" @click="clearSelection">
                      已选择 {{ selectedJobs.length }} 项
                    </el-button>
                  </div>
                  <el-pagination
                    v-model:current-page="currentPage" v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
                    @current-change="handlePageChange" @size-change="handleSizeChange"
                  />
                </div>

                <!-- 批量操作栏 -->
                <div
                  v-if="false"
                  class="flex items-center border-t border-gray-200 bg-gray-50 p-3 space-x-3"
                >
                  <span class="text-sm text-gray-600">已选择 {{ selectedJobs.length }} 项</span>
                  <el-button v-auth="'postManagement/index/batchEnable'" size="small" class="!rounded-button whitespace-nowrap" @click="batchEnable">
                    <el-icon class="mr-1">
                      <Check />
                    </el-icon>批量启用
                  </el-button>
                  <el-button v-auth="'postManagement/index/batchDisable'" size="small" class="!rounded-button whitespace-nowrap" @click="batchDisable">
                    <el-icon class="mr-1">
                      <Close />
                    </el-icon>批量禁用
                  </el-button>
                  <el-button v-auth="'postManagement/index/batchDelete'" size="small" class="!rounded-button whitespace-nowrap" @click="batchDelete">
                    <el-icon class="mr-1">
                      <Delete />
                    </el-icon>批量删除
                  </el-button>
                  <el-button v-auth="'postManagement/index/batchSetDepartment'" size="small" class="!rounded-button whitespace-nowrap" @click="batchSetDepartment">
                    <el-icon class="mr-1">
                      <Setting />
                    </el-icon>批量设置部门
                  </el-button>
                  <el-button size="small" type="text" @click="clearSelection">
                    取消选择
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  岗位概览
                </div>
              </template>
              <div class="grid grid-cols-2 mb-6 gap-4">
                <div class="rounded-lg bg-gray-50 p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    总岗位数
                  </div>
                  <div class="text-2xl text-gray-800 font-bold">
                    42
                  </div>
                </div>
                <div class="rounded-lg bg-gray-50 p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    管理岗位
                  </div>
                  <div class="text-2xl text-gray-800 font-bold">
                    12
                  </div>
                </div>
                <div class="rounded-lg bg-gray-50 p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    专业岗位
                  </div>
                  <div class="text-2xl text-gray-800 font-bold">
                    18
                  </div>
                </div>
                <div class="rounded-lg bg-gray-50 p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    其他岗位
                  </div>
                  <div class="text-2xl text-gray-800 font-bold">
                    12
                  </div>
                </div>
              </div>

              <div class="mb-6">
                <h4 class="text-md mb-3 text-gray-700 font-semibold">
                  岗位类型分布
                </h4>
                <div ref="pieChart" class="h-48" />
              </div>

              <div>
                <h4 class="text-md mb-3 text-gray-700 font-semibold">
                  部门岗位统计
                </h4>
                <div ref="barChart" class="h-64" />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>

    <!-- 新增/编辑岗位弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="岗位编码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入岗位编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入岗位名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="岗位分类" prop="category">
              <el-select v-model="formData.category" placeholder="请选择岗位分类" style="width: 100%">
                <el-option label="管理岗" value="MANAGEMENT" />
                <el-option label="技术岗" value="TECHNICAL" />
                <el-option label="业务岗" value="BUSINESS" />
                <el-option label="支持岗" value="SUPPORT" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位等级">
              <el-input-number v-model="formData.level" :min="1" :max="20" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="所属部门" prop="orgUnitId">
              <DepartmentTreeSelect
                v-model="formData.orgUnitId"
                placeholder="请选择所属部门"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述信息">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入岗位描述" />
        </el-form-item>

        <el-form-item label="扩展元数据">
          <el-input v-model="formData.metadata" type="textarea" :rows="2" placeholder="请输入扩展元数据（JSON格式）" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" :loading="loading" @click="savePosition">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-menu {
    border-right: none;
  }

  .el-table {
    --el-table-border-color: #f0f2f5;
  }

  .el-table :deep(.el-table__header-wrapper) th {
    background-color: #f5f7fa;
  }

  .el-table :deep(.el-table__row:hover) {
    background-color: #f5f9ff;
  }

  .el-tag {
    --el-tag-border-radius: 4px;
  }

  .el-button--text {
    min-height: auto;
    padding: 0;
  }

  .el-badge {
    --el-badge-size: 18px;
  }

  .el-dropdown-link {
    display: flex;
    align-items: center;
  }

  .el-select {
    --el-select-height: 36px;
  }

  .el-input {
    --el-input-height: 36px;
  }

  .el-pagination {
    --el-pagination-button-height: 32px;
  }

  .el-switch {
    --el-switch-on-color: #67c23a;
    --el-switch-off-color: #f56c6c;
  }
</style>
