<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import planApi from '@/api/complianceApi/prevention/plan'

// 定义组件名称，解决 keep-alive 缓存问题
defineOptions({
  name: 'TrainingPlan',
})

// 路由实例
const router = useRouter()

// 接口类型定义
interface TrainingPlan {
  id: number
  planCode: string
  planName: string
  planDescription: string
  planType: string
  planStatus: string
  startDate: any
  endDate: any
  trainingTarget: string
  responsiblePerson: string
  trainingObjective: string
  implementationPlan: string
  resourceRequirements: string
  riskManagement: string
  targetCompletionRate: number
  creatorType: string
  priority: string
  coverImageUrl: string
  metadata: string
  courseCount: number
  version: number
  createdBy: string
  createdAt: any
  updatedBy: string
  updatedAt: any
  isDeleted: boolean
  trainingPlanCourses: any[]
}

interface TrainingFilters {
  planStatus: string
  trainingTarget: string
  dateRange: string[]
  planName: string
  planType: string
  priority: string
  responsiblePerson: string
}

// 响应式数据
const loading = ref(false)
const trainingPage = ref(1)
const trainingPageSize = ref(10)
const trainingTotal = ref(0)
const trainingFilters = ref<TrainingFilters>({
  planStatus: '',
  trainingTarget: '',
  dateRange: [],
  planName: '',
  planType: '',
  priority: '',
  responsiblePerson: '',
})

// 培训计划列表数据
const trainingPlans = ref<TrainingPlan[]>([])

// 获取培训计划列表
async function getTrainingPlanList() {
  try {
    loading.value = true

    // 构建查询参数
    const queryParams: any = {
      page: trainingPage.value - 1, // 后端页码从0开始
      size: trainingPageSize.value,
    }

    // 添加筛选条件
    if (trainingFilters.value.planStatus) {
      queryParams.planStatus = trainingFilters.value.planStatus
    }
    if (trainingFilters.value.trainingTarget) {
      queryParams.trainingTarget = trainingFilters.value.trainingTarget
    }
    if (trainingFilters.value.planName.trim()) {
      queryParams.planName = trainingFilters.value.planName.trim()
    }
    if (trainingFilters.value.planType) {
      queryParams.planType = trainingFilters.value.planType
    }
    if (trainingFilters.value.priority) {
      queryParams.priority = trainingFilters.value.priority
    }
    if (trainingFilters.value.responsiblePerson) {
      queryParams.responsiblePerson = trainingFilters.value.responsiblePerson
    }

    // 处理日期范围
    if (trainingFilters.value.dateRange && trainingFilters.value.dateRange.length === 2) {
      const [startDate, endDate] = trainingFilters.value.dateRange
      queryParams.startDateFrom = startDate
      queryParams.startDateTo = endDate
    }

    const response = await planApi.getLearningPlanList(queryParams)

    if (response && response.content) {
      trainingPlans.value = response.content
      trainingTotal.value = response.totalElements || 0
    }
    else {
      trainingPlans.value = []
      trainingTotal.value = 0
    }
  }
  catch (error) {
    console.error('获取培训计划列表失败:', error)
    ElMessage.error('获取培训计划列表失败')
    trainingPlans.value = []
    trainingTotal.value = 0
  }
  finally {
    loading.value = false
  }
}

// 进度条颜色配置
const progressColors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#6f7ad3', percentage: 100 },
]

// 工具函数
function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    DRAFT: '草稿',
    PENDING: '待启动',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消',
  }
  return statusMap[status] || status
}

function getStatusType(status: string): 'info' | 'warning' | 'success' | 'primary' | 'danger' {
  const typeMap: Record<string, 'info' | 'warning' | 'success' | 'primary' | 'danger'> = {
    DRAFT: 'info',
    PENDING: 'warning',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    CANCELLED: 'danger',
  }
  return typeMap[status] || 'info'
}

// 获取优先级类型
function getPriorityType(priority: string): 'danger' | 'warning' | 'info' {
  if (priority === 'HIGH') {
    return 'danger'
  }
  if (priority === 'MIDDLE') {
    return 'warning'
  }
  return 'info'
}

// 获取优先级文本
function getPriorityText(priority: string): string {
  if (priority === 'HIGH') {
    return '高'
  }
  if (priority === 'MIDDLE') {
    return '中'
  }
  if (priority === 'LOW') {
    return '低'
  }
  return '-'
}

// 格式化日期
function formatDate(dateObj: any): string {
  if (!dateObj) {
    return '-'
  }

  let date: Date
  if (dateObj.seconds) {
    // 处理后端返回的时间格式
    date = new Date(dateObj.seconds * 1000)
  }
  else if (typeof dateObj === 'string') {
    date = new Date(dateObj)
  }
  else {
    return '-'
  }

  return date.toLocaleDateString('zh-CN')
}

// 计算课程数量
function getCourseCount(plan: TrainingPlan): number {
  return plan.courseCount || 0
}

// 事件处理函数
function goAddEdit(item: any) {
  if (item?.id) {
    // 编辑计划
    router.push({
      name: '/training/plan/edit',
      query: { id: item.id },
    })
  }
  else {
    // 新增计划
    router.push({
      name: '/training/plan/edit',
    })
  }
}

// 查看计划详情
function goDetail(row: TrainingPlan) {
  router.push({
    name: '/training/plan/detail',
    query: { id: row.id },
  })
}
async function goPatch(row: TrainingPlan) {
  try {
    await planApi.updateTrainingPlan(row.id, { id: row.id, planStatus: 'IN_PROGRESS' })
    await getTrainingPlanList()
    ElMessage.success('启动成功')
  }
  catch {
    ElMessage.info('启动失败')
  }
}
async function handleDelete(row: TrainingPlan) {
  try {
    await ElMessageBox.confirm(
      `确定要删除培训计划"${row.planName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 调用删除接口
    await planApi.deleteTrainingPlan(row.id)
    ElMessage.success('删除成功')
    // 重新加载数据
    await getTrainingPlanList()
  }
  catch {
    ElMessage.info('已取消删除')
  }
}

function resetFilters() {
  trainingFilters.value = {
    planStatus: '',
    trainingTarget: '',
    dateRange: [],
    planName: '',
    planType: '',
    priority: '',
    responsiblePerson: '',
  }

  // 重置页码并查询
  trainingPage.value = 1
  getTrainingPlanList()
}

// 手动查询函数
function handleSearch() {
  trainingPage.value = 1
  getTrainingPlanList()
}

// 处理分页变化
function handlePageChange() {
  getTrainingPlanList()
}

function handleSizeChange() {
  trainingPage.value = 1
  getTrainingPlanList()
}

// 组件挂载时的初始化
onMounted(() => {
  getTrainingPlanList()
})
</script>

<template>
  <div class="w-full px-1 py-1">
    <page-header>
      <template #content>
        <div class="flex flex-col justify-between md:flex-row md:items-center">
          <h1 class="text-xl text-gray-800 font-bold md:mb-0">
            培训计划
          </h1>
          <div class="flex items-center space-x-3">
            <el-button v-auth="'/training/plan/insert'" type="primary" @click="goAddEdit(null)">
              <!-- <svg-icon name="ep:plus" /> -->
              <span class="ml-4">新增培训计划</span>
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <!-- Training Plan Filter -->
    <div class="mb-4 rounded-lg bg-white p-4 shadow-sm">
      <div class="flex flex-wrap items-center gap-4">
        <el-select
          v-model="trainingFilters.planStatus"
          placeholder="计划状态"
          class="w-40"
          clearable
        >
          <el-option label="全部" value="" />
          <el-option label="草稿" value="DRAFT" />
          <el-option label="待启动" value="PENDING" />
          <el-option label="进行中" value="IN_PROGRESS" />
          <el-option label="已完成" value="COMPLETED" />
          <el-option label="已取消" value="CANCELLED" />
        </el-select>

        <el-select
          v-model="trainingFilters.planType"
          placeholder="计划类型"
          class="w-40"
          clearable
        >
          <el-option label="定期培训" value="定期培训" />
          <el-option label="专项培训" value="专项培训" />
          <el-option label="新员工培训" value="新员工培训" />
          <el-option label="法规更新培训" value="法规更新培训" />
          <el-option label="其他" value="其他" />
        </el-select>

        <el-select
          v-model="trainingFilters.priority"
          placeholder="优先级"
          class="w-40"
          clearable
        >
          <el-option label="高" value="HIGH" />
          <el-option label="中" value="MIDDLE" />
          <el-option label="低" value="LOW" />
        </el-select>

        <!-- <el-input
          v-model="trainingFilters.responsiblePerson"
          placeholder="负责人"
          class="w-40"
          clearable
        /> -->

        <el-date-picker
          v-model="trainingFilters.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          class="w-64"
          clearable
        />

        <el-input
          v-model="trainingFilters.planName"
          placeholder="搜索计划名称或关键词"
          class="w-64"
          clearable
          @keyup.enter="handleSearch"
        />

        <el-button type="primary" @click="handleSearch">
          <svg-icon name="ep:search" />
          查询
        </el-button>

        <el-button class="ml-2" @click="resetFilters">
          <svg-icon name="ep:refresh" />
          重置
        </el-button>
      </div>
    </div>

    <!-- Training Plan Table -->
    <div class="overflow-hidden rounded-lg bg-white shadow-sm">
      <el-table
        v-loading="loading"
        :data="trainingPlans"
        style="width: 100%"
        border
        element-loading-text="加载中..."
      >
        <el-table-column prop="planCode" label="计划编号" width="150" show-overflow-tooltip />

        <el-table-column prop="planName" label="计划名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="planType" label="计划类型" min-width="200" show-overflow-tooltip />

        <el-table-column prop="period" label="计划周期" width="200">
          <template #default="{ row }">
            <div class="text-sm">
              <div>{{ formatDate(row.startDate) }}</div>
              <div class="text-gray-500">
                至 {{ formatDate(row.endDate) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="trainingTargetName" label="培训对象" width="120" show-overflow-tooltip />

        <el-table-column prop="responsiblePersonName" label="负责人" width="100" show-overflow-tooltip />

        <el-table-column prop="courseCount" label="包含课程数" width="100" align="center">
          <template #default="{ row }">
            <el-tag size="small" effect="plain">
              {{ getCourseCount(row) }} 门
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="targetCompletionRate" label="目标完成率" width="150">
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <el-progress
                :percentage="row.targetCompletionRate || 0"
                :color="progressColors"
                :stroke-width="8"
                class="flex-1"
              />
              <span class="min-w-[40px] text-sm font-medium">{{ row.targetCompletionRate || 0 }}%</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="priority" label="优先级" width="80" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getPriorityType(row.priority)"
              effect="light"
              size="small"
            >
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="planStatus" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.planStatus)"
              effect="light"
              size="small"
            >
              {{ getStatusText(row.planStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="210" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary" plain
              size="small"
              @click="goDetail(row)"
            >
              查看
            </el-button>
            <el-button
              v-auth="'/training/plan/edit'"
              type="warning" plain
              size="small"
              @click="goAddEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-auth="'/training/plan/delete'"
              type="danger" plain
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
            <el-button
              v-if="row.planStatus === 'PENDING'"
              class="!mx-0"
              type="success" plain
              size="small"
              @click="goPatch(row)"
            >
              启动
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <div class="flex items-center justify-between p-4">
        <span class="text-sm text-gray-600">
          共 {{ trainingTotal }} 条记录
        </span>
        <el-pagination
          v-model:current-page="trainingPage"
          v-model:page-size="trainingPageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="trainingTotal"
          layout="total, sizes, prev, pager, next, jumper"
          class="justify-end"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>
