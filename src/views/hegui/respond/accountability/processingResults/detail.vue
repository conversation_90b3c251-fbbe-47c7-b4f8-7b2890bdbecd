<script lang="ts" setup>
import { ref } from 'vue'

const activeTab = ref('confirm')
const confirmRating = ref(0)

const evidenceData = ref([
  {
    name: '违规操作调查报告',
    type: 'PDF',
    uploadTime: '2023-05-08',
    uploader: '王强',
    description: '包含详细调查过程和结论',
  },
  {
    name: '处罚通知书',
    type: 'DOC',
    uploadTime: '2023-05-09',
    uploader: '王强',
    description: '对责任人的处罚决定',
  },
  {
    name: '制度修订稿',
    type: 'DOC',
    uploadTime: '2023-05-10',
    uploader: '张明',
    description: '修订后的市场推广管理办法',
  },
])

const processRecords = ref([
  {
    time: '2023-04-25 09:30',
    person: '审计部 刘芳',
    content: '发现市场部违规操作问题',
    result: '提交违规报告',
    attachment: '审计报告.pdf',
  },
  {
    time: '2023-04-26 14:00',
    person: '合规部 王强',
    content: '启动调查程序',
    result: '成立调查小组',
    attachment: null,
  },
  {
    time: '2023-05-05 16:30',
    person: '合规部 王强',
    content: '完成调查并形成处理方案',
    result: '提交处理方案',
    attachment: '处理方案.docx',
  },
  {
    time: '2023-05-10 10:00',
    person: '合规部 王强',
    content: '处理措施执行完毕',
    result: '提交结果确认',
    attachment: '执行报告.pdf',
  },
])

const measureData = ref([
  {
    name: '责任人处罚',
    type: '处罚',
    startDate: '2023-05-03',
    endDate: '2023-05-05',
    person: '王强',
    status: '已完成',
    progress: '已执行完毕',
  },
  {
    name: '制度修订',
    type: '整改',
    startDate: '2023-05-05',
    endDate: '2023-05-08',
    person: '张明',
    status: '已完成',
    progress: '已发布实施',
  },
  {
    name: '合规培训',
    type: '培训',
    startDate: '2023-05-10',
    endDate: '2023-05-15',
    person: '李华',
    status: '进行中',
    progress: '计划已制定',
  },
])

const operationLogs = ref([
  {
    time: '2023-05-10 10:05',
    person: '王强',
    type: '创建',
    content: '创建处理结果确认',
    ip: '*************',
  },
  {
    time: '2023-05-10 14:30',
    person: '张明',
    type: '查看',
    content: '查看处理结果',
    ip: '*************',
  },
  {
    time: '2023-05-11 09:15',
    person: '李华',
    type: '查看',
    content: '查看处理结果',
    ip: '*************',
  },
])

const statusChanges = ref([
  {
    time: '2023-05-10 10:05',
    person: '王强',
    before: '-',
    after: '待确认',
    reason: '处理完成提交确认',
  },
])

const relatedProcesses = ref([
  {
    title: '市场部广告投放违规操作处理',
    status: '已完成',
    confirmStatus: '已确认',
  },
  {
    title: '市场部预算超支问题处理',
    status: '进行中',
    confirmStatus: '待确认',
  },
  {
    title: '市场部供应商管理违规处理',
    status: '已完成',
    confirmStatus: '已确认',
  },
])

const relatedPersons = ref([
  {
    name: '王强',
    role: '处理人',
    department: '合规部',
  },
  {
    name: '李华',
    role: '责任人',
    department: '市场部',
  },
  {
    name: '张明',
    role: '部门负责人',
    department: '市场部',
  },
])

function getStatusTagType(status: string) {
  switch (status) {
    case '待确认':
      return 'warning'
    case '已确认':
      return 'success'
    case '已驳回':
      return 'danger'
    case '已豁免':
      return 'info'
    default:
      return ''
  }
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              关于市场部违规操作的处理结果确认
            </h1>
            <el-tag type="warning" class="ml-4">
              待确定
            </el-tag>
          </div>
          <div class="flex space-x-4">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" v-debounce="3000">
              <el-icon class="mr-1">
                <i class="fas fa-check" />
              </el-icon>
              确认
            </el-button>
            <el-button type="danger" class="!rounded-button whitespace-nowrap" v-debounce="3000">
              <el-icon class="mr-1">
                <i class="fas fa-times" />
              </el-icon>
              驳回
            </el-button>
            <el-button type="warning" class="!rounded-button whitespace-nowrap" v-debounce="3000">
              <el-icon class="mr-1">
                <i class="fas fa-hand-paper" />
              </el-icon>
              豁免
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" v-debounce="3000">
              <el-icon class="mr-1">
                <i class="fas fa-print" />
              </el-icon>
              打印
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" v-debounce="3000">
              <el-icon class="mr-1">
                <i class="fas fa-file-export" />
              </el-icon>
              导出
            </el-button>
            <el-dropdown>
              <el-button class="!rounded-button whitespace-nowrap" v-debounce="3000">
                <el-icon class="mr-1">
                  <i class="fas fa-ellipsis-h" />
                </el-icon>
                更多
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>撤销确认</el-dropdown-item>
                  <el-dropdown-item>撤销驳回</el-dropdown-item>
                  <el-dropdown-item>添加备注</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  处理措施基本信息
                </div>
              </template>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <p class="text-sm text-gray-500">
                    处理编号
                  </p>
                  <p class="text-gray-800">
                    CZ-2023-0425-001
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    处理标题
                  </p>
                  <p class="text-gray-800">
                    关于市场部违规操作的处理
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    处理类型
                  </p>
                  <p class="text-gray-800">
                    违规操作
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    严重程度
                  </p>
                  <el-tag type="danger">
                    严重
                  </el-tag>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    来源
                  </p>
                  <el-link type="primary">
                    内部审计报告
                  </el-link>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    责任部门
                  </p>
                  <p class="text-gray-800">
                    市场部
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    责任人
                  </p>
                  <p class="text-gray-800">
                    李华
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    处理人
                  </p>
                  <p class="text-gray-800">
                    王强
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    提出日期
                  </p>
                  <p class="text-gray-800">
                    2023-04-25
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    完成日期
                  </p>
                  <p class="text-gray-800">
                    2023-05-10
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    确认状态
                  </p>
                  <el-tag type="warning">
                    待确认
                  </el-tag>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  处理结果内容
                </div>
              </template>
              <div class="space-y-4">
                <div>
                  <h3 class="mb-2 text-gray-700 font-medium">
                    结果概述
                  </h3>
                  <p class="text-gray-800">
                    经调查核实，市场部在2023年第一季度存在未经审批擅自变更广告投放渠道的行为，违反了公司《市场推广管理办法》第5.2条规定。已对相关责任人进行处罚，并完善相关审批流程。
                  </p>
                </div>
                <div>
                  <h3 class="mb-2 text-gray-700 font-medium">
                    具体措施
                  </h3>
                  <p class="text-gray-800">
                    1. 对直接责任人李华给予警告处分，扣除当月绩效奖金30%；<br>
                    2. 对市场部负责人张明进行通报批评；<br>
                    3. 修订《市场推广管理办法》，明确广告投放变更审批流程；<br>
                    4. 组织市场部全员进行合规培训。
                  </p>
                </div>
                <div>
                  <h3 class="mb-2 text-gray-700 font-medium">
                    执行情况
                  </h3>
                  <p class="text-gray-800">
                    1. 处罚措施已于2023年5月5日执行完毕；<br>
                    2. 制度修订已完成，并于2023年5月8日发布实施；<br>
                    3. 合规培训计划已制定，将于2023年5月15日实施。
                  </p>
                </div>
                <div>
                  <h3 class="mb-2 text-gray-700 font-medium">
                    相关证据
                  </h3>
                  <el-table :data="evidenceData" style="width: 100%;">
                    <el-table-column prop="name" label="材料名称" width="180" />
                    <el-table-column prop="type" label="类型" width="100" />
                    <el-table-column prop="uploadTime" label="上传时间" width="150" />
                    <el-table-column prop="description" label="描述" />
                    <el-table-column label="操作" width="120">
                      <template #default>
                        <el-button size="small" text v-debounce="3000">
                          <el-icon><i class="fas fa-eye" /></el-icon>
                        </el-button>
                        <el-button size="small" text v-debounce="3000">
                          <el-icon><i class="fas fa-download" /></el-icon>
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <el-tabs v-model="activeTab">
                <el-tab-pane label="确认信息" name="confirm">
                  <div class="space-y-6">
                    <div>
                      <h3 class="mb-2 text-gray-700 font-medium">
                        确认状态信息
                      </h3>
                      <div class="flex items-center space-x-4">
                        <div class="flex-1">
                          <div class="relative h-2 rounded-full bg-gray-200">
                            <div class="absolute left-0 top-0 h-full rounded-full bg-blue-500" style="width: 66%;" />
                          </div>
                          <div class="mt-2 flex justify-between">
                            <span class="text-xs text-gray-500">提交确认</span>
                            <span class="text-xs text-gray-500">确认截止</span>
                            <span class="text-xs text-gray-500">确认完成</span>
                          </div>
                        </div>
                        <div>
                          <el-tag type="warning">
                            待确认
                          </el-tag>
                        </div>
                      </div>
                      <div class="mt-4">
                        <p class="text-sm text-gray-500">
                          确认截止时间: 2023-05-15 18:00
                        </p>
                        <p class="text-sm text-gray-500">
                          剩余时间: 2天3小时
                        </p>
                      </div>
                    </div>
                    <div>
                      <h3 class="mb-2 text-gray-700 font-medium">
                        确认表单
                      </h3>
                      <el-form label-position="top">
                        <el-form-item label="确认意见">
                          <el-input type="textarea" :rows="4" placeholder="请输入确认意见" />
                        </el-form-item>
                        <el-form-item label="确认评分">
                          <el-rate v-model="confirmRating" />
                        </el-form-item>
                        <el-form-item>
                          <div class="flex space-x-4">
                            <el-button type="primary" class="!rounded-button whitespace-nowrap" v-debounce="3000">
                              <el-icon class="mr-1">
                                <i class="fas fa-check" />
                              </el-icon>
                              确认
                            </el-button>
                            <el-button type="danger" class="!rounded-button whitespace-nowrap" v-debounce="3000">
                              <el-icon class="mr-1">
                                <i class="fas fa-times" />
                              </el-icon>
                              驳回
                            </el-button>
                          </div>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="处理过程" name="process">
                  <div class="space-y-6">
                    <div>
                      <h3 class="mb-4 text-gray-700 font-medium">
                        处理记录
                      </h3>
                      <div class="space-y-4">
                        <div v-for="(record, index) in processRecords" :key="index" class="flex">
                          <div class="mr-4 flex flex-col items-center">
                            <div class="mt-1 h-3 w-3 rounded-full bg-blue-500" />
                            <div v-if="index !== processRecords.length - 1" class="h-full w-px bg-gray-200" />
                          </div>
                          <div class="flex-1 pb-4">
                            <div class="flex justify-between">
                              <p class="font-medium">
                                {{ record.time }}
                              </p>
                              <p class="text-sm text-gray-500">
                                {{ record.person }}
                              </p>
                            </div>
                            <p class="mt-1 text-gray-800">
                              {{ record.content }}
                            </p>
                            <p class="mt-1 text-sm text-gray-500">
                              {{ record.result }}
                            </p>
                            <div v-if="record.attachment" class="mt-2">
                              <el-link type="primary" :underline="false">
                                <el-icon class="mr-1">
                                  <i class="fas fa-paperclip" />
                                </el-icon>
                                {{ record.attachment }}
                              </el-link>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h3 class="mb-4 text-gray-700 font-medium">
                        处理措施执行情况
                      </h3>
                      <el-table :data="measureData" style="width: 100%;">
                        <el-table-column prop="name" label="措施名称" width="180" />
                        <el-table-column prop="type" label="措施类型" width="120" />
                        <el-table-column prop="startDate" label="开始日期" width="120" />
                        <el-table-column prop="endDate" label="完成日期" width="120" />
                        <el-table-column prop="person" label="负责人" width="120" />
                        <el-table-column prop="status" label="状态" width="120">
                          <template #default="{ row }">
                            <el-tag :type="row.status === '已完成' ? 'success' : 'warning'" size="small">
                              {{ row.status }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column prop="progress" label="完成情况" />
                      </el-table>
                    </div>
                    <div>
                      <h3 class="mb-2 text-gray-700 font-medium">
                        处理任务完成情况
                      </h3>
                      <div class="flex items-center space-x-4">
                        <div class="w-1/4">
                          <el-progress :percentage="75" :stroke-width="12" />
                        </div>
                        <span class="text-sm text-gray-500">75%</span>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="相关证据" name="evidence">
                  <div class="space-y-6">
                    <div>
                      <h3 class="mb-4 text-gray-700 font-medium">
                        证据列表
                      </h3>
                      <el-table :data="evidenceData" style="width: 100%;">
                        <el-table-column prop="name" label="证据名称" width="180" />
                        <el-table-column prop="type" label="证据类型" width="120" />
                        <el-table-column prop="uploadTime" label="上传时间" width="150" />
                        <el-table-column prop="uploader" label="上传人" width="120" />
                        <el-table-column prop="description" label="描述" />
                        <el-table-column label="操作" width="120">
                      <template #default>
                        <el-button size="small" text v-debounce="3000">
                          <el-icon><i class="fas fa-eye" /></el-icon>
                        </el-button>
                        <el-button size="small" text v-debounce="3000">
                          <el-icon><i class="fas fa-download" /></el-icon>
                        </el-button>
                      </template>
                    </el-table-column>
                      </el-table>
                    </div>
                    <div>
                      <h3 class="mb-2 text-gray-700 font-medium">
                        证据预览
                      </h3>
                      <div class="h-64 flex items-center justify-center border border-gray-200 rounded-lg p-4">
                        <el-icon class="text-4xl text-gray-400">
                          <i class="fas fa-file-pdf" />
                        </el-icon>
                        <span class="ml-2 text-gray-500">选择左侧证据进行预览</span>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="操作历史" name="history">
                  <div class="space-y-6">
                    <div>
                      <h3 class="mb-4 text-gray-700 font-medium">
                        操作日志
                      </h3>
                      <el-table :data="operationLogs" style="width: 100%;">
                        <el-table-column prop="time" label="时间" width="150" />
                        <el-table-column prop="person" label="操作人" width="120" />
                        <el-table-column prop="type" label="操作类型" width="120" />
                        <el-table-column prop="content" label="操作内容" />
                        <el-table-column prop="ip" label="IP地址" width="120" />
                      </el-table>
                    </div>
                    <div>
                      <h3 class="mb-4 text-gray-700 font-medium">
                        状态变更记录
                      </h3>
                      <el-table :data="statusChanges" style="width: 100%;">
                        <el-table-column prop="time" label="变更时间" width="150" />
                        <el-table-column prop="person" label="变更人" width="120" />
                        <el-table-column prop="before" label="变更前状态" width="120">
                          <template #default="{ row }">
                            <el-tag :type="getStatusTagType(row.before) as any" size="small">
                              {{ row.before }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column prop="after" label="变更后状态" width="120">
                          <template #default="{ row }">
                            <el-tag :type="getStatusTagType(row.after) as any" size="small">
                              {{ row.after }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column prop="reason" label="变更原因" />
                      </el-table>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  确认流程
                </div>
              </template>
              <div class="space-y-4">
                <div class="flex">
                  <div class="mr-3 flex flex-col items-center">
                    <div class="mt-1 h-3 w-3 rounded-full bg-gray-300" />
                    <div class="h-8 w-px bg-gray-200" />
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">
                      2023-05-10
                    </p>
                    <p class="text-sm">
                      处理完成
                    </p>
                  </div>
                </div>
                <div class="flex">
                  <div class="mr-3 flex flex-col items-center">
                    <div class="mt-1 h-3 w-3 rounded-full bg-gray-300" />
                    <div class="h-8 w-px bg-gray-200" />
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">
                      2023-05-11
                    </p>
                    <p class="text-sm">
                      提交确认
                    </p>
                  </div>
                </div>
                <div class="flex">
                  <div class="mr-3 flex flex-col items-center">
                    <div class="mt-1 h-4 w-4 rounded-full bg-blue-500" />
                    <div class="h-8 w-px bg-gray-200" />
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">
                      2023-05-15
                    </p>
                    <p class="text-sm font-medium">
                      确认截止
                    </p>
                  </div>
                </div>
                <div class="flex">
                  <div class="mr-3 flex flex-col items-center">
                    <div class="mt-1 h-3 w-3 rounded-full bg-gray-100" />
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">
                      -
                    </p>
                    <p class="text-sm">
                      确认完成
                    </p>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关处理
                </div>
              </template>
              <div class="space-y-3">
                <div
                  v-for="(item, index) in relatedProcesses" :key="index"
                  class="cursor-pointer rounded p-2 hover:bg-gray-50"
                >
                  <p class="truncate text-sm">
                    {{ item.title }}
                  </p>
                  <div class="mt-1 flex justify-between">
                    <el-tag :type="item.status === '已完成' ? 'success' : 'warning'" size="small">
                      {{ item.status }}
                    </el-tag>
                    <el-tag :type="item.confirmStatus === '已确认' ? 'success' : 'warning'" size="small">
                      {{ item.confirmStatus }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关人员
                </div>
              </template>
              <div class="space-y-4">
                <div v-for="(person, index) in relatedPersons" :key="index" class="flex items-center">
                  <div class="mr-3 h-10 w-10 flex items-center justify-center rounded-full bg-blue-100">
                    <span class="text-blue-600">{{ person.name.charAt(0) }}</span>
                  </div>
                  <div>
                    <p class="text-sm font-medium">
                      {{ person.name }}
                    </p>
                    <p class="text-xs text-gray-500">
                      {{ person.role }} · {{ person.department }}
                    </p>
                    <div class="mt-1 flex space-x-2">
                      <el-tooltip content="电话: 13800138000" placement="top">
                        <el-icon class="cursor-pointer text-gray-400 hover:text-blue-500">
                          <i
                            class="fas fa-phone"
                          />
                        </el-icon>
                      </el-tooltip>
                      <el-tooltip content="邮箱: <EMAIL>" placement="top">
                        <el-icon class="cursor-pointer text-gray-400 hover:text-blue-500">
                          <i
                            class="fas fa-envelope"
                          />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <el-button type="primary" class="!rounded-button w-full whitespace-nowrap" v-debounce="3000">
                    <el-icon class="mr-1">
                      <i class="fas fa-check" />
                    </el-icon>
                    确认结果
                  </el-button>
                </div>
                <div>
                  <el-button type="danger" class="!rounded-button w-full whitespace-nowrap" v-debounce="3000">
                    <el-icon class="mr-1">
                      <i class="fas fa-times" />
                    </el-icon>
                    驳回结果
                  </el-button>
                </div>
                <div>
                  <el-button class="!rounded-button w-full whitespace-nowrap" v-debounce="3000">
                    <el-icon class="mr-1">
                      <i class="fas fa-search" />
                    </el-icon>
                    查看源处理
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }

  .bg-gray-50 {
    background-color: #f9fafb;
  }

  .bg-[#1A1F37] {
    background-color: #1a1f37;
  }

  .shadow-sm {
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%);
  }

  .rounded-lg {
    border-radius: 0.5rem;
  }

  .sticky {
    position: sticky;
  }

  .h-fit {
    height: fit-content;
  }

  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
