import api from '@/api/index'

export default {
  // 岗位管理
  positionApi(paging: any, params: any, key: any) {
    switch (key) {
      case 'detail':
        return api.get(`/whiskerguardorgservice/api/positions/${params.id}`, {
          params,
        })
      case 'create':
        return api.post(`/whiskerguardorgservice/api/positions`, params)
      case 'update':
        return api.patch(`/whiskerguardorgservice/api/positions/${params.id}`, params)
      case 'delete':
        return api.delete(`/whiskerguardorgservice/api/positions/${params.id}`)
      default:
        return api.get(`/whiskerguardorgservice/api/positions?page=${paging.page}&size=${paging.size}`, {
          params,
        })
    }
  },
  // 组织单元管理和部门
  organizationalUnitApi(paging: any, params: any, key: any) {
    switch (key) {
      case 'detail':
        return api.get(`/whiskerguardorgservice/api/org/units/${params.id}`, {
          params,
        })
      case 'create':
        return api.post(`/whiskerguardorgservice/api/org/units`, params)
      case 'update':
        return api.patch(`/whiskerguardorgservice/api/org/units/${params.id}`, params)
      case 'delete':
        return api.delete(`/whiskerguardorgservice/api/org/units/${params.id}`)
      default:
        return api.get(`/whiskerguardorgservice/api/org/units?page=${paging.page}&size=${paging.size}`, {})
    }
  },
  // 组织架构树形结构/岗位树形结构
  organizationalUnitTreeApi() {
    return api.get(`/whiskerguardorgservice/api/org/units/tree`, {
    })
  },

  // 角色管理相关接口
  roleApi(paging: any, params: any, key: any) {
    switch (key) {
      case 'detail':
        return api.get(`/whiskerguardorgservice/api/roles/${params.id}`, {
          params,
        })
      case 'create':
        return api.post(`/whiskerguardorgservice/api/roles`, params)
      case 'update':
        return api.patch(`/whiskerguardorgservice/api/roles/${params.id}`, params)
      case 'delete':
        return api.delete(`/whiskerguardorgservice/api/roles/${params.id}`)
      default:
        return api.post(`/whiskerguardorgservice/api/roles/page`, {
          page: paging.page,
          size: paging.size,
          ...params,
        })
    }
  },
  createRole(params: any) {
    return api.post(`/whiskerguardorgservice/api/role/permissions`, params)
  },

  // 角色导入相关接口
  downloadRoleTemplate() {
    return api.get(`/whiskerguardorgservice/api/roles/template`, {
      responseType: 'blob',
    })
  },
  importRoles(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return api.post(`/whiskerguardorgservice/api/roles/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 获取用户统计信息
  getUserStatistics() {
    return api.get(`/whiskerguardorgservice/api/employees/statistics`)
  },

  // 部门导入相关接口
  downloadDepartmentTemplate() {
    return api.get(`/whiskerguardorgservice/api/org/units/template`, {
      responseType: 'blob',
    })
  },
  importDepartments(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return api.post(`/whiskerguardorgservice/api/org/units/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
}
