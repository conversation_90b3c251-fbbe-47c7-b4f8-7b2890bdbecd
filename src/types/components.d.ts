/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    Auth: typeof import('./../components/Auth/index.vue')['default']
    AuthAll: typeof import('./../components/AuthAll/index.vue')['default']
    Chip: typeof import('./../components/Chip/index.vue')['default']
    ColorfulCard: typeof import('./../components/ColorfulCard/index.vue')['default']
    copy: typeof import('./../components/LoadingAnimation/index copy.vue')['default']
    CourseSelector: typeof import('./../components/CourseSelector.vue')['default']
    Demo: typeof import('./../components/LoadingAnimation/demo.vue')['default']
    DemoChat: typeof import('./../components/DemoChat/index.vue')['default']
    DepartmentTree: typeof import('./../components/DepartmentTree/index.vue')['default']
    DepartmentTreeSelect: typeof import('./../components/DepartmentTreeSelect/index.vue')['default']
    DepartPerson: typeof import('./../components/departPerson/index.vue')['default']
    DocumentUpload: typeof import('./../components/DocumentUpload/index.vue')['default']
    Drawer: typeof import('./../components/drawer/index.vue')['default']
    FileUpload: typeof import('./../components/FileUpload/index.vue')['default']
    FixedActionBar: typeof import('./../components/FixedActionBar/index.vue')['default']
    HandlingMeasuresSelector: typeof import('./../components/HandlingMeasuresSelector/index.vue')['default']
    HBadge: typeof import('./../layouts/ui-kit/HBadge.vue')['default']
    HButton: typeof import('./../layouts/ui-kit/HButton.vue')['default']
    HCheckList: typeof import('./../layouts/ui-kit/HCheckList.vue')['default']
    HDialog: typeof import('./../layouts/ui-kit/HDialog.vue')['default']
    HDropdown: typeof import('./../layouts/ui-kit/HDropdown.vue')['default']
    HDropdownMenu: typeof import('./../layouts/ui-kit/HDropdownMenu.vue')['default']
    HInput: typeof import('./../layouts/ui-kit/HInput.vue')['default']
    HKbd: typeof import('./../layouts/ui-kit/HKbd.vue')['default']
    HSelect: typeof import('./../layouts/ui-kit/HSelect.vue')['default']
    HSlideover: typeof import('./../layouts/ui-kit/HSlideover.vue')['default']
    HTabList: typeof import('./../layouts/ui-kit/HTabList.vue')['default']
    HToggle: typeof import('./../layouts/ui-kit/HToggle.vue')['default']
    HTooltip: typeof import('./../layouts/ui-kit/HTooltip.vue')['default']
    I18nSelector: typeof import('./../components/I18nSelector/index.vue')['default']
    IconPicker: typeof import('./../components/IconPicker/index.vue')['default']
    IconSelector: typeof import('./../components/IconSelector/index.vue')['default']
    ImageMBB: typeof import('./../components/uploadMbb/imageMBB.vue')['default']
    ImagePreview: typeof import('./../components/ImagePreview/index.vue')['default']
    ImagesUpload: typeof import('./../components/ImagesUpload/index.vue')['default']
    ImageUpload: typeof import('./../components/ImageUpload/index.vue')['default']
    Import: typeof import('./../components/import/index.vue')['default']
    ImportV1: typeof import('./../components/import/ImportV1.vue')['default']
    ImportV2: typeof import('./../components/import/ImportV2.vue')['default']
    'Index copy': typeof import('./../components/LoadingAnimation/index copy.vue')['default']
    Index_new: typeof import('./../components/LoadingAnimation/index_new.vue')['default']
    Initiate: typeof import('./../components/initiate/index.vue')['default']
    LawsPop: typeof import('./../components/lawsPop/index.vue')['default']
    LayoutContainer: typeof import('./../components/LayoutContainer/index.vue')['default']
    Loading: typeof import('./../components/loading/index.vue')['default']
    LoadingAnimation: typeof import('./../components/LoadingAnimation/index.vue')['default']
    MarkdownRenderer: typeof import('./../components/MarkdownRenderer/index.vue')['default']
    NewLoading: typeof import('./../components/LoadingAnimation/newLoading.vue')['default']
    NotAllowed: typeof import('./../components/NotAllowed/index.vue')['default']
    OtherResultDialog: typeof import('./../components/OtherResultDialog/index.vue')['default']
    PageComponent: typeof import('./../components/pageComponent/index.vue')['default']
    PageHeader: typeof import('./../components/PageHeader/index.vue')['default']
    PageMain: typeof import('./../components/PageMain/index.vue')['default']
    Pagination: typeof import('./../components/Pagination/index.vue')['default']
    Particles: typeof import('./../components/particles/index.vue')['default']
    PcasCascader: typeof import('./../components/PcasCascader/index.vue')['default']
    ReviewResultDialog: typeof import('./../components/ReviewResultDialog/index.vue')['default']
    RichText: typeof import('./../components/richText/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchBar: typeof import('./../components/SearchBar/index.vue')['default']
    Sparkline: typeof import('./../components/Sparkline/index.vue')['default']
    SpinkitLoading: typeof import('./../components/SpinkitLoading/index.vue')['default']
    StorageBox: typeof import('./../components/StorageBox/index.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    SystemInfo: typeof import('./../components/SystemInfo/index.vue')['default']
    Trend: typeof import('./../components/Trend/index.vue')['default']
    UploadMbb: typeof import('./../components/uploadMbb/index.vue')['default']
    Viewer: typeof import('./../components/richText/viewer.vue')['default']
    ViolationInvestigationSelector: typeof import('./../components/ViolationInvestigationSelector/index.vue')['default']
  }
}
