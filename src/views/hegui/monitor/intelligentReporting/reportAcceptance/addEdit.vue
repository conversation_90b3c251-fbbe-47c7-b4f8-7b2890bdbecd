<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Close, Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import UploadMbb from '@/components/uploadMbb/index.vue'
import systemApi from '@/api/modules/system/dict'
import intelligentReportingApi from '@/api/report/intelligentReporting'

const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 是否编辑模式
const isEdit = computed(() => !!route.params.id)

// 举报类型选项
const reportTypes = ref<any[]>([])

// 表单数据
const formData = reactive({
  violationType: '',
  violationCode: '',
  title: '',
  detail: '',
  attachmentList: [] as any[],
  isAnonymous: false,
  contactWay: '',
})

// 表单验证规则
const rules: FormRules = {
  violationType: [
    { required: true, message: '请选择举报类型', trigger: 'change' },
  ],
  title: [
    { required: true, message: '请输入举报标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度在2到50个字符之间', trigger: 'blur' },
  ],
  detail: [
    { required: true, message: '请输入举报详情', trigger: 'blur' },
    { min: 10, max: 1000, message: '详情长度在10到1000个字符之间', trigger: 'blur' },
  ],
}

// 获取字典数据
function getDictData() {
  return new Promise<void>((resolve) => {
    Promise.all([
      systemApi.getCode('VIOLATION'),
      systemApi.dictAll('45'),
    ]).then(([codeRes, dictRes]) => {
      formData.violationCode = codeRes

      if (dictRes && dictRes.length > 0) {
        reportTypes.value = dictRes.map((item: any) => ({
          name: item.name,
          value: item.value,
        }))
      }
      resolve()
    }).catch((error) => {
      ElMessage.error('获取字典数据失败')
      resolve()
    })
  })
}

// 获取详情数据
function getDetailData() {
  if (!isEdit.value) {
    return
  }

  loading.value = true
  intelligentReportingApi.handling({ id: route.params.id }, 'info')
    .then((res) => {
      if (res) {
        Object.assign(formData, {
          violationType: res.violationType || '',
          violationCode: res.violationCode || '',
          title: res.title || '',
          detail: res.detail || '',
          attachmentList: res.attachmentList || [],
          isAnonymous: res.isAnonymous || false,
          contactWay: res.contactWay || '',
        })
      }
    })
    .catch(() => {
      ElMessage.error('获取详情失败')
    })
    .finally(() => {
      loading.value = false
    })
}

// 附件上传成功回调
function handleUploadSuccess(fileList: any[]) {
  formData.attachmentList = fileList.map((item: any) => ({
    fileName: item?.fileName,
    fileType: item.fileType,
    filePath: item.key,
    fileSize: `${item.size}kb`,
  }))
}

// 取消操作
function handleCancel() {
  router.back()
}

// 提交表单
function handleSubmit() {
  if (!formRef.value) {
    return
  }

  formRef.value.validate()
    .then(() => {
      loading.value = true

      const submitData = {
        ...formData,
        attachmentList: formData.attachmentList.map((item: any) => ({
          fileName: item?.fileName,
          fileType: item.fileType,
          filePath: item.key || item.filePath,
          fileSize: item.size ? `${item.size}kb` : item.fileSize,
        })),
      }

      const apiCall = isEdit.value
        ? intelligentReportingApi.updateReport({ ...submitData, id: route.params.id })
        : intelligentReportingApi.createReport(submitData)

      return apiCall
    })
    .then(() => {
      ElMessage.success(isEdit.value ? '更新成功' : '提交成功')
      router.back()
    })
    .catch((error) => {
      if (error !== false) {
        ElMessage.error(isEdit.value ? '更新失败' : '提交失败')
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 页面初始化
onMounted(() => {
  getDictData()
  getDetailData()
})
</script>

<template>
  <div class="absolute-container">
    <PageHeader>
      <template #content>
        <!-- 页面标题和操作按钮 -->
        <div class="flex items-center justify-between">
          <h1 class="text-xl text-gray-800 font-bold">
            {{ isEdit ? '编辑举报' : '新增举报' }}
          </h1>
          <div class="flex items-center space-x-3">
            <el-button
              type="primary"
              class="rounded-button whitespace-nowrap"
              :loading="loading"
              @click="handleSubmit"
            >
              <el-icon class="mr-2">
                <Plus />
              </el-icon>
              {{ isEdit ? '更新' : '提交' }}
            </el-button>
            <el-button
              plain
              class="rounded-button whitespace-nowrap"
              @click="handleCancel"
            >
              <el-icon class="mr-2">
                <Close />
              </el-icon>
              取消
            </el-button>
          </div>
        </div>
      </template>
    </PageHeader>

    <PageMain style="background-color: transparent;">
      <div class="m-auto flex space-x-6">
        <div class="m-20 space-y-6">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-width="120px"
          >
            <el-card shadow="hover" class="p-6">
              <h2 class="mb-6 text-lg text-gray-800 font-bold">
                举报信息
              </h2>

              <div class="grid grid-cols-2 gap-6">
                <!-- 举报类型 -->
                <div>
                  <el-form-item label="举报类型" prop="violationType">
                    <el-select
                      v-model="formData.violationType"
                      placeholder="请选择举报类型"
                      class="w-full"
                    >
                      <el-option
                        v-for="item in reportTypes"
                        :key="item.value"
                        :label="item.name"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>

                <!-- 举报编号 -->
                <div>
                  <el-form-item label="举报编号">
                    <el-input
                      v-model="formData.violationCode"
                      placeholder="自动生成"
                      readonly
                      class="w-full"
                    />
                  </el-form-item>
                </div>
              </div>

              <!-- 举报标题 -->
              <el-form-item label="举报标题" prop="title">
                <el-input
                  v-model="formData.title"
                  placeholder="请输入举报标题"
                  maxlength="50"
                  show-word-limit
                />
              </el-form-item>

              <!-- 举报详情 -->
              <el-form-item label="举报详情" prop="detail">
                <el-input
                  v-model="formData.detail"
                  type="textarea"
                  placeholder="请详细描述举报内容"
                  :rows="6"
                  maxlength="1000"
                  show-word-limit
                />
              </el-form-item>

              <!-- 附件上传 -->
              <el-form-item label="附件上传">
                <UploadMbb
                  v-model="formData.attachmentList"
                  :auto-upload="true"
                  :max="10"
                  :size="20"
                  service-name="whiskerguardgeneralservice"
                  :use-file-path="true"
                  category-name="contract"
                  tip-text="支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 20MB"
                />
              </el-form-item>

              <!-- 匿名模式 -->
              <el-form-item label="匿名模式">
                <el-switch
                  v-model="formData.isAnonymous"
                  active-text="匿名举报"
                  inactive-text="实名举报"
                />
              </el-form-item>

              <!-- 联系方式 -->
              <el-form-item
                v-if="!formData.isAnonymous"
                label="联系方式"
              >
                <el-input
                  v-model="formData.contactWay"
                  placeholder="请输入联系方式（手机号或邮箱）"
                  maxlength="50"
                />
              </el-form-item>
            </el-card>
          </el-form>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";
.rounded-button {
  border-radius: 6px;
}

.absolute-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}

:deep(.el-switch__label) {
  font-size: 14px;
}
</style>
