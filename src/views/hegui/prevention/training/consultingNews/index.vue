<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  Document,
  FolderOpened,
  InfoFilled,
  Plus,
  Search,
  Warning,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import systemApi from '@/api/curriculum/news'

function handleSelectionChange(_val: any) {
  // 处理选择变化
}

// 状态映射：英文状态码到中文
function getStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    DRAFT: '草稿',
    PENDING_REVIEW: '审核中',
    PUBLISHED: '已发布',
    ARCHIVED: '存档',
    DEADLINE: '下线',
  }
  return statusMap[status] || status
}

function getStatusTag(status: string): 'info' | 'warning' | 'success' | 'danger' | 'primary' {
  const map: Record<string, 'info' | 'warning' | 'success' | 'danger' | 'primary'> = {
    草稿: 'info',
    待审核: 'warning',
    审核中: 'warning',
    已发布: 'success',
    已下线: 'danger',
    下线: 'danger',
    存档: 'info',
    DRAFT: 'info',
    PENDING_REVIEW: 'warning',
    PUBLISHED: 'success',
    ARCHIVED: 'info',
    DEADLINE: 'danger',
  }
  return map[status] || 'primary'
}
const loading = ref(false)
const dataList: any = ref([])
const paging: any = ref({
  page: 1,
  limit: 10,
  total: 0,
})

// 筛选表单数据
const searchForm = ref({
  type: '',
  status: '',
  dateRange: [] as any,
  keyword: '',
})

// 新增标签弹窗相关
const tagDialogVisible = ref(false)
const isEditingTag = ref(false)
const tagForm = ref({
  id: null,
  name: '',
  description: '',
})
const tagFormRef = ref()
const tagFormRules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
  ],
}

// 标签管理弹窗相关
const tagManageDialogVisible = ref(false)
const tagList = ref([])
const tagLoading = ref(false)

// 新增分类弹窗相关
const categoryDialogVisible = ref(false)
const isEditingCategory = ref(false)
const categoryForm = ref({
  id: null,
  status: 'PUBLISHED',
  sortOrder: 1,
  name: '',
  description: '',
  coverImageUrl: '',
})
const categoryFormRef = ref()
const categoryFormRules = {
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
  ],
}

// 分类管理弹窗相关
const categoryManageDialogVisible = ref(false)
const categoryList = ref([])
const categoryLoading = ref(false)

// 新增/编辑
const router = useRouter()

function goAddEdit(item: any) {
  if (item?.id) {
    // 编辑咨讯
    router.push({
      name: '/training/consultingNews/addEdit',
      query: { id: item.id },
    })
  }
  else {
    // 新增咨讯
    router.push({
      name: '/training/consultingNews/addEdit',
    })
  }
}
// 查看咨讯详情
function goDetail(row: any) {
  router.push({
    path: '/training/consultingNews/detail',
    query: { id: row.id },
  })
}

onMounted(() => {
  getList()
})

function getList() {
  const params: any = {
    page: Number(paging.value.page) - 1,
    size: Number(paging.value.limit),
  }

  // 添加筛选参数，空字符串转为null
  if (searchForm.value.type && searchForm.value.type !== 'all') {
    params.type = searchForm.value.type
  }
  if (searchForm.value.status && searchForm.value.status !== 'all') {
    params.status = searchForm.value.status
  }
  if (searchForm.value.dateRange && Array.isArray(searchForm.value.dateRange) && searchForm.value.dateRange.length === 2) {
    params.publishStartDate = searchForm.value.dateRange[0]
    params.publishEndDate = searchForm.value.dateRange[1]
  }
  if (searchForm.value.keyword && searchForm.value.keyword.trim() !== '') {
    params.keyword = searchForm.value.keyword.trim()
  }
  loading.value = true
  systemApi.news(params, null).then((res: any) => {
    dataList.value = res.content ? res.content : []
    paging.value.total = res.totalElements ? res.totalElements : 0
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

// 查询方法
function handleSearch() {
  paging.value.page = 1
  getList()
}

// 重置方法
function handleReset() {
  searchForm.value = {
    type: '',
    status: '',
    dateRange: [] as any,
    keyword: '',
  }
  paging.value.page = 1
  getList()
}

// 打开新增标签弹窗
function openTagDialog() {
  isEditingTag.value = false
  tagDialogVisible.value = true
  tagForm.value = {
    id: null,
    name: '',
    description: '',
  }
}

// 关闭标签弹窗
function closeTagDialog() {
  tagDialogVisible.value = false
  tagFormRef.value?.resetFields()
}

// 提交标签表单
function submitTagForm() {
  tagFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      const apiCall = isEditingTag.value
        ? systemApi.updateTagCategory(tagForm.value)
        : systemApi.createTagCategory(tagForm.value)

      const successMessage = isEditingTag.value ? '标签更新成功' : '标签创建成功'
      const errorMessage = isEditingTag.value ? '标签更新失败' : '标签创建失败'

      apiCall.then((_res: any) => {
        ElMessage.success(successMessage)
        closeTagDialog()
        // 如果标签管理弹窗是打开的，刷新列表
        if (tagManageDialogVisible.value) {
          getTagList()
        }
      }).catch((error: any) => {
        ElMessage.error(errorMessage)
        console.error(error)
      })
    }
  })
}

// 打开新增分类弹窗
function openCategoryDialog() {
  isEditingCategory.value = false
  categoryDialogVisible.value = true
  categoryForm.value = {
    id: null,
    status: 'DRAFT',
    sortOrder: 1,
    name: '',
    description: '',
    coverImageUrl: '',
  }
}

// 关闭分类弹窗
function closeCategoryDialog() {
  categoryDialogVisible.value = false
  categoryFormRef.value?.resetFields()
}

// 提交分类表单
function submitCategoryForm() {
  categoryFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      const apiCall = isEditingCategory.value
        ? systemApi.updateNewsCategory(categoryForm.value)
        : systemApi.createNewsCategory(categoryForm.value)

      const successMessage = isEditingCategory.value ? '分类更新成功' : '分类创建成功'
      const errorMessage = isEditingCategory.value ? '分类更新失败' : '分类创建失败'

      apiCall.then((_res: any) => {
        ElMessage.success(successMessage)
        closeCategoryDialog()
        // 如果分类管理弹窗是打开的，刷新列表
        if (categoryManageDialogVisible.value) {
          getCategoryList()
        }
      }).catch((error: any) => {
        ElMessage.error(errorMessage)
        console.error(error)
      })
    }
  })
}

// 打开分类管理弹窗
function openCategoryManageDialog() {
  categoryManageDialogVisible.value = true
  getCategoryList()
}

// 关闭分类管理弹窗
function closeCategoryManageDialog() {
  categoryManageDialogVisible.value = false
}

// 获取分类列表
function getCategoryList() {
  categoryLoading.value = true
  systemApi.getNewsCategories({}).then((res: any) => {
    categoryList.value = res.content || []
  }).catch((error: any) => {
    ElMessage.error('获取分类列表失败')
    console.error(error)
  }).finally(() => {
    categoryLoading.value = false
  })
}

// 编辑分类
function editCategory(item: any) {
  isEditingCategory.value = true
  categoryForm.value = {
    id: item.id,
    name: item.name,
    description: item.description,
    sortOrder: item.sortOrder || 1,
    status: item.status || 'PUBLISHED',
    coverImageUrl: item.coverImageUrl || '',
  }
  categoryDialogVisible.value = true
}

// 删除分类
function deleteCategory(item: any) {
  ElMessageBox.confirm(
    `确定要删除分类"${item.name}"吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    systemApi.deleteNewsCategory({ id: item.id }).then((_res: any) => {
      ElMessage.success('分类删除成功')
      getCategoryList()
    }).catch((error: any) => {
      ElMessage.error('分类删除失败')
      console.error(error)
    })
  }).catch(() => {
    // 用户取消删除
  })
}

// 打开标签管理弹窗
function openTagManageDialog() {
  tagManageDialogVisible.value = true
  getTagList()
}

// 关闭标签管理弹窗
function closeTagManageDialog() {
  tagManageDialogVisible.value = false
}

// 获取标签列表
function getTagList() {
  tagLoading.value = true
  systemApi.getTagCategories({ page: 0, size: 20 }).then((res: any) => {
    tagList.value = res.content || []
  }).catch((error: any) => {
    ElMessage.error('获取标签列表失败')
    console.error(error)
  }).finally(() => {
    tagLoading.value = false
  })
}

// 编辑标签
function editTag(item: any) {
  isEditingTag.value = true
  tagForm.value = {
    id: item.id,
    name: item.name,
    description: item.description || '',
  }
  tagDialogVisible.value = true
}

// 删除标签
function deleteTag(item: any) {
  ElMessageBox.confirm(
    `确定要删除标签"${item.name}"吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    systemApi.deleteTagCategory({ id: item.id }).then((_res: any) => {
      ElMessage.success('标签删除成功')
      getTagList()
    }).catch((error: any) => {
      ElMessage.error('标签删除失败')
      console.error(error)
    })
  }).catch(() => {
    // 用户取消删除
  })
}

// 发布/下线方法
function togglePublishStatus(row: any) {
  const currentStatus = row.status
  let newStatus = ''
  let actionText = ''

  if (currentStatus === 'DRAFT') {
    newStatus = 'PUBLISHED'
    actionText = '发布'
  }
  else if (currentStatus === 'PUBLISHED') {
    // newStatus = 'DEADLINE'
    newStatus = 'DRAFT'
    actionText = '下线'
  }
  else {
    ElMessage.warning('当前状态不支持此操作')
    return
  }

  ElMessageBox.confirm(
    `确定要${actionText}这篇资讯吗？`,
    `确认${actionText}`,
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    const params = {
      id: row.id,
      status: newStatus,
    }

    systemApi.news(params, 'update').then((_res: any) => {
      ElMessage.success(`${actionText}成功`)
      getList() // 刷新列表
    }).catch((error: any) => {
      ElMessage.error(`${actionText}失败`)
      console.error(error)
    })
  }).catch(() => {
    // 用户取消操作
  })
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="mr-4 text-xl c-[#000000] font-bold">
              资讯动态管理
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="['consultingNews/index/add']" type="primary" class="!rounded-button whitespace-nowrap" @click="goAddEdit(null)">
              <!-- <el-icon class="mr-1">
                <Plus />
              </el-icon> -->
              发布资讯
            </el-button>
            <el-button v-auth="['consultingNews/index/addTag']" type="primary" plain class="!rounded-button whitespace-nowrap" @click="openTagDialog">
              <!-- <el-icon class="mr-1">
                <Plus />
              </el-icon> -->
              新增标签
            </el-button>
            <el-button v-auth="['consultingNews/index/addCategory']" type="primary" plain class="!rounded-button whitespace-nowrap" @click="openCategoryDialog">
              <!-- <el-icon class="mr-1">
                <Plus />
              </el-icon> -->
              新增分类
            </el-button>
            <el-button v-auth="['consultingNews/index/tagManage']" type="success" class="!rounded-button whitespace-nowrap" @click="openTagManageDialog">
              <!-- <el-icon class="mr-1">
                <FolderOpened />
              </el-icon> -->
              标签管理
            </el-button>
            <el-button v-auth="['consultingNews/index/categoryManage']" type="success" plain class="!rounded-button whitespace-nowrap" @click="openCategoryManageDialog">
              <!-- <el-icon class="mr-1">
                <FolderOpened />
              </el-icon> -->
              分类管理
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row v-if="false" :gutter="20" class="!mx-0">
          <el-col :span="6">
            <el-card shadow="hover">
              <div class="text-3xl text-blue-500 font-bold">
                128
              </div>
              <div class="mt-1 text-sm text-gray-500">
                总资讯数
              </div>
              <div class="mt-2 text-xs text-green-600">
                +18 本月新增
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <div class="text-3xl text-green-600 font-bold">
                115
              </div>
              <div class="mt-1 text-sm text-gray-500">
                已发布
              </div>
              <div class="mt-2 text-xs text-green-600">
                89.8% 发布率
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <div class="text-3xl text-yellow-600 font-bold">
                8
              </div>
              <div class="mt-1 text-sm text-gray-500">
                待审核
              </div>
              <div class="mt-2 text-xs text-yellow-600">
                8 待处理
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <div class="text-3xl text-gray-600 font-bold">
                186
              </div>
              <div class="mt-1 text-sm text-gray-500">
                平均阅读量
              </div>
              <div class="mt-2 text-xs text-green-600">
                +23 较上月
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-10">
          <el-col :span="24">
            <el-card shadow="none">
              <div class="flex items-center space-x-4">
                <!-- <el-select v-model="searchForm.type" placeholder="资讯类型" class="w-40">
                  <el-option label="全部" value="all" />
                  <el-option label="培训通知" value="notice" />
                  <el-option label="学习资料" value="material" />
                  <el-option label="政策解读" value="policy" />
                  <el-option label="经验分享" value="experience" />
                  <el-option label="工作动态" value="news" />
                </el-select> -->
                <el-select v-model="searchForm.status" clearable placeholder="发布状态" class="w-32">
                  <el-option label="草稿" value="DRAFT" />
                  <!-- <el-option label="待审核" value="PENDING" /> -->
                  <el-option label="已发布" value="PUBLISHED" />
                  <!-- <el-option label="已下线" value="OFFLINE" /> -->
                </el-select>
                <el-date-picker
                  v-model="searchForm.dateRange"
                  value-format="YYYY-MM-DD"
                  type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  class="w-64"
                />
                <el-input v-model="searchForm.keyword" placeholder="请输入关键词" class="flex-1" />
                <el-button type="primary" @click="handleSearch">
                  查询
                </el-button>
                <el-button @click="handleReset">
                  重置
                </el-button>
              </div>
              <el-table v-loading="loading" :data="dataList" style="width: 100%" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="50" />
                <el-table-column prop="title" label="资讯标题" width="250">
                  <template #default="{ row }">
                    {{ row.title }}
                  </template>
                </el-table-column>

                <!--                <el-table-column prop="type" label="资讯类型" width="120">
                  <template #default="{row}">
                    <el-tag :type="getTypeTag(row.type)" size="small">{{ row.type }}</el-tag>
                  </template>
                </el-table-column> -->
                <el-table-column prop="status" label="发布状态" width="120">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTag(row.status)" size="small">
                      {{ getStatusLabel(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createdBy" label="创建人" width="150" />
                <el-table-column prop="createdAt" label="创建时间" width="180" />
                <el-table-column prop="publishDate" label="发布时间" />

                <el-table-column width="250" label="操作">
                  <template #default="{ row }">
                    <el-button v-auth="['consultingNews/index/view']" size="small" type="primary" plain :underline="false" class="mr-3" @click="goDetail(row)">
                      查看
                    </el-button>
                    <el-button v-auth="['consultingNews/index/edit']" size="small" type="warning" plain :underline="false" class="mr-3" @click="goAddEdit(row)">
                      编辑
                    </el-button>
                    <el-button
                      size="small"
                      :type="row.status === 'DRAFT' ? 'success' : 'danger'" plain
                      :underline="false"
                      @click="togglePublishStatus(row)"
                    >
                      {{ row.status === 'DRAFT' ? '发布' : row.status === 'PUBLISHED' ? '下线' : '发布' }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <page-compon
                :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
                @pag-change="Object.assign(paging, $event), getList()"
              />
              <!-- 批量操作 -->
              <!-- <div class="flex items-center justify-between p-3 border-t bg-gray-50">
                <div class="flex items-center">
                  <el-checkbox v-model="selectAll" class="mr-3">全选</el-checkbox>
                  <span class="text-sm text-gray-500">已选择 {{ selectedCount }} 条资讯</span>
                </div>
                <div class="space-x-2">
                  <el-button type="success" size="small" class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <upload />
                    </el-icon>批量发布
                  </el-button>
                  <el-button type="warning" size="small" class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <download />
                    </el-icon>批量下线
                  </el-button>
                  <el-button type="danger" size="small" class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <delete />
                    </el-icon>批量删除
                  </el-button>
                </div>
              </div> -->
            </el-card>
          </el-col>
          <el-col v-if="false" :span="6">
            <el-card shadow="hover">
              <template #header>
                <div class="font-bold">
                  类型分布
                </div>
              </template>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm">培训通知</span>
                  <span class="text-sm font-medium">42篇 (32.8%)</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm">学习资料</span>
                  <span class="text-sm font-medium">35篇 (27.3%)</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm">政策解读</span>
                  <span class="text-sm font-medium">28篇 (21.9%)</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm">经验分享</span>
                  <span class="text-sm font-medium">15篇 (11.7%)</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm">工作动态</span>
                  <span class="text-sm font-medium">8篇 (6.3%)</span>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="font-bold">
                  热门资讯
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <p class="text-sm font-medium">
                    财务合规新政策解读
                  </p>
                  <p class="text-xs text-gray-500">
                    阅读量 356次
                  </p>
                </div>
                <div>
                  <p class="text-sm font-medium">
                    合规培训考试安排通知
                  </p>
                  <p class="text-xs text-gray-500">
                    阅读量 289次
                  </p>
                </div>
                <div>
                  <p class="text-sm font-medium">
                    优秀合规案例分享
                  </p>
                  <p class="text-xs text-gray-500">
                    阅读量 234次
                  </p>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="font-bold">
                  发布趋势
                </div>
              </template>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="text-sm">本周发布</span>
                  <span class="text-sm font-medium">12篇 <span class="text-green-500">+3篇</span></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm">本月发布</span>
                  <span class="text-sm font-medium">35篇 <span class="text-green-500">+8篇</span></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm">员工参与度</span>
                  <span class="text-sm font-medium">82.5%</span>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="font-bold">
                  待处理事项
                </div>
              </template>
              <div class="space-y-2">
                <div class="flex items-center">
                  <el-icon class="mr-2 text-yellow-500">
                    <Warning />
                  </el-icon>
                  <span class="text-sm">8篇资讯待审核</span>
                </div>
                <div class="flex items-center">
                  <el-icon class="mr-2 text-gray-500">
                    <Document />
                  </el-icon>
                  <span class="text-sm">5篇草稿未完成</span>
                </div>
                <div class="flex items-center">
                  <el-icon class="mr-2 text-orange-500">
                    <InfoFilled />
                  </el-icon>
                  <span class="text-sm">3篇资讯阅读量偏低</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>

    <!-- 新增标签弹窗 -->
    <el-dialog v-model="tagDialogVisible" :title="isEditingTag ? '编辑标签' : '新增标签'" width="500px" @close="closeTagDialog">
      <el-form ref="tagFormRef" :model="tagForm" :rules="tagFormRules" label-width="80px">
        <el-form-item label="标签名称" prop="name">
          <el-input v-model="tagForm.name" placeholder="请输入标签名称" />
        </el-form-item>
        <el-form-item label="标签描述">
          <el-input v-model="tagForm.description" type="textarea" :rows="3" placeholder="请输入标签描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeTagDialog">
            取消
          </el-button>
          <el-button type="primary" @click="submitTagForm">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增分类弹窗 -->
    <el-dialog v-model="categoryDialogVisible" :title="isEditingCategory ? '编辑分类' : '新增分类'" width="500px" @close="closeCategoryDialog">
      <el-form ref="categoryFormRef" :model="categoryForm" :rules="categoryFormRules" label-width="100px">
        <!-- <el-form-item label="状态" prop="status">
          <el-select v-model="categoryForm.status" placeholder="请选择状态">
            <el-option label="草稿" value="DRAFT" />
            <el-option label="待审核" value="PENDING_REVIEW" />
            <el-option label="已发布" value="PUBLISHED" />
            <el-option label="已归档" value="ARCHIVED" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="排序序号">
          <el-input-number v-model="categoryForm.sortOrder" :min="1" placeholder="请输入排序序号" />
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类描述">
          <el-input v-model="categoryForm.description" type="textarea" :rows="3" placeholder="请输入分类描述" />
        </el-form-item>
        <!-- <el-form-item label="封面图URL">
          <el-input v-model="categoryForm.coverImageUrl" placeholder="请输入封面图URL" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeCategoryDialog">
            取消
          </el-button>
          <el-button type="primary" @click="submitCategoryForm">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 标签管理弹窗 -->
    <el-dialog v-model="tagManageDialogVisible" title="标签管理" width="800px" @close="closeTagManageDialog">
      <div class="mb-4">
        <el-button type="primary" @click="openTagDialog">
          <el-icon class="mr-1">
            <Plus />
          </el-icon>
          新增标签
        </el-button>
      </div>
      <el-table v-loading="tagLoading" :data="tagList" style="width: 100%">
        <el-table-column prop="name" label="标签名称" width="200" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ row.createdAt || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="updatedAt" label="更新时间" width="180">
          <template #default="{ row }">
            {{ row.updatedAt || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" :underline="false" class="mr-3" @click="editTag(row)">
              编辑
            </el-button>
            <el-button type="danger" :underline="false" @click="deleteTag(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 分类管理弹窗 -->
    <el-dialog v-model="categoryManageDialogVisible" title="分类管理" width="800px" @close="closeCategoryManageDialog">
      <div class="mb-4">
        <el-button v-auth="['consultingNews/index/addCategory']" type="primary" @click="openCategoryDialog">
          <el-icon class="mr-1">
            <Plus />
          </el-icon>
          新增分类
        </el-button>
      </div>
      <el-table v-loading="categoryLoading" :data="categoryList" style="width: 100%">
        <el-table-column prop="name" label="分类名称" width="200" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ row.createdAt || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="updatedAt" label="更新时间" width="180">
          <template #default="{ row }">
            {{ row.updatedAt || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" :underline="false" class="mr-3" @click="editCategory(row)">
              编辑
            </el-button>
            <el-button type="danger" :underline="false" @click="deleteCategory(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss.scss";
</style>

<style scoped>
  :deep(.el-table .published-row) {
    --el-table-tr-bg-color: #f0fdf4;
    border-left: 4px solid #059669;
  }

  :deep(.el-table .pending-row) {
    --el-table-tr-bg-color: #fef3cd;
    border-left: 4px solid #d97706;
  }

  :deep(.el-table .draft-row) {
    --el-table-tr-bg-color: #f8fafc;
    border-left: 4px solid #9ca3af;
  }

  :deep(.el-table .offline-row) {
    --el-table-tr-bg-color: #fef2f2;
    border-left: 4px solid #ef4444;
  }
</style>
