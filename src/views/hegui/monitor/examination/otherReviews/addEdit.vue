<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import {
  Bell,
  DataBoard,
  Delete,
  Monitor,
  Plus,
  Search,
  UploadFilled,
  Warning,
} from '@element-plus/icons-vue'
import otherApi from '@/api/review/other'
import dictApi from '@/api/modules/system/dict'
import UploadMbb from '@/components/uploadMbb/index.vue'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import DepartPerson from '@/components/departPerson/index.vue'
import { disablePastDates } from '@/utils/dateUtils'

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  reviewCode: '',
  reviewObject: '',
  level: 'GENERAL',
  department: null,
  auditBy: '',
  deadlineDate: null,
  explain: '',
  reviewRange: '',
  reviewTarget: '',
  reviewAccording: '',
  keyAttribute: [] as Array<{ name: string, value: string, explain: string }>,
  noticeMethod: null,
  noticeObject: '',
  statusNotice: null,
  startRemind: 1,
  deadlineRemind: 1,
  // 审查规则相关字段 - 对应supplementalReviewRule
  processId: 0,
  importantConfig: '',
  matchRange: '',
  // 通知设置相关字段（与后端字段名保持一致）
  // noticeMethod、noticeObject、statusNotice 已在上面定义
  // 风险类型字段

  // 子对象数据
  supplementalAttachments: [] as any[],
  supplementalReviewRisks: [] as Array<{
    riskSelf: string
    riskType: any
    riskAssessment: string
    riskDesc: string
    level: any
    effectRange: string
    countermeasures: string
  }>,
  supplementalReviewSpecials: [] as Array<{
    focus: string
    reviewRequirement: string
    explain: string
    relatedName: string
    relatedType: any
    relatedExplain: string
  }>,
  supplementalReviewRule: null,
})

// 生成审查编号
async function generateReviewCode() {
  try {
    const response = await dictApi.getCode('SUPPLEMENTAL')
    if (response) {
      formData.reviewCode = response
      ElMessage.success('审查编号生成成功')
    }
  }
  catch (error) {
    console.error('获取审查编号失败:', error)
    ElMessage.error('获取审查编号失败')
  }
}

// 关键属性管理函数
function addKeyAttribute() {
  formData.keyAttribute.push({
    name: '',
    value: '',
    explain: '',
  })
}

function removeKeyAttribute(index: number) {
  formData.keyAttribute.splice(index, 1)
}

// 风险评估管理函数
function addRiskItem() {
  formData.supplementalReviewRisks.push({
    riskSelf: '',
    riskType: 'LAWS',
    riskAssessment: '',
    riskDesc: '',
    level: 'LOW',
    effectRange: 'FINANCE',
    countermeasures: '',
  })
}

function removeRiskItem(index: number) {
  formData.supplementalReviewRisks.splice(index, 1)
}

// 特殊要求管理函数
function addSpecialItem() {
  formData.supplementalReviewSpecials.push({
    focus: '',
    reviewRequirement: 'LEGAL_COMPLIANCE',
    explain: '',
    relatedName: '',
    relatedType: '',
    relatedExplain: '',
  })
}

function removeSpecialItem(index: number) {
  formData.supplementalReviewSpecials.splice(index, 1)
}

// 附件上传成功处理
function handleAttachmentUploadSuccess(_fileInfo: any) {
  // 附件上传成功，循环数据添加attachmentType字段
  // formData.supplementalAttachments.forEach((item) => {
  //   if (!item.attachmentType) {
  //     item.attachmentType = 1
  //   }
  // })
}

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入审查名称', trigger: 'blur' },
  ],
  reviewObject: [
    { required: true, message: '请输入审查对象', trigger: 'blur' },
  ],
  level: [
    { required: true, message: '请选择审查级别', trigger: 'change' },
  ],

  department: [
    { required: true, message: '请选择发起部门', trigger: 'change' },
  ],
  auditBy: [
    { required: true, message: '请选择审查人员', trigger: 'change' },
  ],
  reviewRange: [
    { required: true, message: '请输入审查范围', trigger: 'blur' },
  ],
  reviewTarget: [
    { required: true, message: '请输入审查目标', trigger: 'blur' },
  ],

})

// 保存草稿
async function handleSave() {
  if (!formRef.value) { return }

  try {
    await formRef.value.validate()

    loading.value = true

    // 准备提交的数据，直接使用formData并设置特殊字段
    const submitData = {
      ...formData,
      // 将关键属性数组转换为JSON字符串
      keyAttribute: JSON.stringify(formData.keyAttribute),
    }

    // 附件数据已通过组件自动处理，无需手动构建

    // 风险评估和特殊要求数据已经是数组形式，直接使用
    // submitData.supplementalReviewRisks 和 submitData.supplementalReviewSpecials 已包含在 formData 中

    // 根据是否有ID判断是新增还是编辑
    let result
    if (submitData.id) {
      // 编辑
      result = await otherApi.supplementalReview(null, submitData, 'update')
    }
    else {
      // 新增
      result = await otherApi.supplementalReview(null, submitData, 'create')
    }

    if (result) {
      ElMessage.success('保存成功')
      if (!formData.id && result.data) {
        formData.id = result.data.id
      }
      router.back()
    }
    else {
      ElMessage.error(result.msg || '保存失败')
    }
  }
  catch (error) {
    console.error('表单验证失败', error)
    ElMessage.error('请完善必填信息')
  }
  finally {
    loading.value = false
  }
}

// 提交审查
async function handleSubmit() {
  if (!formRef.value) { return }

  try {
    await formRef.value.validate()

    loading.value = true

    // 准备提交的数据，直接使用formData并设置特殊字段
    const submitData = {
      ...formData,
      // 将关键属性数组转换为JSON字符串
      keyAttribute: JSON.stringify(formData.keyAttribute),
    }

    // 附件数据已通过组件自动处理，无需手动构建

    // 风险评估和特殊要求数据已经是数组形式，直接使用
    // submitData.supplementalReviewRisks 和 submitData.supplementalReviewSpecials 已包含在 formData 中

    // 根据是否有ID判断是新增还是编辑
    let result
    if (submitData.id) {
      // 编辑
      result = await otherApi.supplementalReview(null, submitData, 'update')
    }
    else {
      // 新增
      result = await otherApi.supplementalReview(null, submitData, 'create')
    }

    if (result) {
      ElMessage.success('提交成功')
      router.push('/monitor/examination/ohter')
    }
    else {
      ElMessage.error(result.msg || '提交失败')
    }
  }
  catch (error) {
    console.error('表单验证失败', error)
    ElMessage.error('请完善必填信息')
  }
  finally {
    loading.value = false
  }
}

// 取消
function handleCancel() {
  ElMessageBox.confirm('确定要取消编辑吗？未保存的内容将丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    router.back()
  }).catch(() => {})
}

// 获取详情数据
async function getDetailData() {
  if (!route.query.id) { return }

  try {
    loading.value = true
    const response = await otherApi.supplementalReview({}, { id: route.query.id }, 'info')
    const data = response

    // 处理关键属性字段的JSON转换
    if (data.keyAttribute && typeof data.keyAttribute === 'string') {
      try {
        data.keyAttribute = JSON.parse(data.keyAttribute)
      }
      catch (e) {
        console.warn('关键属性字段解析失败:', e)
        data.keyAttribute = []
      }
    }
    else if (!data.keyAttribute) {
      data.keyAttribute = []
    }

    // 映射基础字段和数组数据
    Object.assign(formData, {
      ...data,
      // 确保数组字段正确映射
      supplementalReviewRisks: data.supplementalReviewRisks || [],
      supplementalReviewSpecials: data.supplementalReviewSpecials || [],
    })
  }
  catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
  finally {
    loading.value = false
  }
}

onMounted(async () => {
  if (route.query.id) {
    // 编辑模式，加载数据
    await getDetailData()
  }
  else {
    // 新增模式，生成编号并初始化
    generateReviewCode()
    // 初始化一个空的关键属性项
    if (formData.keyAttribute.length === 0) {
      addKeyAttribute()
    }
    // 初始化一个空的风险评估项
    if (formData.supplementalReviewRisks.length === 0) {
      addRiskItem()
    }
    // 初始化一个空的特殊要求项
    if (formData.supplementalReviewSpecials.length === 0) {
      addSpecialItem()
    }
  }
})
</script>

<template>
  <div class="absolute-container">
    <pageHeader>
      <template #content>
        <!-- 顶部标题栏 -->
        <div class="mb-6 flex items-center justify-between">
          <h1 class="text-xl font-bold">
            新增其他审查
          </h1>
          <div class="flex space-x-3">
            <el-button v-if="!route.query.id || formData.status === 'MODIFY'" v-debounce="2000" type="primary" class="!rounded-button whitespace-nowrap" :loading="loading" @click="handleSave">
              保存
            </el-button>
            <!-- <el-button v-auth="'otherReviews/addEdit/submit'" class="!rounded-button whitespace-nowrap" :loading="loading" @click="handleSubmit">
              提交审查
            </el-button> -->
            <el-button v-debounce="2000" class="!rounded-button whitespace-nowrap" @click="handleCancel">
              取消
            </el-button>
          </div>
        </div>
      </template>
    </pageHeader>
    <!-- 主内容区 -->
    <PageMain style="background-color: transparent;">
      <div>
        <!-- 页面内容 -->
        <div class="p-6">
          <div class="grid grid-cols-4 m-20">
            <!-- 主表单区 -->
            <div class="col-span-4 space-y-6">
              <!-- 基本信息 -->
              <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
                <el-card shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      基本信息
                    </div>
                  </template>
                  <div class="grid grid-cols-2 gap-4">
                    <el-form-item label="审查名称" prop="name" required>
                      <el-input v-model="formData.name" placeholder="请输入审查名称" />
                    </el-form-item>
                    <el-form-item label="审查编号">
                      <el-input v-model="formData.reviewCode" placeholder="自动生成" readonly />
                    </el-form-item>
                    <el-form-item label="审查对象" prop="reviewObject" required>
                      <el-input v-model="formData.reviewObject" placeholder="请输入审查对象" />
                    </el-form-item>
                    <el-form-item label="审查级别" prop="level" required>
                      <el-radio-group v-model="formData.level">
                        <el-radio-button value="GENERAL">
                          一般
                        </el-radio-button>
                        <el-radio-button value="IMPORTANT">
                          重要
                        </el-radio-button>
                        <el-radio-button value="CRITICAL">
                          关键
                        </el-radio-button>
                      </el-radio-group>
                    </el-form-item>

                    <el-form-item label="发起部门" prop="department" required>
                      <DepartmentTreeSelect v-model="formData.department" placeholder="请选择部门" />
                    </el-form-item>

                    <el-form-item label="审查人员" prop="auditBy" required>
                      <DepartPerson
                        v-model="formData.auditBy"
                        placeholder="请选择审查人员"
                      />
                    </el-form-item>
                    <el-form-item label="审查截止日期">
                      <el-date-picker
                        v-model="formData.deadlineDate"
                        :disabled-date="disablePastDates" format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" type="date" placeholder="选择日期" class="w-full"
                      />
                    </el-form-item>
                    <el-form-item label="审查说明" class="col-span-2">
                      <el-input v-model="formData.explain" type="textarea" :rows="3" placeholder="请输入审查说明" />
                    </el-form-item>
                  </div>
                </el-card>
                <!-- 审查内容 -->
                <el-card shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      审查内容
                    </div>
                  </template>
                  <div class="space-y-4">
                    <el-form-item label="审查范围" prop="reviewRange" required>
                      <el-input v-model="formData.reviewRange" type="textarea" :rows="2" placeholder="请输入审查范围" />
                    </el-form-item>
                    <el-form-item label="审查目标" prop="reviewTarget" required>
                      <el-input v-model="formData.reviewTarget" type="textarea" :rows="2" placeholder="请输入审查目标" />
                    </el-form-item>
                    <!-- <el-form-item label="对象描述">
                      <div class="border rounded">
                        <el-tiptap :height="200" />
                      </div>
                    </el-form-item> -->
                    <el-form-item label="审查依据">
                      <el-input v-model="formData.reviewAccording" type="textarea" :rows="2" placeholder="请输入审查依据" />
                    </el-form-item>
                    <el-form-item label="关键属性">
                      <div class="space-y-3">
                        <div v-for="(item, index) in formData.keyAttribute" :key="index" class="flex items-center space-x-2">
                          <el-input v-model="item.name" placeholder="属性名称" />
                          <el-input v-model="item.value" placeholder="属性值" />
                          <el-input v-model="item.explain" placeholder="属性说明" />
                          <el-button v-auth="'otherReviews/addEdit/deleteAttribute'" type="danger" text @click="removeKeyAttribute(index)">
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                        <el-button v-auth="'otherReviews/addEdit/addAttribute'" type="primary" text @click="addKeyAttribute">
                          <el-icon><Plus /></el-icon>
                          添加属性
                        </el-button>
                      </div>
                    </el-form-item>
                  </div>
                </el-card>
                <!-- 文档上传 -->
                <el-card shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      文档上传
                    </div>
                  </template>
                  <div class="space-y-6">
                    <el-form-item label-position="top" label="审查附件">
                      <UploadMbb
                        v-model="formData.supplementalAttachments"
                        :auto-upload="true"
                        :multiple="true"
                        :use-file-path="true"
                        category-name="attachment"
                        tip-text="支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 20MB"
                        @upload-success="handleAttachmentUploadSuccess"
                      />
                    </el-form-item>
                  </div>
                </el-card>
                <!-- 风险评估 -->
                <el-card v-if="false" shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      风险评估
                    </div>
                  </template>
                  <div class="space-y-6">
                    <!-- <el-form-item label="风险自评">
                      <el-select v-model="formData.riskType" placeholder="请选择风险类型" class="w-full">
                        <el-option value="LEGAL" label="法律风险" />
                        <el-option value="COMPLIANCE" label="合规风险" />
                        <el-option value="TECHNICAL" label="技术风险" />
                        <el-option value="BUSINESS" label="商业风险" />
                        <el-option value="REPUTATION" label="声誉风险" />
                        <el-option value="OTHER" label="其他" />
                      </el-select>
                      <el-input
                        v-model="formData.riskDescription"
                        type="textarea"
                        :rows="2"
                        placeholder="请输入风险描述"
                        class="mt-2"
                      />
                    </el-form-item> -->
                    <el-form-item label="自动风险评估">
                      <div class="flex items-center space-x-4">
                        <el-button v-auth="'otherReviews/addEdit/startAssessment'" v-debounce="2000" type="primary" class="!rounded-button whitespace-nowrap">
                          开始评估
                        </el-button>
                        <div class="text-gray-500">
                          点击按钮进行自动风险分析
                        </div>
                      </div>
                      <div class="mt-4 rounded bg-gray-50 p-4">
                        <div class="text-gray-500">
                          评估结果将显示在此处
                        </div>
                      </div>
                    </el-form-item>
                    <el-form-item label="风险点管理">
                      <div class="space-y-4">
                        <div v-for="(risk, index) in formData.supplementalReviewRisks" :key="index" class="border rounded-lg p-6 space-y-4">
                          <div class="grid grid-cols-2 mb-4 gap-4">
                            <el-form-item label="风险自评" class="mb-0">
                              <el-input v-model="risk.riskSelf" placeholder="请输入风险自评" />
                            </el-form-item>
                            <el-form-item label="风险类型" class="mb-0">
                              <el-select v-model="risk.riskType" placeholder="请选择风险类型">
                                <el-option value="LEGAL" label="法律风险" />
                                <el-option value="COMPLIANCE" label="合规风险" />
                                <el-option value="TECHNICAL" label="技术风险" />
                                <el-option value="BUSINESS" label="商业风险" />
                                <el-option value="REPUTATION" label="声誉风险" />
                                <el-option value="OTHER" label="其他" />
                              </el-select>
                            </el-form-item>
                            <el-form-item label="风险等级" class="mb-0">
                              <el-select v-model="risk.level" placeholder="请选择风险等级">
                                <el-option label="高" value="HIGH" />
                                <el-option label="中" value="MEDIUM" />
                                <el-option label="低" value="LOW" />
                              </el-select>
                            </el-form-item>
                            <el-form-item label="影响范围" class="mb-0">
                              <el-select v-model="risk.effectRange" placeholder="请选择影响范围">
                                <el-option label="财务" value="FINANCE" />
                                <el-option label="运营" value="OPERATION" />
                                <el-option label="客户" value="CUSTOMER" />
                                <el-option label="声誉" value="REPUTATION" />
                              </el-select>
                            </el-form-item>
                          </div>
                          <el-form-item label="风险评估" class="mb-4">
                            <el-input v-model="risk.riskAssessment" type="textarea" :rows="2" placeholder="请输入风险评估" />
                          </el-form-item>
                          <el-form-item label="风险描述" class="mb-4">
                            <el-input v-model="risk.riskDesc" type="textarea" :rows="2" placeholder="请输入风险描述" />
                          </el-form-item>
                          <el-form-item label="应对措施" class="mb-4">
                            <el-input v-model="risk.countermeasures" type="textarea" :rows="2" placeholder="请输入应对措施" />
                          </el-form-item>
                          <div class="flex justify-end pt-2">
                            <el-button v-auth="'otherReviews/addEdit/deleteRisk'" v-debounce="2000" type="danger" text @click="removeRiskItem(index)">
                              <el-icon><Delete /></el-icon>
                              删除
                            </el-button>
                          </div>
                        </div>
                        <div class="pt-2">
                          <el-button v-auth="'otherReviews/addEdit/addRisk'" v-debounce="2000" type="primary" text @click="addRiskItem">
                            <el-icon><Plus /></el-icon>
                            添加风险点
                          </el-button>
                        </div>
                      </div>
                    </el-form-item>
                  </div>
                <!-- 第一个 -->
                </el-card>
                <!-- 特殊要求区 -->
                <el-card v-if="false" shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      特殊要求
                    </div>
                  </template>
                  <div class="space-y-6">
                    <el-form-item label="特殊要求管理">
                      <div class="space-y-4">
                        <div v-for="(special, index) in formData.supplementalReviewSpecials" :key="index" class="border rounded-lg p-6 space-y-4">
                          <el-form-item label="关注重点" class="mb-4">
                            <el-input v-model="special.focus" type="textarea" :rows="3" placeholder="请输入审查中需特别关注的内容" />
                          </el-form-item>
                          <el-form-item label="特殊审查要求" class="mb-4">
                            <el-select v-model="special.reviewRequirement" placeholder="请选择审查要求">
                              <el-option label="法律合规性审查" value="LEGAL_COMPLIANCE" />
                              <el-option label="技术安全性审查" value="TECHNICAL_SECURITY" />
                              <el-option label="商业风险评估" value="BUSINESS_RISK" />
                              <el-option label="隐私保护审查" value="PRIVACY_PROTECTION" />
                              <el-option label="其他特殊要求" value="OTHER" />
                            </el-select>
                          </el-form-item>
                          <el-form-item label="特殊说明" class="mb-4">
                            <el-input v-model="special.explain" type="textarea" :rows="3" placeholder="请输入特殊说明" />
                          </el-form-item>
                          <div class="grid grid-cols-2 mb-4 gap-4">
                            <el-form-item label="相关方名称" class="mb-0">
                              <el-input v-model="special.relatedName" placeholder="请输入相关方名称" />
                            </el-form-item>
                            <el-form-item label="相关方类型" class="mb-0">
                              <el-select v-model="special.relatedType" placeholder="请选择类型">
                                <el-option label="内部部门" value="INTERNAL_DEPT" />
                                <el-option label="外部机构" value="EXTERNAL_ORG" />
                                <el-option label="合作伙伴" value="PARTNER" />
                                <el-option label="监管机构" value="REGULATOR" />
                              </el-select>
                            </el-form-item>
                            <el-form-item label="关系说明" class="col-span-2 mb-0">
                              <el-input v-model="special.relatedExplain" type="textarea" :rows="2" placeholder="请输入关系说明" />
                            </el-form-item>
                          </div>
                          <div class="flex justify-end pt-2">
                            <el-button v-auth="'otherReviews/addEdit/deleteSpecial'" v-debounce="2000" type="danger" text @click="removeSpecialItem(index)">
                              <el-icon><Delete /></el-icon>
                              删除
                            </el-button>
                          </div>
                        </div>
                        <div class="pt-2">
                          <el-button v-auth="'otherReviews/addEdit/addSpecial'" v-debounce="2000" type="primary" text @click="addSpecialItem">
                            <el-icon><Plus /></el-icon>
                            添加特殊要求
                          </el-button>
                        </div>
                      </div>
                    </el-form-item>
                  </div>
                <!-- 第二个 -->
                </el-card>
                <!-- 审查规则配置区 -->
                <el-card v-if="false" shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      审查规则
                    </div>
                  </template>
                  <div class="space-y-6">
                    <el-form-item label="流程ID">
                      <el-input-number v-model="formData.processId" placeholder="请输入流程ID" class="w-full" />
                    </el-form-item>
                    <el-form-item label="重要配置">
                      <el-input v-model="formData.importantConfig" type="textarea" :rows="3" placeholder="请输入重要配置信息" />
                    </el-form-item>
                    <el-form-item label="匹配范围">
                      <el-input v-model="formData.matchRange" type="textarea" :rows="3" placeholder="请输入匹配范围" />
                    </el-form-item>
                    <el-form-item label="审批流程">
                      <div class="border rounded p-4">
                        <div class="mb-4 flex items-center justify-between">
                          <div class="text-gray-500">
                            当前审批流程
                          </div>
                          <el-button type="primary" text>
                            <el-icon><Plus /></el-icon>
                            添加节点
                          </el-button>
                        </div>
                        <div class="space-y-3">
                          <div class="flex items-center justify-between rounded bg-gray-50 p-3">
                            <div>
                              <div class="font-medium">
                                初审
                              </div>
                              <div class="text-sm text-gray-500">
                                技术部负责人
                              </div>
                            </div>
                            <div class="flex space-x-2">
                              <el-button size="small" text>
                                编辑
                              </el-button>
                              <el-button size="small" text type="danger">
                                删除
                              </el-button>
                            </div>
                          </div>
                          <div class="flex items-center justify-between rounded bg-gray-50 p-3">
                            <div>
                              <div class="font-medium">
                                复审
                              </div>
                              <div class="text-sm text-gray-500">
                                合规委员会
                              </div>
                            </div>
                            <div class="flex space-x-2">
                              <el-button size="small" text>
                                编辑
                              </el-button>
                              <el-button size="small" text type="danger">
                                删除
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-form-item>
                  </div>
                <!-- 第三个 -->
                </el-card>

                <!-- 通知设置区 -->
                <el-card v-if="false" shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      通知设置
                    </div>
                  </template>
                  <div class="space-y-6">
                    <el-form-item label="通知方式">
                      <el-select v-model="formData.noticeMethod" placeholder="请选择通知方式" clearable>
                        <el-option value="SYSTEM" label="系统消息" />
                        <el-option value="EMAIL" label="邮件通知" />
                        <el-option value="SMS" label="短信提醒" />
                        <el-option value="OTHER" label="其他方式" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="通知对象">
                      <el-input v-model="formData.noticeObject" placeholder="请输入通知对象" />
                    </el-form-item>
                    <el-form-item label="重要状态通知">
                      <el-select v-model="formData.statusNotice" placeholder="请选择重要状态通知" clearable>
                        <el-option value="REVIEW_START" label="审查开始" />
                        <el-option value="REVIEW_DELAY" label="审查延期" />
                        <el-option value="REVIEW_COMPLETE" label="审查完成" />
                        <el-option value="RISK_ESCALATION" label="风险升级" />
                        <el-option value="APPROVAL_PASS" label="审批通过" />
                        <el-option value="APPROVAL_REJECT" label="审批驳回" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="提醒设置">
                      <div class="grid grid-cols-2 gap-4">
                        <el-form-item label="开始提醒">
                          <el-input-number v-model="formData.startRemind" :min="1" :max="30" />
                          <span class="ml-2 text-gray-500">天前提醒</span>
                        </el-form-item>
                        <el-form-item label="截止提醒">
                          <el-input-number v-model="formData.deadlineRemind" :min="1" :max="30" />
                          <span class="ml-2 text-gray-500">天前提醒</span>
                        </el-form-item>
                      </div>
                    </el-form-item>
                  </div>
                <!-- 第四个 -->
                </el-card>
              </el-form>
            </div>
            <!-- 右侧辅助区 -->
            <div v-if="false" class="space-y-6">
              <el-card shadow="hover" class="!border-0">
                <template #header>
                  <div class="text-base font-bold">
                    审查指南
                  </div>
                </template>
                <div class="text-sm text-gray-600 space-y-2">
                  <p>1. 确保审查名称简洁明了</p>
                  <p>2. 审查对象需具体到产品或系统</p>
                  <p>3. 审查级别应根据重要性合理选择</p>
                  <el-link type="primary" class="mt-2">
                    查看详细指南
                  </el-link>
                </div>
              </el-card>
              <el-card shadow="hover" class="!border-0">
                <template #header>
                  <div class="text-base font-bold">
                    审查模板
                  </div>
                </template>
                <div class="space-y-2">
                  <el-link type="primary">
                    安全合规审查模板
                  </el-link>
                  <el-link type="primary">
                    人事合规审查模板
                  </el-link>
                  <el-link type="primary">
                    IT系统合规审查模板
                  </el-link>
                  <el-link type="primary">
                    产品发布合规模板
                  </el-link>
                </div>
              </el-card>
              <el-card shadow="hover" class="!border-0">
                <template #header>
                  <div class="text-base font-bold">
                    AI辅助
                  </div>
                </template>
                <div class="space-y-3">
                  <el-button v-auth="'otherReviews/addEdit/aiAutoFill'" class="!rounded-button w-full whitespace-nowrap">
                    AI智能填写
                  </el-button>
                  <el-button v-auth="'otherReviews/addEdit/aiRiskAnalysis'" class="!rounded-button w-full whitespace-nowrap">
                    AI风险预分析
                  </el-button>
                  <el-button v-auth="'otherReviews/addEdit/aiSuggestion'" class="!rounded-button w-full whitespace-nowrap">
                    AI审查建议
                  </el-button>
                  <div class="mt-2 rounded bg-gray-50 p-3">
                    <div class="text-sm text-gray-500">
                      AI建议将显示在此处
                    </div>
                  </div>
                </div>
              </el-card>
              <el-card shadow="hover" class="!border-0">
                <template #header>
                  <div class="text-base font-bold">
                    合规参考
                  </div>
                </template>
                <div class="space-y-2">
                  <el-link type="primary">
                    《网络安全法》
                  </el-link>
                  <el-link type="primary">
                    《个人信息保护法》
                  </el-link>
                  <el-link type="primary">
                    ISO 27001标准
                  </el-link>
                  <el-link type="primary" class="mt-2 block">
                    查看相似审查
                  </el-link>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

  <style lang="scss" scoped>
  @use "@/styles/toolsCss";
  </style>
