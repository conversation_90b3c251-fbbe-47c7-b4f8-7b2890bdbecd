<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useRouter } from 'vue-router'
import storage from '@/utils/storage'
import baseInfoApi from '@/api/personal/baseInfo'
import ImageUpload from '@/components/ImageUpload/index.vue'
import useUserStore from '@/store/modules/user'

const router = useRouter()
const userStore = useUserStore()

// 生成上传URL
const uploadActionUrl = computed(() => {
  const baseUrl = (import.meta.env.DEV && import.meta.env.VITE_OPEN_PROXY === 'true')
    ? '/proxy/'
    : import.meta.env.VITE_APP_API_BASEURL
  return `${baseUrl}personal/api/file/upload?serviceName=personal&categoryName=uploadFile`
})

// 表单数据
const form = ref({
  realName: '',
  phone: '',
  email: '',
  gender: '',
  idCard: '',
  avatar: '',
  address: '',
  emergencyContact: '',
  emergencyPhone: '',
  introduction: '',
  skills: [],
  hobbies: [],
})

// 表单验证规则
const formRules: FormRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email' as const, message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' },
  ],
}

const formRef = ref<FormInstance>()
const loading = ref(false)
const skillInput = ref('')
const hobbyInput = ref('')

// 获取用户详情
function getUserDetail() {
  const userDetail = JSON.parse(storage.local.get('userDetail') || '{}')
  if (userDetail) {
    form.value = {
      realName: userDetail.realName || '',
      phone: userDetail.phone || '',
      email: userDetail.email || '',
      gender: userDetail.gender || '',
      idCard: userDetail.idCard || '',
      avatar: userDetail.avatar || '',
      address: userDetail.address || '',
      emergencyContact: userDetail.emergencyContact || '',
      emergencyPhone: userDetail.emergencyPhone || '',
      introduction: userDetail.introduction || '',
      skills: userDetail.skills || [],
      hobbies: userDetail.hobbies || [],
    }
  }
}

// 头像上传成功回调
function handleAvatarSuccess(url: string) {
  form.value.avatar = url
  ElMessage.success('头像上传成功')
}

// 添加技能
function addSkill() {
  if (skillInput.value.trim() && !form.value.skills.includes(skillInput.value.trim())) {
    form.value.skills.push(skillInput.value.trim())
    skillInput.value = ''
  }
}

// 删除技能
function removeSkill(index: number) {
  form.value.skills.splice(index, 1)
}

// 添加爱好
function addHobby() {
  if (hobbyInput.value.trim() && !form.value.hobbies.includes(hobbyInput.value.trim())) {
    form.value.hobbies.push(hobbyInput.value.trim())
    hobbyInput.value = ''
  }
}

// 删除爱好
function removeHobby(index: number) {
  form.value.hobbies.splice(index, 1)
}

// 提交表单
function submitForm() {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        loading.value = true
        await baseInfoApi.updatePersonalInfo(userStore.userId, form.value)

        // 更新本地存储
        const userDetail = JSON.parse(storage.local.get('userDetail') || '{}')
        const updatedUserDetail = { ...userDetail, ...form.value }
        storage.local.set('userDetail', JSON.stringify(updatedUserDetail))

        ElMessage.success('个人信息更新成功')
        router.back()
      }
      catch (error) {
        console.error('更新失败:', error)
        ElMessage.error('更新失败，请重试')
      }
      finally {
        loading.value = false
      }
    }
  })
}

// 取消编辑
function cancelEdit() {
  router.back()
}

onMounted(() => {
  getUserDetail()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              编辑个人资料
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button class="!rounded-button whitespace-nowrap" @click="cancelEdit" v-debounce="3000">
              取消
            </el-button>
            <el-button v-auth="'basicInformation/detail/save'" type="primary" class="!rounded-button whitespace-nowrap" :loading="loading" @click="submitForm" v-debounce="3000">
              保存
            </el-button>
          </div>
        </div>
      </template>
    </page-header>

    <PageMain style="background-color: transparent;">
      <el-form ref="formRef" :model="form" :rules="formRules" label-width="120px" class="max-w-4xl">
        <el-row :gutter="20">
          <el-col :span="18">
            <!-- 基本信息 -->
            <el-card shadow="hover" class="mb-6">
              <template #header>
                <div class="f-16 fw-600">
                  基本信息
                </div>
              </template>

              <div class="flex space-x-8">
                <!-- 头像上传区域 -->
                <div class="flex flex-col items-center space-y-4">
                  <div class="mb-2 text-sm text-gray-500">
                    头像
                  </div>
                  <ImageUpload
                    v-model:url="form.avatar"
                    :size="5"
                    :width="120"
                    :height="120"
                    :ext="['jpg', 'jpeg', 'png']"
                    class="avatar-upload"
                    @on-success="handleAvatarSuccess"
                  />
                  <div class="text-center text-xs text-gray-400">
                    支持JPG、PNG格式<br>
                    文件大小不超过5MB
                  </div>
                </div>

                <!-- 基本信息表单 -->
                <div class="flex-1">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="真实姓名" prop="realName">
                        <el-input v-model="form.realName" placeholder="请输入真实姓名" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="性别" prop="gender">
                        <el-radio-group v-model="form.gender">
                          <el-radio label="MALE">
                            男
                          </el-radio>
                          <el-radio label="FEMALE">
                            女
                          </el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="手机号码" prop="phone">
                        <el-input v-model="form.phone" placeholder="请输入手机号码" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="电子邮箱" prop="email">
                        <el-input v-model="form.email" placeholder="请输入邮箱地址" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-form-item label="身份证号" prop="idCard">
                    <el-input v-model="form.idCard" placeholder="请输入身份证号" style="width: 300px;" />
                  </el-form-item>
                </div>
              </div>
            </el-card>

            <!-- 联系信息 -->
            <el-card shadow="hover" class="mb-6">
              <template #header>
                <div class="f-16 fw-600">
                  联系信息
                </div>
              </template>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="联系地址">
                    <el-input v-model="form.address" placeholder="请输入联系地址" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="紧急联系人">
                    <el-input v-model="form.emergencyContact" placeholder="请输入紧急联系人" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="紧急联系电话">
                    <el-input v-model="form.emergencyPhone" placeholder="请输入紧急联系电话" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

            <!-- 补充信息 -->
            <el-card v-if="false" shadow="hover" class="mb-6">
              <template #header>
                <div class="f-16 fw-600">
                  补充信息
                </div>
              </template>

              <el-form-item label="个人简介">
                <el-input
                  v-model="form.introduction"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入个人简介"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>

              <!-- 专业技能 -->
              <el-form-item label="专业技能">
                <div class="space-y-3">
                  <div class="flex space-x-2">
                    <el-input
                      v-model="skillInput"
                      placeholder="请输入技能标签"
                      style="width: 200px;"
                      @keyup.enter="addSkill"
                    />
                    <el-button type="primary" @click="addSkill">
                      添加
                    </el-button>
                  </div>
                  <div v-if="form.skills.length > 0" class="flex flex-wrap gap-2">
                    <el-tag
                      v-for="(skill, index) in form.skills"
                      :key="index"
                      closable
                      @close="removeSkill(index)"
                    >
                      {{ skill }}
                    </el-tag>
                  </div>
                </div>
              </el-form-item>

              <!-- 兴趣爱好 -->
              <el-form-item label="兴趣爱好">
                <div class="space-y-3">
                  <div class="flex space-x-2">
                    <el-input
                      v-model="hobbyInput"
                      placeholder="请输入兴趣爱好"
                      style="width: 200px;"
                      @keyup.enter="addHobby"
                    />
                    <el-button type="primary" @click="addHobby">
                      添加
                    </el-button>
                  </div>
                  <div v-if="form.hobbies.length > 0" class="flex flex-wrap gap-2">
                    <el-tag
                      v-for="(hobby, index) in form.hobbies"
                      :key="index"
                      closable
                      type="info"
                      @close="removeHobby(index)"
                    >
                      {{ hobby }}
                    </el-tag>
                  </div>
                </div>
              </el-form-item>
            </el-card>
          </el-col>

          <el-col :span="6">
            <!-- 操作提示 -->
            <el-card shadow="hover" class="mb-6">
              <template #header>
                <div class="f-16 fw-600">
                  操作提示
                </div>
              </template>

              <div class="text-sm text-gray-600 space-y-3">
                <div class="flex items-start space-x-2">
                  <div class="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500" />
                  <div>请确保填写的信息真实有效</div>
                </div>
                <div class="flex items-start space-x-2">
                  <div class="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500" />
                  <div>头像建议使用清晰的个人照片</div>
                </div>
                <div class="flex items-start space-x-2">
                  <div class="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500" />
                  <div>手机号和邮箱用于重要通知</div>
                </div>
                <div class="flex items-start space-x-2">
                  <div class="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500" />
                  <div>技能和爱好有助于团队了解您</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/toolsCss";

.avatar-upload {
  :deep(.el-upload) {
    border: 2px dashed #d9d9d9;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
    }
  }

  :deep(.el-upload-dragger) {
    border-radius: 50%;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>
