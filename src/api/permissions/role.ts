import api from '@/api/index'

export default {
  // 权限员工
  rolePermissions(params: any) {
    const employeeId = params.userId || params.employee || 1
    return api.get(`/whiskerguardorgservice/api/permissions/tree/employee/${employeeId}`, {})
  },
  // 权限树
  rolePermissionsTree() {
    return api.get(`/whiskerguardorgservice/api/permissions/menu/tree`, {})
  },
  // 角色权限
  rolePermissionsApi(roleId: any) {
    return api.get(`/whiskerguardorgservice/api/permissions/tree/role/${roleId}`, {})
  },
  // 关联角色与权限
  roleAndPermissions(params: any) {
    return api.post(`/whiskerguardorgservice/api/role/permissions/batch`, params)
  },
  // 根据部门id获取岗位
  getDeptByPositionId(params: any) {
    return api.get(`/whiskerguardorgservice/api/positions/dept/positions/${params.id}`, {})
  },
  // 获取所有部门
  unitTree() {
    return api.get('/whiskerguardorgservice/api/org/units/tree')
  },
  // 获取所有岗位
  getAllPosition() {
    return api.get('/whiskerguardorgservice/api/positions?page=0&size=100')
  },
  // 根据部门id获取员工
  getEmpByUnitId(orgUnitId: any) {
    return api.get(`/whiskerguardorgservice/api/employees/org/unit/${orgUnitId}`,
      {
      })
  },
  // 获取所有员工
  getAllEmployee() {
    return api.post('/whiskerguardorgservice/api/employees/page', {
      page: 0,
      size: 100,
    })
  },
}
