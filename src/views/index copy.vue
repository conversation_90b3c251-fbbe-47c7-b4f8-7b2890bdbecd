<script setup lang="ts">
import { ref } from 'vue'

const homeimg = new URL('@/assets/images/homeimg4.jpg', import.meta.url).href

const dataList: any = ref([])

const fxList: any = ref([
  {
    name: '财务部',
    status_name: '高风险',
    status: 3,
  },
  {
    name: '人力资源',
    status_name: '中风险',
    status: 2,
  },
  {
    name: '运营部',
    status_name: '底风险',
    status: 1,
  },
  {
    name: '财务部',
    status_name: '高风险',
    status: 1,
  },
  {
    name: '财务部',
    status_name: '高风险',
    status: 1,
  },
  {
    name: '财务部',
    status_name: '高风险',
    status: 1,
  },
])

const value_bm = ref('')

const options_bm = [
  {
    value: 'Option1',
    label: 'Option1',
  },
  {
    value: 'Option2',
    label: 'Option2',
  },
  {
    value: 'Option3',
    label: 'Option3',
  },
  {
    value: 'Option4',
    label: 'Option4',
  },
  {
    value: 'Option5',
    label: 'Option5',
  },
]
</script>

<template>
  <div class="absolute-container">
    <!--  欢迎回来，张经理
    <div style="padding: 20px; justify-content: center;
      align-items: center; height:80vh;" class="d-c flex w-100% h-100% bg-[#fff]">
    	<img :src="homeimg" />
    	<div class="font-size-6 ">欢迎进入后台管理系统！</div>
    </div> -->
    <!-- <PageHeader title="表格高度自适应" /> -->
    <PageMain style="background-color: transparent;">
      <div class="aic jcsb flex">
        <div class="f-24 fw-700">
          欢迎回来，张经理
        </div>
        <div class="font-size-3.5">
          2024年2月15日
        </div>
      </div>
      <div class="mt-20">
        <el-row :gutter="20">
          <el-col v-for="i, j in 4" :key="j" :span="6">
            <div class="jcsb card flex p-16">
              <div class="fdc flex">
                <div class="f-14 c-[#4B5563]">
                  合规风险总数
                </div>
                <div class="f-24 mt-16 fw-700">
                  128
                </div>
                <div v-if="j == 0" class="f-14 aic mt-10 flex fw-400">
                  <img
                    style="width: 14px;height: 14px;"
                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAM5JREFUOE+10d0JwjAQwPG7C/jsBqZO4LNV6AY6gB/pBOIEuoITWCv46ggVWvHVBdS6hticpFCR6kOsmOf75X8kCBUPVnTwXyiT0ZgQZLGdvtHCqihj5QkGqYWeIEBLEzlW0JTkfjBDJgeAGyyE/wab++GSAVsMdz91N8dXdOmEykmG0Rs0CACvmihA1lvmuw9APVMyKL8kVl7aDXbPYoHObjjPBw5KioyXTHA8t1fT8rflsIxs/hZlpOqilvVP7jqwAcWM9at+XPWb0s/FBxZWSZPljIYxAAAAAElFTkSuQmCC"
                    alt=""
                  >
                  <span class="f-14 ml-6 c-[#22C55E]">128</span>
                </div>
                <div v-else class="f-14 aic mt-10 flex fw-400">
                  <img
                    style="width: 14px;height: 14px;"
                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAL9JREFUOE+90dENgjAUBdD7yBtAov/iBDoCsQ2/4ga4gW6gEzgCOoEDtMG4ASM4gEkXaMDQiInoR8XEfve8+3pL6Hmop8Nv8DafZ6OiOHyT7hKNEDmIrqHWO1/8XLWLTRxHYM4BlKHWm+7Alze2GNYewXwC0IC4IoqGSmVuOykXsPbyVo5Jkj3qOgWwDJUqH5e3LTZSnsG88m7VSOlwUNdjb+jWC4IBqmoNYAbmiVeiESIF0bQtqGnfC376ov/DOxDLPmuyNMqVAAAAAElFTkSuQmCC"
                    alt=""
                  >
                  <span class="f-14 ml-6 c-[#EF4444]">128</span>
                </div>
              </div>
              <div>
                <img
                  v-if="j == 0" style="width: 32px;height: 32px;"
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgAgMAAAAOFJJnAAAADFBMVEUAAAAYj/8Ykf8YkP+pTBVBAAAAA3RSTlMAgH8BTzA4AAAANklEQVQY02NAAtw/oAz+P8Qx9jdAGf8PkM7QfwBl2H8gksH0D8pg/k8rBuNf9i/yF+od4GECAMK5alogF28rAAAAAElFTkSuQmCC"
                  alt=""
                >
                <img
                  v-if="j == 1" style="width: 32px;height: 32px;"
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAPFBMVEUAAAD6rRT6qxP6qxD5rBT6rhT7rhT6rRP6rhT7rRT/rxD5rRT6rxX4rRT8rBP0rxX0qhX6rhT6rBT6rRSpzDs9AAAAE3RSTlMA318fQL9/n++PEK9vb08wMM/Pyv5s0wAAAJZJREFUOMvN0UsOgzAMRVE7Xwjl077977WkchnUjtUhd2IJzJFI6H6tXBs5BQAzOTHOogf0sgv0kgO4ROwAO0T5/EIaEhFnG1EfgYyqvOmD2wBAkFkGAB6yMDUNyJcHbCJ/nycIYZ9RlQV1Z3wtbJCiBYD3BVL+AXTJAMBhgSaiAHIOmii4eu4zNDFhUPp34Xix2Uo36Q1wZBX3HCYTeAAAAABJRU5ErkJggg=="
                  alt=""
                >
                <img
                  v-if="j == 2" style="width: 32px;height: 32px;"
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgBAMAAACBVGfHAAAAKlBMVEUAAABwKM9wMM9yLtFyLdJxLdFyLtFxLtBwMM9xLtFyLs9xLdJ0LM9yLtF8/bHUAAAADXRSTlMAICDfv1/vzxDPcE9AyHB/wgAAAGZJREFUKM9jIAKI3IWBQojAXLjAFYjAXQQgXkCBgQmEgQJgc8ESdwUYGEECV+ACYACkcApAzICyQfpBGC4AA3gEFEDOQTUDCIaWGQ25l4ECtgiBA8GpQIG1CAELsGGsCCWXNxAT9wB5c+51+TZEoQAAAABJRU5ErkJggg=="
                  alt=""
                >
                <img
                  v-if="j == 3" style="width: 32px;height: 32px;"
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAPFBMVEUAAABQwhVRxBlRxBlSxBlRxBlRxBlTxBtRxRlSxBpRxBlQxxhQvxhSwxpSxRpQxBhRwhpSxBtRxxhSxBpHtlq3AAAAE3RSTlMAEI8/38+/vzDvgCAg739QoHBfK2xoZQAAALNJREFUOMu9kkkOwyAMRQ04zEna/vvftRJRatlCZZe3QnoWHulZQo0M7NHnuY74EQNZnIfCO+M3GDZn/d8IjwlefMAUqTQaU1obvdw+w3BSV1/YCip1Ho9DMmhPoynJUZRP8iXThfLcqOJGBbRWLn/CBlwVdcoFyNR3aVcVyZ0+eI8GpJyLAwN2lMnxZNhB1qNnku2oX1RVw8tlrde9Ppj1ya2PVghJdAo0I/tUAE410KN8AU9ZIuaixP0wAAAAAElFTkSuQmCC"
                  alt=""
                >
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="jcsb mt-24 flex">
        <div class="conLeft">
          <div class="card br-8 p-24">
            <div class="aic jcsb flex">
              <div>
                合规风险分布
              </div>
              <div style="width: 200px;">
                <el-select v-model="value_bm" class="m-2" placeholder="按部门" size="large">
                  <el-option v-for="item in options_bm" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </div>
            </div>
            <div class="mt-24">
              <div class="flex flex-wrap">
                <div
                  v-for="i, j in fxList" class="mr-8 mt-8" style="width: 19.5%;min-width: 130px;padding: 12px 16px;background: #fee2e2;
border-radius: 4px;
" :style="{ 'background-color': ['', '#DCFCE7', '#FEF9C3', '#FEE2E2'][i.status] }"
                >
                  <div class="f-14 fw-500">
                    {{ i.name }}
                  </div>
                  <div
                    class="f-12 mt-10 fw-500" style="line-height: 16px;"
                    :style="{ color: ['', '#16A34A', '#CA8A04', '#DC2626'][i.status] }"
                  >
                    {{ i.status_name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card br-8 mt-16 p-24">
            <div class="aic jcsb flex">
              <div class="f-18 fw-600">
                近期动态
              </div>
            </div>
            <div v-for="i, j in 4" :key="j" class="news mt-16" :style="{ borderBottom: j != 3 ? '1px solid #E5E7EB' : '' }">
              <div class="aic jcsb flex">
                <div>
                  <div class="f-16 fw-500">
                    完成季度合规检查
                  </div>
                  <div class="f-14 mt-10 c-[#6B7280] fw-400">
                    财务部 · 2小时前
                  </div>
                </div>
                <div>
                  <el-tag class="ml-2" type="" size="large" color="#DCFCE7">
                    <span class="f-14 c-[#16A34A]">已完成</span>
                  </el-tag>
                  <el-tag class="ml-2" type="" size="large" color="#DBEAFE">
                    <span class="f-14 c-[#2563EB]">进行中</span>
                  </el-tag>
                  <el-tag class="ml-2" type="" size="large" color="#F3F4F6">
                    <span class="f-14 c-[#4B5563]">待处理</span>
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="conRight">
          <el-card class="">
            <div class="f-18 fw-600">
              待办事项
            </div>
            <div class="aic mt-16 flex">
              <div style="width: 8px;height: 8px;background: #ef4444;border-radius: 50%;" />
              <div class="f-16 ml-12 fw-400">
                审核新供应商资质文件
              </div>
            </div>
            <div class="aic mt-16 flex">
              <div style="width: 8px;height: 8px;background: #ef4444;border-radius: 50%;" />
              <div class="f-16 ml-12 fw-400">
                更新部门合规手册
              </div>
            </div>
            <div class="aic mt-16 flex">
              <div style="width: 8px;height: 8px;background: #ef4444;border-radius: 50%;" />
              <div class="f-16 ml-12 fw-400">
                审核新供应商资质文件
              </div>
            </div>
            <div class="aic mt-16 flex">
              <div style="width: 8px;height: 8px;background: #ef4444;border-radius: 50%;" />
              <div class="f-16 ml-12 fw-400">
                审核新供应商资质文件
              </div>
            </div>
          </el-card>
          <el-card class="mt-12">
            <div class="f-18 fw-600">
              合规状态
            </div>
            <div class="mt-16">
              <div class="aic jcsb flex">
                <div class="f-14 fw-400">
                  整体合规率
                </div>
                <div class="f-14 fw-500">
                  92%
                </div>
              </div>
              <div class="mt-6">
                <el-progress :percentage="92" status="success" :show-text="false" />
              </div>
            </div>
            <div class="mt-16">
              <div class="aic jcsb flex">
                <div class="f-14 fw-400">
                  风险处理率
                </div>
                <div class="f-14 fw-500">
                  85%
                </div>
              </div>
              <div class="mt-6">
                <el-progress :percentage="85" status="warning" :show-text="false" />
              </div>
            </div>
            <div class="mt-16">
              <div class="aic jcsb flex">
                <div class="f-14 fw-400">
                  培训完成率
                </div>
                <div class="f-14 fw-500">
                  75%
                </div>
              </div>
              <div class="mt-6">
                <el-progress :percentage="75" status="format" :show-text="false" />
              </div>
            </div>
          </el-card>
          <el-card class="mt-12">
            <div class="f-18 fw-600">
              最新法规更新
            </div>
            <div class="mt-16">
              <div class="aic jcsb flex">
                <span class="f-16 fw-500">《数据安全管理规定》更新</span>
                <!-- <el-tag class="ml-2" type="info">一般</el-tag> -->
                <el-tag class="ml-2" type="danger">
                  重要
                </el-tag>
              </div>
              <div class="f-14 mt-6 c-['#6B7280']">
                2024-02-15
              </div>
            </div>
            <div class="mt-16">
              <div class="aic jcsb flex">
                <span class="f-16 fw-500">《反洗钱管理办法》修订</span>
                <el-tag class="ml-2" type="info">
                  一般
                </el-tag>
                <!-- <el-tag class="ml-2" type="danger">重要</el-tag> -->
              </div>
              <div class="f-14 mt-6 c-['#6B7280']">
                2024-02-15
              </div>
            </div>
            <div class="mt-16">
              <div class="aic jcsb flex">
                <span class="f-16 fw-500">《网络安全法》实施细则</span>
                <!-- <el-tag class="ml-2" type="info">一般</el-tag> -->
                <el-tag class="ml-2" type="danger">
                  重要
                </el-tag>
              </div>
              <div class="f-14 mt-6 c-['#6B7280']">
                2024-02-15
              </div>
            </div>
          </el-card>
        </div>
      </div>
      <!-- <ElTable :data="dataList" stripe highlight-current-row border height="100%">
        <ElTableColumn type="index" width="50" />
        <ElTableColumn prop="date" label="日期" width="180" />
        <ElTableColumn prop="name" label="姓名" width="180" />
        <ElTableColumn prop="address" label="地址" />
      </ElTable> -->
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }

  .conLeft {
    width: 64%;

    .srarch1 {
      width: 126px;
      height: 32px;
      padding: 0 12px;
      background: #efefef;
      border: 1px solid #e5e7eb;
      border-radius: 4px;
    }

    .news {
      width: 100%;
      height: 70px;

      // display: flex;
      // flex-direction: column;
      // justify-content: center;
    }
  }

  .conRight {
    width: 34.6%;
    // padding: 24px 24px;
    // background: #FFFFFF;
    // box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05), 0px 0px 0px 0px rgba(0, 0, 0, 0.00), 0px 0px 0px 0px rgba(0, 0, 0, 0.00);
    border-radius: 8px;
  }
</style>
