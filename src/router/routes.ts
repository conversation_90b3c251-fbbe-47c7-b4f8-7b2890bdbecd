import { setupLayouts } from 'virtual:meta-layouts'
import generatedRoutes from 'virtual:generated-pages'
import type { RouteRecordRaw } from 'vue-router'
// import MultilevelMenuExample from './modules/multilevel.menu.example'

import one1Menu from './modules/one/system'
import one2Menu from './modules/one/database'
import qa from './modules/one/qa'

import threeListManagement from './modules/prevention/threeListManagement'
import organizationManagement from './modules/prevention/organizationManagement'
import training from './modules/prevention/training'

import monitorCockpit from './modules/monitor/cockpit'
import monitorExamination from './modules/monitor/examination'
import monitorIntelligentReporting from './modules/monitor/intelligentReporting'

import respondViolationIssues from './modules/respond/violationIssues'
import respondAccountability from './modules/respond/accountability'
import respondImproveAndOptimize from './modules/respond/improveAndOptimize'

// 个人设置
import personalCenterPersonalInformation from './modules/personalCenter/personalInformation'
import personalCenterMessageNotification from './modules/personalCenter/messageNotification'
import personalCenterToDoList from './modules/personalCenter/toDoList'

// 系统设置
import systemSettingsEnterpriseInformation from './modules/systemSettings/enterpriseInformation'
import systemSettingsUsersAndPermissions from './modules/systemSettings/usersAndPermissions'
import systemSettingsUsersOrganizationalStructure from './modules/systemSettings/organizationalStructure'
import systemSettingsUsersSystemParameter from './modules/systemSettings/systemParameter'
import systemSettingsUsersIntegratedConfiguration from './modules/systemSettings/integratedConfiguration'
import systemSettingsUserslogQuery from './modules/systemSettings/logQuery'
import useSettingsStore from '@/store/modules/settings'
import { $t } from '@/locales'
import type { Route } from '#/global'

// 固定路由（默认路由）
const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login.vue'),
    meta: {
      whiteList: true,
      title: '登录',
      i18n: $t('route.login'),
    },
  },
  {
    path: '/:all(.*)*',
    name: 'notFound',
    component: () => import('@/views/[...all].vue'),
    meta: {
      title: '找不到页面',
    },
  },
]

// 系统路由
const systemRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/layouts/index.vue'),
    meta: {
      title: () => useSettingsStore().settings.home.title,
      breadcrumb: false,
    },
    children: [
      // // personalCenterPersonalInformation,
      {
        path: '',
        component: () => import('@/views/index.vue'),
        meta: {
          title: () => useSettingsStore().settings.home.title,
          i18n: $t('route.home'),
          icon: 'i-ant-design:home-twotone',
          breadcrumb: false,
        },
      },
      {
        path: 'reload',
        name: 'reload',
        component: () => import('@/views/reload.vue'),
        meta: {
          title: '重新加载',
          breadcrumb: false,
        },
      },
      {
        path: 'personal/notification',
        name: 'personalNotification',
        component: () => import('@/views/personal/notification.vue'),
        meta: {
          title: '通知中心',
          i18n: $t('route.personal.notification'),
        },
      },
      // {
      //   path: 'personalCenter/PersonalInformation',
      //   name: 'personalCenter/PersonalInformation',
      //   component: () => import('@/views/personal/notification.vue'),
      //   meta: {
      //     title: '通知中心',
      //     i18n: $t('route.personal.notification'),
      //   },
      // },

    ],
  },
]

// 动态路由（异步路由、导航栏路由）
const asyncRoutes: Route.recordMainRaw[] = [
  {
    meta: {
      title: '一体',
      // i18n: $t('route.ecology'),
      icon: 'ep:setting',
    },
    children: [
      one1Menu,
      one2Menu,
      qa,
    ],
  },
  {
    meta: {
      title: '预防之翼',
      // i18n: $t('route.ecology'),
      icon: 'ep:help',
    },
    children: [
      threeListManagement,
      organizationManagement,
      training,
    ],
  },
  {
    meta: {
      title: '监控之翼',
      // i18n: $t('route.ecology'),
      icon: 'i-ri:database-2-line',
    },
    children: [
      monitorCockpit,
      monitorExamination,
      monitorIntelligentReporting,
    ],
  },
  {
    meta: {
      title: '监控之翼',
      // i18n: $t('route.ecology'),
      icon: 'i-ri:database-2-line',
    },
    children: [
      respondViolationIssues,
      respondAccountability,
      respondImproveAndOptimize,
      // monitorExamination,
      // monitorIntelligentReporting,
    ],
  },
  {
    meta: {
      title: '个人中心',
      // i18n: $t('route.ecology'),
      icon: 'i-ri:database-2-line',
      // sidebar: false,
    },
    children: [
      personalCenterPersonalInformation,
      personalCenterMessageNotification,
      personalCenterToDoList,
    ],
  },
  {
    meta: {
      title: '系统设置',
      // i18n: $t('route.ecology'),
      icon: 'i-ri:database-2-line',
      // sidebar: false,
    },
    children: [
      systemSettingsEnterpriseInformation,
      systemSettingsUsersAndPermissions,
      systemSettingsUsersOrganizationalStructure,
      systemSettingsUsersSystemParameter,
      systemSettingsUsersIntegratedConfiguration,
      systemSettingsUserslogQuery,
    ],
  },

]

const constantRoutesByFilesystem = generatedRoutes.filter((item) => {
  return item.meta?.enabled !== false && item.meta?.constant === true
})

const asyncRoutesByFilesystem = setupLayouts(generatedRoutes.filter((item) => {
  return item.meta?.enabled !== false && item.meta?.constant !== true && item.meta?.layout !== false
}))

export {
  constantRoutes,
  systemRoutes,
  asyncRoutes,
  constantRoutesByFilesystem,
  asyncRoutesByFilesystem,
}
