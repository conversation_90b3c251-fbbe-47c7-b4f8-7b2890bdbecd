<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import useSettingsStore from '@/store/modules/settings'

const props = withDefaults(
  defineProps<{
    content?: string
    minHeight?: number
    maxHeight?: number
  }>(),
  {
    content: '',
    minHeight: 200,
    maxHeight: 800,
  },
)

const settingsStore = useSettingsStore()
const viewerRef = ref<HTMLElement>()

// 根据主题设置样式
const viewerClass = computed(() => {
  const isDark = settingsStore.settings.app.colorScheme === 'dark'
  return {
    'rich-text-viewer': true,
    'rich-text-viewer--dark': isDark,
    'rich-text-viewer--light': !isDark,
  }
})

// 处理富文本内容，确保样式正确
const processedContent = computed(() => {
  if (!props.content) { return '' }

  // 如果内容已经是HTML格式，直接返回
  if (props.content.includes('<') && props.content.includes('>')) {
    return props.content
  }

  // 如果是纯文本，转换为HTML格式
  return props.content
    .replace(/\n/g, '<br>')
    .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;')
    .replace(/ {2}/g, '&nbsp;&nbsp;')
})

onMounted(() => {
  // 确保内容区域的样式正确应用
  if (viewerRef.value) {
    viewerRef.value.style.minHeight = `${props.minHeight}px`
    viewerRef.value.style.maxHeight = `${props.maxHeight}px`
  }
})
</script>

<template>
  <div
    ref="viewerRef"
    :class="viewerClass"
    class="rich-text-viewer"
    v-html="processedContent"
  />
</template>

<style scoped>
.rich-text-viewer {
  @apply border rounded bg-white p-4 overflow-y-auto;
  line-height: 1.6;
  font-size: 16px;
  color: #333;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.rich-text-viewer--dark {
  @apply bg-gray-800 border-gray-600;
  color: #e5e5e5;
}

.rich-text-viewer--light {
  @apply bg-white border-gray-200;
  color: #333;
}

/* 富文本内容样式 */
:deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
  @apply font-semibold mb-3 mt-4;
}

:deep(h1) { font-size: 1.8em; }
:deep(h2) { font-size: 1.6em; }
:deep(h3) { font-size: 1.4em; }
:deep(h4) { font-size: 1.2em; }
:deep(h5) { font-size: 1.1em; }
:deep(h6) { font-size: 1em; }

:deep(p) {
  @apply mb-3;
  line-height: 1.6;
}

:deep(ul), :deep(ol) {
  @apply mb-3 pl-6;
}

:deep(li) {
  @apply mb-1;
}

:deep(blockquote) {
  @apply border-l-4 border-blue-500 pl-4 py-2 mb-3 bg-gray-50;
  font-style: italic;
}

.rich-text-viewer--dark :deep(blockquote) {
  @apply bg-gray-700 border-blue-400;
}

:deep(table) {
  @apply w-full border-collapse mb-3;
}

:deep(table th), :deep(table td) {
  @apply border border-gray-300 px-3 py-2 text-left;
}

.rich-text-viewer--dark :deep(table th),
.rich-text-viewer--dark :deep(table td) {
  @apply border-gray-600;
}

:deep(table th) {
  @apply bg-gray-100 font-semibold;
}

.rich-text-viewer--dark :deep(table th) {
  @apply bg-gray-700;
}

:deep(code) {
  @apply bg-gray-100 px-2 py-1 rounded text-sm;
  font-family: 'Courier New', monospace;
}

.rich-text-viewer--dark :deep(code) {
  @apply bg-gray-700;
}

:deep(pre) {
  @apply bg-gray-100 p-4 rounded mb-3 overflow-x-auto;
  font-family: 'Courier New', monospace;
}

.rich-text-viewer--dark :deep(pre) {
  @apply bg-gray-700;
}

:deep(img) {
  @apply max-w-full h-auto mb-3;
}

:deep(a) {
  @apply text-blue-600 underline;
}

.rich-text-viewer--dark :deep(a) {
  @apply text-blue-400;
}

:deep(strong), :deep(b) {
  @apply font-semibold;
}

:deep(em), :deep(i) {
  @apply italic;
}

:deep(hr) {
  @apply border-t border-gray-300 my-4;
}

.rich-text-viewer--dark :deep(hr) {
  @apply border-gray-600;
}
</style>
