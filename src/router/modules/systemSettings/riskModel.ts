import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/systemSettings/riskModel',
  component: Layout,
  name: '/systemSettings/riskModel',
  meta: {
    title: '风险模型',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/systemSettings/riskModel/riskModelManagement',
      name: '/systemSettings/riskModel/riskModelManagement',
      component: () => import('@/views/hegui/systemSettings/riskModel/riskModelManagement/index.vue'),
      meta: {
        title: '风险模型管理',
      },
      children: [
        {
          path: '/systemSettings/riskModel/riskModelManagement/addEdit',
          name: '/systemSettings/riskModel/riskModelManagement/addEdit',
          component: () => import('@/views/hegui/systemSettings/riskModel/riskModelManagement/addEdit.vue'),
          meta: {
            title: '风险模型',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/systemSettings/riskModel/riskModelManagement/detail',
          name: '/systemSettings/riskModel/riskModelManagement/detail',
          component: () => import('@/views/hegui/systemSettings/riskModel/riskModelManagement/detail.vue'),
          meta: {
            title: '风险模型详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
  ],
}

export default routes
