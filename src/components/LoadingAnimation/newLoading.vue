<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { onMounted, ref } from 'vue'

const progress = ref(0)
const estimatedTime = ref(5)
const showError = ref(false)
// Simulate progress
function simulateProgress() {
  const interval = setInterval(() => {
    if (progress.value < 100) {
      progress.value += 5
      estimatedTime.value = Math.max(0, estimatedTime.value - 0.25)
    }
    else {
      clearInterval(interval)
      // Simulate random error (10% chance)
      if (Math.random() < 0.1) {
        showError.value = true
      }
      else {
        // Normally here would close the modal
      }
    }
  }, 300)
}
function retry() {
  progress.value = 0
  estimatedTime.value = 5
  showError.value = false
  simulateProgress()
}
onMounted(() => {
  simulateProgress()
})
</script>

<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
    <div class="w-[450px] overflow-hidden rounded-lg bg-white shadow-xl">
      <div class="flex flex-col items-center p-6">
        <!-- Logo with rotation animation -->
        <div class="mb-6 animate-spin duration-2000">
          <img
            src="https://ai-public.mastergo.com/ai/img_res/a94debd90e9bb26dcd66f4721c5761fc.jpg"
            alt="Logo"
            class="h-16 w-16 rounded-full object-cover"
          >
        </div>
        <!-- Title -->
        <h3 class="mb-2 text-xl text-gray-800 font-semibold">
          正在处理中...
        </h3>
        <p class="mb-6 text-gray-600">
          正在进行智能分析，请稍候
        </p>
        <!-- Progress bar -->
        <div class="mb-4 w-full">
          <div class="mb-1 flex justify-between text-sm text-gray-500">
            <span>任务进度</span>
            <span>{{ progress }}%</span>
          </div>
          <div class="h-2 w-full overflow-hidden rounded-full bg-gray-200">
            <div
              class="h-full from-blue-400 to-purple-500 bg-gradient-to-r transition-all duration-300 ease-out"
              :style="{ width: `${progress}%` }"
            />
          </div>
        </div>
        <!-- Estimated time -->
        <p class="text-sm text-gray-500">
          预计剩余时间: {{ estimatedTime }}秒
        </p>
        <!-- Error message (hidden by default) -->
        <div
          v-if="showError"
          class="mt-4 w-full rounded bg-red-50 p-3 text-center text-sm text-red-600"
        >
          分析任务失败，请重试
          <button
            class="!rounded-button mt-2 whitespace-nowrap bg-red-500 px-4 py-1 text-sm text-white"
            @click="retry"
          >
            重试
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Custom animation duration for logo spin */
.animate-spin {
animation: spin 2s linear infinite;
}
@keyframes spin {
from {
transform: rotate(0deg);
}
to {
transform: rotate(360deg);
}
}
/* Smooth transition for progress bar */
.transition-all {
transition-property: all;
}
</style>
