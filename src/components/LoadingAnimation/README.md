# LoadingAnimation 加载动画组件

一个定制化的项目加载动画组件，使用项目 Logo 作为核心动画元素，提供丰富的配置选项和流畅的动画效果。

## 特性

- 🎨 **定制化设计** - 使用项目 Logo 作为加载动画核心
- 🔧 **灵活配置** - 支持多种尺寸、模式和样式配置
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🌙 **主题支持** - 支持明暗主题自动切换
- ⚡ **性能优化** - 流畅的 CSS 动画，低性能消耗
- 🎯 **TypeScript** - 完整的类型定义支持

## 安装使用

### 基础用法

```vue
<template>
  <div>
    <el-button @click="showLoading">显示加载</el-button>
    
    <LoadingAnimation
      :visible="loading"
      text="加载中..."
      size="medium"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LoadingAnimation from '@/components/LoadingAnimation/index.vue'

const loading = ref(false)

function showLoading() {
  loading.value = true
  
  // 模拟异步操作
  setTimeout(() => {
    loading.value = false
  }, 3000)
}
</script>
```

### 进度模式

```vue
<template>
  <LoadingAnimation
    :visible="loading"
    text="正在处理数据..."
    size="large"
    :show-progress="true"
    :progress="progress"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'

const loading = ref(false)
const progress = ref(0)

function startProgressLoading() {
  loading.value = true
  progress.value = 0
  
  const interval = setInterval(() => {
    progress.value += 10
    if (progress.value >= 100) {
      clearInterval(interval)
      loading.value = false
    }
  }, 300)
}
</script>
```

### 局部加载

```vue
<template>
  <div class="container">
    <div class="content">
      <!-- 你的内容 -->
      <p>这里是页面内容</p>
    </div>
    
    <LoadingAnimation
      :visible="loading"
      :fullscreen="false"
      text="局部加载中..."
      size="medium"
    />
  </div>
</template>

<style scoped>
.container {
  position: relative;
  min-height: 300px;
}
</style>
```

## API 文档

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `visible` | `boolean` | `false` | 是否显示加载动画 |
| `text` | `string` | `'加载中...'` | 加载提示文字 |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | 组件尺寸 |
| `fullscreen` | `boolean` | `true` | 是否全屏显示 |
| `showProgress` | `boolean` | `false` | 是否显示进度条 |
| `progress` | `number` | `0` | 进度值 (0-100) |
| `logoUrl` | `string` | `项目Logo地址` | 自定义Logo图片地址 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `imageError` | Logo图片加载失败时触发 | 无 |

### 尺寸规格

| 尺寸 | Logo大小 | 适用场景 |
|------|----------|----------|
| `small` | 48x48px | 小型组件、按钮加载 |
| `medium` | 64x64px | 常规页面加载 |
| `large` | 80x80px | 重要操作、首页加载 |

## 动画效果

组件包含以下动画效果：

1. **Logo旋转** - 360度连续旋转动画
2. **Logo缩放** - 轻微的缩放脉动效果
3. **光晕效果** - 围绕Logo的光晕脉动
4. **文字淡入淡出** - 加载文字的透明度变化
5. **进度条动画** - 进度条的渐变闪烁效果

## 样式定制

### CSS 变量

组件支持通过 CSS 变量进行样式定制：

```css
.loading-animation {
  --loading-bg-color: rgba(255, 255, 255, 0.9);
  --loading-text-color: #374151;
  --loading-primary-color: #3b82f6;
  --loading-glow-color: rgba(59, 130, 246, 0.3);
}
```

### 暗色主题

组件自动支持暗色主题，会根据系统设置自动切换：

```css
@media (prefers-color-scheme: dark) {
  .loading-mask {
    background: rgba(17, 24, 39, 0.9);
  }
  
  .loading-text {
    color: #f3f4f6;
  }
}
```

## 最佳实践

### 1. 合理使用全屏模式

```vue
<!-- 页面级加载使用全屏模式 -->
<LoadingAnimation :visible="pageLoading" :fullscreen="true" />

<!-- 组件级加载使用局部模式 -->
<LoadingAnimation :visible="componentLoading" :fullscreen="false" />
```

### 2. 进度反馈

对于耗时较长的操作，建议使用进度模式：

```vue
<LoadingAnimation
  :visible="uploading"
  :show-progress="true"
  :progress="uploadProgress"
  text="正在上传文件..."
/>
```

### 3. 错误处理

```vue
<LoadingAnimation
  :visible="loading"
  @image-error="handleLogoError"
/>

<script setup>
function handleLogoError() {
  console.warn('Logo加载失败，使用默认样式')
  // 可以设置备用Logo或显示文字加载
}
</script>
```

### 4. 性能优化

- 避免频繁切换 `visible` 状态
- 长时间显示时考虑添加取消功能
- 合理设置动画持续时间

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 55
- Safari >= 12
- Edge >= 79

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础加载动画
- 支持进度模式
- 支持局部/全屏模式
- 响应式设计
- 暗色主题支持

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个组件。

## 许可证

MIT License
