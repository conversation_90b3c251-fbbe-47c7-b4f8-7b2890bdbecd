import api from '@/api/index'

export default {
  // 学习中心
  learningCenter: {
    // 获取学习中心综合数据
    getLearningCenterData() {
      return api.get('/whiskerguardtrainingservice/api/learning/center')
    },
  },

  // 课程管理
  system(params: any, key: any) {
    switch (key) {
      case 'detail': // 获取课程详情
        return api.get(`/whiskerguardtrainingservice/api/course/infos/${params.id}`, {
          params,
        })
      case 'info': // 兼容旧版获取课程详情
        return api.get(`/whiskerguardtrainingservice/api/course/infos/${params.id}`, {
          params,
        })
      case 'create': // 创建课程
        return api.post(`/whiskerguardtrainingservice/api/course/infos`, params)
      case 'update': // 更新课程
        return api.put(`/whiskerguardtrainingservice/api/course/infos/${params.id}`, params)
      case 'delete': // 删除课程
        return api.delete(`/whiskerguardtrainingservice/api/course/infos/${params.id}`)
      default: // 分页查询课程
        return api.post(`/whiskerguardtrainingservice/api/course/infos/query`, params)
    }
  },

  // 课程学习相关接口
  learning: {
    // 获取课程详情
    getCourseDetail(id: string) {
      return api.get(`/whiskerguardtrainingservice/api/course/infos/${id}`)
    },

    // 根据课程ID获取课程章节和课时
    getCourseContentByCourseId(courseId: string) {
      return api.get(`/whiskerguardtrainingservice/api/course/chapters/course/${courseId}/with/lessons`)
    },

    // 获取课程内容列表
    getCourseList(params: any) {
      return api.post(`/whiskerguardtrainingservice/api/course/contents/query`, params)
    },

    // 获取课程章节详情
    getCourseContentDetail(params: any) {
      return api.post(`/whiskerguardtrainingservice/api/course/chapters/query`, params)
    },

    // 更新学习进度
    updateProgress(params: {
      userId: string
      courseId: string
      chapterId: string
      playbackPosition: number
      completionStatus?: string
    }) {
      const requestData: any = {
        userId: params.userId,
        courseId: params.courseId,
        chapterId: params.chapterId,
        playbackPosition: params.playbackPosition,
      }

      // 只有当completionStatus存在时才添加到请求中
      if (params.completionStatus) {
        requestData.completionStatus = params.completionStatus
      }

      return api.put(`/whiskerguardtrainingservice/api/learning/progresses/updateOrCreate`, requestData)
    },

    // 获取用户学习进度概览
    getLearningProgressOverview(params: any = {}) {
      return api.get(`/whiskerguardtrainingservice/api/learning/progress/statistics/overview`, {
        params,
      })
    },

    // 证书相关
    getCertificateList(params: any) {
      return api.post(`/whiskerguardtrainingservice/api/certificate/managements/query`, params)
    },

    // 获取证书详情
    getCertificateDetail(id: string) {
      return api.get(`/whiskerguardtrainingservice/api/certificate/managements/${id}`)
    },

    // 学习行为记录
    logBehavior(params: {
      userId: string
      courseId: number
      behaviorType: string
      durationSeconds?: number
    }) {
      return api.post('/whiskerguardtrainingservice/api/learning/behavior/logs/log', params)
    },

    // 更新学习进度统计
    updateProgressStatistics(userId: string, courseId: number) {
      return api.post(`/whiskerguardtrainingservice/api/learning/progress/statistics/course/${courseId}/update`, {
        userId,
      })
    },
    // 学习报告统计
    getReportStatistics(params: any) {
      return api.post(`/whiskerguardtrainingservice/api/training/reports/overview`, params)
    },
  },
}
