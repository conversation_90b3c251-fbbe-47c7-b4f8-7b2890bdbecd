<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, InfoFilled } from '@element-plus/icons-vue'
import assessApi from '@/api/complianceApi/prevention/assess'

// 路由相关
const route = useRoute()
const router = useRouter()

// 错题数据接口
interface WrongQuestion {
  questionId: string
  questionOrder: number
  questionContent: string
  questionType: string
  difficulty?: string
  optionA?: string
  optionB?: string
  optionC?: string
  optionD?: string
  userAnswer?: string
  correctAnswer: string
  questionAnalysis?: string
}

// 页面状态
const examRecordId = ref<string>('')
const wrongQuestions = ref<WrongQuestion[]>([])
const loading = ref(false)
const hasError = ref(false)

// 计算属性
const isEmpty = computed(() => wrongQuestions.value.length === 0 && !loading.value && !hasError.value)

// 返回上一页
function handleBack() {
  router.back()
}

// 获取难度等级样式
function getDifficultyType(difficulty?: string): 'success' | 'warning' | 'danger' | 'info' {
  switch (difficulty) {
    case '简单': return 'success'
    case '中等': return 'warning'
    case '困难': return 'danger'
    default: return 'info'
  }
}

// 获取选项样式类
function getOptionClass(option: string, question: WrongQuestion): string {
  const isUserAnswer = question.userAnswer?.includes(option)
  const isCorrectAnswer = question.correctAnswer?.includes(option)
  
  if (isCorrectAnswer) {
    return 'option-correct'
  }
  else if (isUserAnswer) {
    return 'option-wrong'
  }
  return ''
}

// 获取错题解析
async function loadWrongAnalysis() {
  if (!examRecordId.value) {
    ElMessage.error('缺少考试记录ID')
    return
  }
  
  loading.value = true
  hasError.value = false
  
  try {
    const response = await assessApi.wrongAnalysis(examRecordId.value)
    
    if (response && Array.isArray(response)) {
      wrongQuestions.value = response
    }
    else {
      wrongQuestions.value = []
    }
  }
  catch (error) {
    console.error('获取错题解析失败:', error)
    hasError.value = true
    ElMessage.error('获取错题解析失败')
  }
  finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  const recordId = route.query.examRecordId as string
  
  if (recordId) {
    examRecordId.value = recordId
    loadWrongAnalysis()
  }
  else {
    ElMessage.error('缺少考试记录ID')
  }
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="sticky top-0 z-10 bg-white shadow-sm">
      <div class="flex items-center px-6 py-4">
        <el-button
          :icon="ArrowLeft"
          circle
          @click="handleBack"
        />
        <h1 class="ml-4 text-xl font-semibold text-gray-800">
          错题解析
        </h1>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="p-6">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center py-20">
        <el-loading-spinner />
        <span class="ml-3 text-gray-600">正在加载错题解析...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="flex flex-col items-center justify-center py-20">
        <el-icon class="mb-4 text-6xl text-red-400">
          <InfoFilled />
        </el-icon>
        <p class="text-lg text-gray-600">
          加载错题解析失败
        </p>
        <el-button
          type="primary"
          class="mt-4"
          @click="loadWrongAnalysis"
        >
          重新加载
        </el-button>
      </div>

      <!-- 空状态 -->
      <div v-else-if="isEmpty" class="flex flex-col items-center justify-center py-20">
        <div class="mb-4 text-6xl text-green-400">
          🎉
        </div>
        <h3 class="mb-2 text-xl font-semibold text-green-600">
          恭喜！没有错题
        </h3>
        <p class="text-gray-600">
          您在本次考试中全部答对了
        </p>
      </div>

      <!-- 错题列表 -->
      <div v-else class="space-y-6">
        <el-card
          v-for="(question, index) in wrongQuestions"
          :key="question.questionId"
          class="question-card"
          shadow="hover"
        >
          <!-- 题目头部 -->
          <div class="mb-4 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
              第{{ question.questionOrder }}题
            </h3>
            <el-tag
              v-if="question.difficulty"
              :type="getDifficultyType(question.difficulty)"
              size="small"
            >
              {{ question.difficulty }}
            </el-tag>
          </div>

          <!-- 题目内容 -->
          <div class="mb-4 text-gray-700 leading-relaxed" v-html="question.questionContent" />

          <!-- 选项区域 -->
          <div class="mb-4 space-y-2">
            <div
              v-if="question.optionA"
              class="option-item"
              :class="getOptionClass('A', question)"
            >
              <span class="option-label">A.</span>
              <span class="option-text">{{ question.optionA }}</span>
            </div>
            <div
              v-if="question.optionB"
              class="option-item"
              :class="getOptionClass('B', question)"
            >
              <span class="option-label">B.</span>
              <span class="option-text">{{ question.optionB }}</span>
            </div>
            <div
              v-if="question.optionC"
              class="option-item"
              :class="getOptionClass('C', question)"
            >
              <span class="option-label">C.</span>
              <span class="option-text">{{ question.optionC }}</span>
            </div>
            <div
              v-if="question.optionD"
              class="option-item"
              :class="getOptionClass('D', question)"
            >
              <span class="option-label">D.</span>
              <span class="option-text">{{ question.optionD }}</span>
            </div>
          </div>

          <!-- 答案对比 -->
          <div class="mb-4 rounded-lg bg-gray-50 p-4">
            <div class="mb-2 flex items-center">
              <span class="mr-4 text-sm text-gray-600">您的答案：</span>
              <el-tag
                type="danger"
                size="small"
                effect="light"
              >
                {{ question.userAnswer || '未作答' }}
              </el-tag>
            </div>
            <div class="flex items-center">
              <span class="mr-4 text-sm text-gray-600">正确答案：</span>
              <el-tag
                type="success"
                size="small"
                effect="light"
              >
                {{ question.correctAnswer }}
              </el-tag>
            </div>
          </div>

          <!-- 题目解析 -->
          <div v-if="question.questionAnalysis" class="border-t border-gray-200 pt-4">
            <div class="mb-2 flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <InfoFilled />
              </el-icon>
              <span class="font-medium text-blue-600">题目解析</span>
            </div>
            <div
              class="rounded-lg bg-blue-50 p-3 text-sm text-gray-700 leading-relaxed"
              v-html="question.questionAnalysis"
            />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<style scoped>
.question-card {
  transition: all 0.3s ease;
}

.question-card:hover {
  transform: translateY(-2px);
}

.option-item {
  @apply flex items-start p-3 rounded-lg border-2 transition-all;
  border-color: #f0f0f0;
}

.option-label {
  @apply font-semibold mr-3 text-gray-600 flex-shrink-0;
}

.option-text {
  @apply flex-1 leading-relaxed;
}

.option-correct {
  @apply bg-green-50 border-green-200;
}

.option-correct .option-label,
.option-correct .option-text {
  @apply text-green-700 font-semibold;
}

.option-wrong {
  @apply bg-red-50 border-red-200;
}

.option-wrong .option-label,
.option-wrong .option-text {
  @apply text-red-700 font-semibold;
}
</style>