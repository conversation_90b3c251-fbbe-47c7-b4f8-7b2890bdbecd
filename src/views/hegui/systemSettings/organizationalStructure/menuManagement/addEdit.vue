<!-- 菜单权限新增/编辑 - 使用 TailwindCSS 和 ElementPlus -->
<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  ArrowLeft as ElIconArrowLeft,
  Check as ElIconCheck,
  Close as ElIconClose,
  Delete as ElIconDelete,
  InfoFilled as ElIconInfoFilled,
  Plus as ElIconPlus,
} from '@element-plus/icons-vue'
import menuApi from '@/api/permissions/menu'
import { allIconOptions } from '@/utils/elementPlusIcons'
import IconSelector from '@/components/IconSelector/index.vue'

const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref<FormInstance>()
const loading = ref(false)

// 页面类型和数据
const pageType = ref(route.query.type || 'add') // add | edit
const menuId = ref(route.query.id)
const parentId = ref(route.query.parentId || 0)

// 表单数据
const formData = reactive({
  // tenantId: 1,
  isAvailable: true,
  id: null,
  parentId: 0,
  path: '',
  redirect: '',
  component: '',
  name: '',
  activeMenu: '',
  mark: 1,
  sort: 1,
  is_menu: 1,
  meta: {
    title: '',
    icon: '',
    activeIcon: '',
    defaultOpened: false,
    permanent: false,
    auth: [],
    menu: true,
    breadcrumb: true,
    activeMenu: '',
    cache: [],
    noCache: [],
    badge: '',
    link: '',
    iframe: '',
    copyright: false,
    paddingBottom: '0px',
    sidebar: true,
  },
  auths: [],
})

// 权限项数据结构
const authItem = {
  id: null,
  name: '',
  value: '',
}

// 表单验证规则
const formRules: FormRules = {
  'meta.title': [
    { required: true, message: '请输入菜单名称', trigger: 'blur' },
  ],
  'path': [
    { required: false, message: '请输入菜单路径', trigger: 'blur' },
  ],
  'sort': [
    { required: false, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, message: '排序值不能小于0', trigger: 'blur' },
  ],
}

// 菜单类型选项
const menuTypeOptions = [
  { label: '目录', value: 'directory' },
  { label: '菜单', value: 'menu' },
  { label: '按钮', value: 'button' },
]

// 图标选项（使用动态导入的所有图标）
const iconOptions = allIconOptions

// 计算属性
const pageTitle = computed(() => {
  return pageType.value === 'add' ? '新增菜单' : '编辑菜单'
})

const isDirectory = computed(() => {
  return formData.meta.menu && (!formData.component || formData.component === 'Layout')
})

// 添加权限项
function addAuthItem() {
  formData.auths.push({ ...authItem, id: null })
}

// 删除权限项
function removeAuthItem(auth: any, index: number) {
  menuApi.BusinessPermissions(auth, 'delete')
  formData.auths.splice(index, 1)
}

// 获取菜单详情
async function getMenuDetail() {
  if (pageType.value === 'edit' && route.query.data) {
    try {
      // 从URL参数中获取节点数据
      const nodeData = JSON.parse(decodeURIComponent(route.query.data as string))

      // 回填基本数据
      formData.id = nodeData.id
      formData.parentId = nodeData.parentId || 0
      formData.path = nodeData.path || ''
      formData.redirect = nodeData.redirect || ''
      formData.component = nodeData.component || ''
      formData.name = nodeData.name || ''
      formData.activeMenu = nodeData.activeMenu || ''
      formData.mark = nodeData.mark !== undefined ? nodeData.mark : 1
      formData.sort = nodeData.sort || 1
      formData.is_menu = nodeData.is_menu !== undefined ? nodeData.is_menu : 1

      // 回填meta数据
      if (nodeData.meta) {
        const meta = typeof nodeData.meta === 'string' ? JSON.parse(nodeData.meta) : nodeData.meta
        formData.meta.title = meta.title || nodeData.name || ''
        formData.meta.icon = meta.icon || ''
        formData.meta.activeIcon = meta.activeIcon || ''
        formData.meta.defaultOpened = meta.defaultOpened || false
        formData.meta.permanent = meta.permanent || false
        formData.meta.auth = meta.auth || []
        formData.meta.menu = meta.menu !== undefined ? meta.menu : true
        formData.meta.breadcrumb = meta.breadcrumb !== undefined ? meta.breadcrumb : true
        formData.meta.activeMenu = meta.activeMenu || ''
        formData.meta.cache = meta.cache || []
        formData.meta.noCache = meta.noCache || []
        formData.meta.badge = meta.badge || ''
        formData.meta.link = meta.link || ''
        formData.meta.iframe = meta.iframe || ''
        formData.meta.copyright = meta.copyright || false
        formData.meta.paddingBottom = meta.paddingBottom || '0px'
        formData.meta.sidebar = meta.sidebar !== undefined ? meta.sidebar : true
      }

      // 回填权限数据
      if (nodeData.auths && Array.isArray(nodeData.auths)) {
        formData.auths = nodeData.auths.map(auth => ({
          id: auth.id,
          name: auth.name || '',
          value: auth.value || '',
        }))
      }

      ElMessage.success('数据加载成功')
    }
    catch (error) {
      ElMessage.error('数据解析失败')
      console.error(error)
    }
  }
}

// 提交表单
async function handleSubmit() {
  console.log('formData', formData)
  // if (!formRef.value) { return }

  try {
    // await formRef.value.validate()
    // 创建meta的深拷贝，避免循环引用问题
    const metaCopy = {
      title: formData.meta.title,
      icon: formData.meta.icon,
      activeIcon: formData.meta.activeIcon,
      defaultOpened: formData.meta.defaultOpened,
      permanent: formData.meta.permanent,
      auth: formData.meta.auth,
      menu: formData.meta.menu,
      breadcrumb: formData.meta.breadcrumb,
      activeMenu: formData.meta.activeMenu,
      cache: formData.meta.cache,
      noCache: formData.meta.noCache,
      badge: formData.meta.badge,
      link: formData.meta.link,
      iframe: formData.meta.iframe,
      copyright: formData.meta.copyright,
      paddingBottom: formData.meta.paddingBottom,
      sidebar: formData.meta.sidebar,
    }

    loading.value = true

    // 处理表单数据
    const submitData = {
      ...formData,
      meta: JSON.stringify(metaCopy),
      parentId: formData.parentId,
    }

    if (pageType.value === 'add') {
      // 新增菜单
      await menuApi.BusinessPermissions(submitData, 'create')
      ElMessage.success('新增菜单成功')
    }
    else {
      // 更新菜单
      await menuApi.BusinessPermissions(submitData, 'update')
      ElMessage.success('更新菜单成功')
    }

    // 返回列表页
    router.push('/systemSettings/organizationalStructure/menuManagement')
  }
  catch (error) {
    if (error !== false) { // 表单验证失败时error为false
      ElMessage.error(pageType.value === 'add' ? '新增菜单失败' : '更新菜单失败')
      console.error(error)
    }
  }
  finally {
    loading.value = false
  }
}

// 取消操作
function handleCancel() {
  router.back()
}

// 重置表单
function handleReset() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

onMounted(() => {
  if (parentId.value) {
    formData.parentId = Number(parentId.value)
  }
  getMenuDetail()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h1 class="text-xl text-gray-900 font-bold">
              {{ pageTitle }}
            </h1>
          </div>
          <div class="flex items-center space-x-2">
            <el-button @click="handleReset">
              重置
            </el-button>
            <el-button v-auth="'menuManagement/addEdit/save'" type="primary" :loading="loading" @click="handleSubmit">
              <el-icon class="mr-1">
                <ElIconCheck />
              </el-icon>
              {{ pageType === 'add' ? '创建' : '保存' }}
            </el-button>
          </div>
        </div>
      </template>
    </page-header>

    <PageMain style="background-color: transparent;">
      <div class="rounded-lg bg-white shadow-sm">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          class="p-20"
        >
          <!-- 基本信息 -->
          <div class="mb-8">
            <h3 class="mb-4 border-b pb-5 text-lg text-gray-900 font-semibold">
              基本信息
            </h3>
            <div class="grid grid-cols-1 gap-2 md:grid-cols-2">
              <el-form-item label="菜单名称" prop="meta.title" required>
                <el-input
                  v-model="formData.meta.title"
                  placeholder="请输入菜单名称"
                  clearable
                />
              </el-form-item>

              <el-form-item label="菜单路径" prop="path">
                <el-input
                  v-model="formData.path"
                  placeholder="请输入菜单路径，如：/system/user"
                  clearable
                >
                  <template #prepend>
                    /
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="路由名称" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入路由名称"
                  clearable
                />
              </el-form-item>

              <el-form-item label="组件路径" prop="component">
                <el-input
                  v-model="formData.component"
                  placeholder="请输入组件路径，如：system/user/index.vue"
                  clearable
                />
                <div class="mt-1 text-xs text-gray-500">
                  目录类型请填写 "Layout"，页面类型填写具体组件路径
                </div>
              </el-form-item>

              <el-form-item label="重定向" prop="redirect">
                <el-input
                  v-model="formData.redirect"
                  placeholder="请输入重定向路径"
                  clearable
                />
              </el-form-item>

              <el-form-item label="排序" prop="sort" required>
                <el-input-number
                  v-model="formData.sort"
                  :min="0"
                  :max="9999"
                  placeholder="排序值"
                  class="w-full"
                />
              </el-form-item>
            </div>
          </div>

          <!-- 显示配置 -->
          <div class="mb-8">
            <h3 class="mb-4 border-b pb-5 text-lg text-gray-900 font-semibold">
              显示配置
            </h3>
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
              <el-form-item label="菜单图标" prop="meta.icon">
                <IconSelector
                  v-model="formData.meta.icon"
                  placeholder="请选择菜单图标"
                />
              </el-form-item>

              <el-form-item label="激活图标" prop="meta.activeIcon">
                <IconSelector
                  v-model="formData.meta.activeIcon"
                  placeholder="请选择激活状态图标"
                />
              </el-form-item>

              <el-form-item label="徽章文本" prop="meta.badge">
                <el-input
                  v-model="formData.meta.badge"
                  placeholder="请输入徽章文本"
                  clearable
                />
              </el-form-item>

              <el-form-item label="外链地址" prop="meta.link">
                <el-input
                  v-model="formData.meta.link"
                  placeholder="请输入外链地址"
                  clearable
                />
              </el-form-item>
            </div>

            <!-- 开关配置 -->
            <h3 class="mb-4 border-b pb-5 text-lg text-gray-900 font-semibold">
              开关配置
            </h3>
            <div class="grid grid-cols-2 mt-6 gap-6 md:grid-cols-3">
              <el-form-item label="启用状态">
                <el-switch
                  v-model="formData.mark"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="启用"
                  inactive-text="禁用"
                />
              </el-form-item>

              <el-form-item label="显示菜单">
                <el-switch
                  v-model="formData.meta.menu"
                  active-text="显示"
                  inactive-text="隐藏"
                />
              </el-form-item>

              <el-form-item label="显示面包屑">
                <el-switch
                  v-model="formData.meta.breadcrumb"
                  active-text="显示"
                  inactive-text="隐藏"
                />
              </el-form-item>

              <el-form-item label="侧边栏显示">
                <el-switch
                  v-model="formData.meta.sidebar"
                  active-text="显示"
                  inactive-text="隐藏"
                />
              </el-form-item>

              <el-form-item label="默认展开">
                <el-switch
                  v-model="formData.meta.defaultOpened"
                  active-text="展开"
                  inactive-text="收起"
                />
              </el-form-item>

              <el-form-item label="固定菜单">
                <el-switch
                  v-model="formData.meta.permanent"
                  active-text="固定"
                  inactive-text="普通"
                />
              </el-form-item>

              <el-form-item label="版权信息">
                <el-switch
                  v-model="formData.meta.copyright"
                  active-text="显示"
                  inactive-text="隐藏"
                />
              </el-form-item>
            </div>
          </div>

          <!-- 权限配置 -->
          <div class="mb-8">
            <div class="mb-4 flex items-center justify-between">
              <h3 class="flex-1 border-b pb-5 text-lg text-gray-900 font-semibold">
                权限配置
              </h3>
              <el-button v-auth="'menuManagement/addEdit/addPermissions'" type="primary" size="small" @click="addAuthItem">
                <el-icon class="mr-1">
                  <ElIconPlus />
                </el-icon>
                添加权限
              </el-button>
            </div>

            <div v-if="formData.auths.length === 0" class="py-8 text-center text-gray-500">
              <el-icon size="48" class="mb-2">
                <ElIconInfoFilled />
              </el-icon>
              <p>暂无权限配置，点击上方按钮添加权限</p>
            </div>

            <div v-else class="space-y-4">
              <div
                v-for="(auth, index) in formData.auths"
                :key="auth.id || index"
                class="flex items-center rounded-lg bg-gray-50 p-4 space-x-4"
              >
                <div class="grid grid-cols-1 flex-1 gap-4 md:grid-cols-2">
                  <el-form-item
                    :prop="`auths.${index}.name`"
                    label="权限名称"
                    :rules="[{ required: true, message: '请输入权限名称', trigger: 'blur' }]"
                  >
                    <el-input
                      v-model="auth.name"
                      placeholder="请输入权限名称，如：新增用户"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item
                    :prop="`auths.${index}.value`"
                    label="权限标识"
                    :rules="[{ required: true, message: '请输入权限标识', trigger: 'blur' }]"
                  >
                    <el-input
                      v-model="auth.value"
                      placeholder="请输入权限标识，如：/system/user/add"
                      clearable
                    />
                  </el-form-item>
                </div>

                <el-button
                  type="danger"
                  size="small"
                  @click="removeAuthItem(auth, index)"
                >
                  <el-icon>
                    <ElIconDelete />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 缓存配置 -->
          <div class="mb-8">
            <h3 class="mb-4 border-b pb-5 text-lg text-gray-900 font-semibold">
              缓存配置
            </h3>
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
              <el-form-item label="缓存页面" prop="meta.cache">
                <el-select
                  v-model="formData.meta.cache"
                  multiple
                  placeholder="请选择需要缓存的页面"
                  class="w-full"
                >
                  <!-- 这里可以动态加载页面选项 -->
                </el-select>
                <div class="mt-1 text-xs text-gray-500">
                  选择需要缓存的页面组件名称
                </div>
              </el-form-item>

              <el-form-item label="不缓存页面" prop="meta.noCache">
                <el-select
                  v-model="formData.meta.noCache"
                  multiple
                  placeholder="请选择不需要缓存的页面"
                  class="w-full"
                >
                  <!-- 这里可以动态加载页面选项 -->
                </el-select>
                <div class="mt-1 text-xs text-gray-500">
                  选择不需要缓存的页面组件名称
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 高级配置 -->
          <div>
            <h3 class="mb-4 border-b pb-5 text-lg text-gray-900 font-semibold">
              高级配置
            </h3>
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
              <el-form-item label="激活菜单" prop="meta.activeMenu">
                <el-input
                  v-model="formData.meta.activeMenu"
                  placeholder="请输入激活菜单路径"
                  clearable
                />
                <div class="mt-1 text-xs text-gray-500">
                  用于设置当前页面激活的菜单项
                </div>
              </el-form-item>

              <el-form-item label="内边距" prop="meta.paddingBottom">
                <el-input
                  v-model="formData.meta.paddingBottom"
                  placeholder="请输入底部内边距，如：20px"
                  clearable
                />
              </el-form-item>

              <el-form-item label="iframe地址" prop="meta.iframe">
                <el-input
                  v-model="formData.meta.iframe"
                  placeholder="请输入iframe页面地址"
                  clearable
                />
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/toolsCss";

.absolute-container {
  @apply h-full flex flex-col;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-input-group__prepend) {
  background-color: #f9fafb;
  border-color: #d1d5db;
  color: #6b7280;
}
</style>
