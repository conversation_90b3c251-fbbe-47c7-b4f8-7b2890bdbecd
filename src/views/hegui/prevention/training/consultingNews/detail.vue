<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowDown, Document } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import systemApi from '@/api/curriculum/news'

const router = useRouter()
const route = useRoute()

// 新闻详情数据
const newsDetail = ref<any>({})
const loading = ref(false)
const newsId = ref<string | null>(null)

// 获取新闻详情
async function getNewsDetail() {
  if (!newsId.value) {
    return
  }

  try {
    loading.value = true
    const response = await systemApi.news({ id: newsId.value }, 'info')
    if (response) {
      newsDetail.value = response
    }
  }
  catch (error) {
    console.error('获取新闻详情失败:', error)
    ElMessage.error('获取新闻详情失败')
  }
  finally {
    loading.value = false
  }
}

// 返回列表
function goBack() {
  router.back()
}

// 获取状态标签类型
function getStatusType(status: string) {
  switch (status) {
    case 'PUBLISHED':
      return 'success'
    case 'DRAFT':
      return 'info'
    case 'REVIEW':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取状态文本
function getStatusText(status: string) {
  switch (status) {
    case 'PUBLISHED':
      return '已发布'
    case 'DRAFT':
      return '草稿'
    case 'REVIEW':
      return '审核中'
    default:
      return '未知'
  }
}

// 获取置顶状态类型
function getStickyStatusType() {
  if (!newsDetail.value.isSticky) {
    return 'info'
  }
  const now = new Date()
  const startTime = newsDetail.value.stickyStartTime ? new Date(newsDetail.value.stickyStartTime) : null
  const endTime = newsDetail.value.stickyEndTime ? new Date(newsDetail.value.stickyEndTime) : null

  if (startTime && now < startTime) {
    return 'warning'
  }
  if (endTime && now > endTime) {
    return 'danger'
  }
  return 'success'
}

// 获取置顶状态文本
function getStickyStatusText() {
  if (!newsDetail.value.isSticky) {
    return '未置顶'
  }
  const now = new Date()
  const startTime = newsDetail.value.stickyStartTime ? new Date(newsDetail.value.stickyStartTime) : null
  const endTime = newsDetail.value.stickyEndTime ? new Date(newsDetail.value.stickyEndTime) : null

  if (startTime && now < startTime) {
    return '待开始'
  }
  if (endTime && now > endTime) {
    return '已结束'
  }
  return '进行中'
}

// 下载附件
function downloadFile(attachment: any) {
  if (attachment.url) {
    window.open(attachment.url, '_blank')
  }
  else {
    ElMessage.warning('附件下载链接不存在')
  }
}

// 预览附件
function previewFile(attachment: any) {
  if (attachment.previewUrl) {
    window.open(attachment.previewUrl, '_blank')
  }
  else {
    ElMessage.warning('附件预览功能暂不支持此文件类型')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  const id = route.params.id || route.query.id
  if (id) {
    newsId.value = typeof id === 'string' ? id : id[0]
    getNewsDetail()
  }
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between" />
          <div class="flex space-x-3">
            <el-button v-debounce="2000" class="!rounded-button whitespace-nowrap" @click="goBack">
              返回
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-card class="!mx-0" shadow="hover">
          <div v-loading="loading" class="flex items-start justify-between">
            <div>
              <h2 class="mb-2 text-2xl text-gray-900 font-bold">
                {{ newsDetail.title || '资讯详情' }}
              </h2>
              <div class="mb-4 flex items-center space-x-3">
                <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800">{{ newsDetail.category?.name || '-' }}</span>
                <el-tag :type="getStatusType(newsDetail.status)" size="small">
                  {{ getStatusText(newsDetail.status) }}
                </el-tag>
                <span v-if="newsDetail.isSticky" class="rounded bg-orange-100 px-2 py-1 text-xs text-orange-800">置顶</span>
              </div>
            </div>
          </div>
          <div v-if="false" class="grid grid-cols-3 mt-6 gap-4">
            <div class="border-r pr-4">
              <h3 class="mb-2 text-sm text-gray-500 font-medium">
                发布信息
              </h3>
              <p class="text-sm text-gray-800">
                发布人：{{ newsDetail.createdBy || '-' }}
              </p>
              <p class="mt-1 text-sm text-gray-800">
                发布时间：{{ newsDetail.createdAt || '-' }}
              </p>
              <p class="mt-1 text-sm text-gray-800">
                最后修改：{{ newsDetail.updatedAt || '-' }}
              </p>
            </div>
            <div class="border-r pr-4">
              <h3 class="mb-2 text-sm text-gray-500 font-medium">
                阅读统计
              </h3>
              <p class="text-sm text-gray-800">
                总阅读量：{{ newsDetail.viewCount || 0 }}次
              </p>
              <p class="mt-1 text-sm text-gray-800">
                今日阅读：{{ newsDetail.todayReadCount || 0 }}次
              </p>
              <p class="mt-1 text-sm text-gray-800">
                平均阅读时长：{{ newsDetail.avgReadTime || '-' }}
              </p>
            </div>
            <div>
              <h3 class="mb-2 text-sm text-gray-500 font-medium">
                互动统计
              </h3>
              <p class="text-sm text-gray-800">
                评论数：{{ newsDetail.commentCount || 0 }}条
              </p>
              <p class="mt-1 text-sm text-gray-800">
                点赞数：{{ newsDetail.likeCount || 0 }}个
              </p>
              <p class="mt-1 text-sm text-gray-800">
                分享次数：{{ newsDetail.shareCount || 0 }}次
              </p>
            </div>
          </div>
        </el-card>
        <el-row :gutter="20" class="mt-20">
          <el-col :span="18">
            <el-card shadow="hover">
              <div class="mb-6">
                <h3 class="mb-2 text-sm text-gray-500 font-medium">
                  资讯摘要
                </h3>
                <div class="rounded bg-gray-50 p-4">
                  <p class="text-gray-700">
                    {{ newsDetail.summary || '暂无摘要' }}
                  </p>
                </div>
                <p class="mt-1 text-xs text-gray-400">
                  此部分内容将在移动端列表中显示
                </p>
              </div>
              <div class="mb-6">
                <h3 class="mb-4 text-lg text-gray-900 font-medium">
                  正文内容
                </h3>
                <div class="max-w-none prose" v-html="newsDetail.content || '暂无内容'" />
              </div>
              <!-- 附件信息 -->
              <div v-if="newsDetail.attachments && newsDetail.attachments.length > 0" class="mb-6">
                <h3 class="mb-4 text-lg text-gray-900 font-medium">
                  附件信息
                </h3>
                <div class="space-y-3">
                  <div v-for="attachment in newsDetail.attachments" :key="attachment.id" class="flex items-center justify-between rounded bg-gray-50 p-3">
                    <div class="flex items-center">
                      <el-icon class="mr-3 text-blue-600">
                        <Document />
                      </el-icon>
                      <div>
                        <div class="text-gray-900 font-medium">
                          {{ attachment.fileName || attachment.name }}
                        </div>
                        <div class="text-sm text-gray-500">
                          文件大小：{{ attachment.fileSize || '-' }} | 上传时间：{{ attachment.createdAt || '-' }}
                        </div>
                      </div>
                    </div>
                    <div class="flex space-x-2">
                      <el-button size="small" type="primary" @click="downloadFile(attachment)">
                        下载
                      </el-button>
                      <el-button size="small" @click="previewFile(attachment)">
                        预览
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <div v-loading="loading" class="space-y-6">
              <!-- 基本信息 -->
              <el-card shadow="hover">
                <template #header>
                  <div class="font-bold">
                    基本信息
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">资讯分类：</span>
                    <span class="font-medium">{{ newsDetail.category?.name || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">发布状态：</span>
                    <el-tag :type="getStatusType(newsDetail.status)" size="small">
                      {{ getStatusText(newsDetail.status) }}
                    </el-tag>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">排序权重：</span>
                    <span class="font-medium">{{ newsDetail.sortOrder || 0 }}</span>
                  </div>
                </div>
              </el-card>

              <!-- 发布信息 -->
              <el-card shadow="hover">
                <template #header>
                  <div class="font-bold">
                    发布信息
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">发布人：</span>
                    <span class="font-medium">{{ newsDetail.createdBy || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">发布时间：</span>
                    <span class="font-medium">{{ newsDetail.createdBy || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">最后修改：</span>
                    <span class="font-medium">{{ newsDetail.updatedAt || '-' }}</span>
                  </div>
                </div>
              </el-card>

              <!-- 置顶设置 -->
              <el-card shadow="hover">
                <template #header>
                  <div class="font-bold">
                    置顶设置
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">是否置顶：</span>
                    <el-tag :type="newsDetail.isSticky ? 'success' : 'info'" size="small">
                      {{ newsDetail.isSticky ? '是' : '否' }}
                    </el-tag>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">置顶状态：</span>
                    <el-tag :type="getStickyStatusType()" size="small">
                      {{ getStickyStatusText() }}
                    </el-tag>
                  </div>
                  <div v-if="newsDetail.isSticky">
                    <div class="mb-2 flex justify-between">
                      <span class="text-gray-600">开始时间：</span>
                      <span class="text-sm font-medium">{{ newsDetail.stickyStartTime || '-' }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">结束时间：</span>
                      <span class="text-sm font-medium">{{ newsDetail.stickyEndTime || '-' }}</span>
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- 阅读统计 -->
              <el-card v-if="false" shadow="hover">
                <template #header>
                  <div class="font-bold">
                    阅读统计
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">总阅读量：</span>
                    <span class="text-blue-600 font-medium">{{ newsDetail.readCount || 0 }}次</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">今日阅读：</span>
                    <span class="text-green-600 font-medium">{{ newsDetail.todayReadCount || 0 }}次</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">平均阅读时长：</span>
                    <span class="font-medium">{{ newsDetail.avgReadTime || '-' }}</span>
                  </div>
                </div>
              </el-card>

              <!-- 互动数据 -->
              <el-card v-if="false" shadow="hover">
                <template #header>
                  <div class="font-bold">
                    互动数据
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">评论数：</span>
                    <span class="font-medium">{{ newsDetail.commentCount || 0 }}条</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">点赞数：</span>
                    <span class="text-red-600 font-medium">{{ newsDetail.likeCount || 0 }}个</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">分享次数：</span>
                    <span class="font-medium">{{ newsDetail.shareCount || 0 }}次</span>
                  </div>
                </div>
              </el-card>
            </div>
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="font-bold">
                  推送效果
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <p class="text-sm text-gray-500">
                    推送范围
                  </p>
                  <p class="mt-1 text-sm text-gray-800">
                    全员推送
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    到达率
                  </p>
                  <div class="mt-1 flex items-center">
                    <div class="h-2.5 w-full rounded-full bg-gray-200">
                      <div class="h-2.5 rounded-full bg-green-500" style="width: 98.5%" />
                    </div>
                    <span class="ml-2 text-sm text-gray-600">98.5%</span>
                  </div>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    打开率
                  </p>
                  <div class="mt-1 flex items-center">
                    <div class="h-2.5 w-full rounded-full bg-gray-200">
                      <div class="h-2.5 rounded-full bg-blue-500" style="width: 78%" />
                    </div>
                    <span class="ml-2 text-sm text-gray-600">78.0%</span>
                  </div>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    及时性
                  </p>
                  <p class="mt-1 text-sm text-gray-800">
                    平均阅读延迟2小时30分钟
                  </p>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss.scss";
</style>

<style scoped>
  .prose {
    line-height: 1.75;
  }

  .prose p {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .prose h4 {
    font-weight: 600;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
  }
</style>
