<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowRight, Document, Picture, Search } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'
import { useRouter } from 'vue-router'
import curriculumApi from '@/api/curriculum'
import uploadApi from '@/api/mbbUpload'

// 响应式数据
const showModal = ref(false)
const searchTerm = ref('')
const cerObj = ref<any>({})
const certificateList = ref<any[]>([])
const loading = ref(false)
const certificateImageUrl = ref('')
const certificatePdfUrl = ref('')
const router = useRouter()

// 分页参数
const paging = ref({
  page: 1,
  size: 10,
  total: 0,
})

// 证书状态字典
const certificateStatusDict = ref<Record<string, string>>({
  1: '有效',
  2: '即将过期',
  3: '已过期',
  4: '已撤销',
})

// 获取证书列表
const getCertificateList = debounce(async () => {
  try {
    loading.value = true
    const params = {
      page: paging.value.page - 1,
      size: paging.value.size,
      searchTerm: searchTerm.value || null,
    }

    const res = await curriculumApi.learning.getCertificateList(params)
    if (res) {
      if (res.content && Array.isArray(res.content)) {
        certificateList.value = res.content
        paging.value.total = res.totalElements || 0
      }
      else if (Array.isArray(res)) {
        certificateList.value = res
        paging.value.total = res.length
      }
    }
  }
  catch (error) {
    console.error('获取证书列表失败:', error)
    ElMessage.error('获取证书列表失败')
  }
  finally {
    loading.value = false
  }
}, 300)

// 获取证书详情
async function getCertificateDetail(id: string) {
  try {
    const res = await curriculumApi.learning.getCertificateDetail(id)
    if (res) {
      cerObj.value = res

      // 获取证书预览图片真实地址
      if (res.certificatePreviewUrl) {
        await getCertificateImageUrl(res.certificatePreviewUrl)
      }

      // 获取证书PDF文件真实地址
      if (res.certificateFileUrl) {
        await getCertificatePdfUrl(res.certificateFileUrl)
      }

      showModal.value = true
    }
  }
  catch (error) {
    console.error('获取证书详情失败:', error)
    ElMessage.error('获取证书详情失败')
  }
}

// 获取证书预览图片真实地址
async function getCertificateImageUrl(key: string) {
  try {
    if (key) {
      const result = await uploadApi.getFileUrl(key)
      certificateImageUrl.value = result.data || result.url || result
    }
  }
  catch (error) {
    console.error('获取证书图片地址失败:', error)
    // 如果获取失败，使用原始地址作为备用
    certificateImageUrl.value = key
  }
}

// 获取证书PDF文件真实地址
async function getCertificatePdfUrl(key: string) {
  try {
    if (key) {
      const result = await uploadApi.getFileUrl(key)
      certificatePdfUrl.value = result.data || result.url || result
    }
  }
  catch (error) {
    console.error('获取证书PDF地址失败:', error)
    // 如果获取失败，使用原始地址作为备用
    certificatePdfUrl.value = key
  }
}

// 打开PDF预览
function openPdfPreview() {
  if (!certificatePdfUrl.value) {
    ElMessage.warning('PDF文件地址获取中，请稍后重试')
    return
  }

  // 在新窗口中打开PDF
  window.open(certificatePdfUrl.value, '_blank')
}

// 下载证书
function handleDownload() {
  if (!certificatePdfUrl.value) {
    ElMessage.warning('下载地址获取中，请稍后重试')
    return
  }

  // 创建下载链接
  const link = document.createElement('a')
  link.href = certificatePdfUrl.value
  link.download = `${cerObj.value.certificateName || '证书'}.pdf`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  ElMessage.success('开始下载证书')
}

// 获取证书状态显示文本
function getCertificateStatusText(status: string) {
  return certificateStatusDict.value[status] || status || '未知'
}

// 获取证书状态标签类型
function getCertificateStatusType(status: string) {
  const statusText = getCertificateStatusText(status)
  switch (statusText) {
    case '有效':
      return 'success'
    case '即将过期':
      return 'warning'
    case '已过期':
      return 'danger'
    case '已撤销':
      return 'info'
    default:
      return 'info'
  }
}

// 搜索处理
const handleSearch = debounce(() => {
  paging.value.page = 1
  getCertificateList()
}, 300)

// 分页处理
function handlePageChange(page: number) {
  paging.value.page = page
  getCertificateList()
}

function handleSizeChange(size: number) {
  paging.value.size = size
  paging.value.page = 1
  getCertificateList()
}

// 关闭弹窗
function handleClose() {
  showModal.value = false
  cerObj.value = {}
  certificateImageUrl.value = ''
  certificatePdfUrl.value = ''
}

// 全屏预览
function showFullPreview() {
  // Element Plus 的 el-image 组件已经内置了预览功能
}

// 组件挂载时获取数据
onMounted(() => {
  getCertificateList()
})
</script>

<template>
  <PageMain>
    <div class="certificate-container">
      <!-- 搜索栏 -->
      <div class="flex justify-between">
        <div class="search-bar">
          <!-- @input="handleSearch" -->
          <el-input
            v-model="searchTerm"
            placeholder="搜索证书名称"
            clearable
            style="width: 300px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div>
          <el-button type="primary" @click="handleSearch">
            查询
          </el-button>
          <el-button @click="router.back()">
            返回
          </el-button>
        </div>
      </div>

      <!-- 证书列表 -->
      <div v-loading="loading" class="certificate-list">
        <!-- 有数据时显示列表 -->
        <template v-if="certificateList.length > 0">
          <el-card
            v-for="(item, index) in certificateList"
            :key="index"
            class="certificate-item"
            shadow="hover"
            @click="getCertificateDetail(item.id)"
          >
            <div class="certificate-content">
              <div class="certificate-info">
                <h3 class="certificate-name">
                  {{ item.certificateName }}
                </h3>
                <p class="certificate-date">
                  颁发日期 {{ item.validFrom }} · 有效期至 {{ item.validTo }}
                </p>
                <div class="certificate-status">
                  <el-tag
                    :type="getCertificateStatusType(item.certificateStatus)"
                    size="small"
                  >
                    {{ getCertificateStatusText(item.certificateStatus) }}
                  </el-tag>
                </div>
              </div>
              <div class="certificate-action">
                <el-icon class="arrow-icon">
                  <ArrowRight />
                </el-icon>
              </div>
            </div>
          </el-card>
        </template>

        <!-- 空状态 -->
        <el-empty v-else-if="!loading" description="暂无证书数据" />
      </div>

      <!-- 分页 -->
      <div v-if="certificateList.length > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="paging.page"
          v-model:page-size="paging.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="paging.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>

      <!-- 证书详情弹窗 -->
      <el-dialog
        v-model="showModal"
        title="证书详情"
        width="800px"
        :before-close="handleClose"
      >
        <div class="certificate-detail">
          <!-- 基本信息 -->
          <div class="info-card">
            <h4 class="card-title">
              基本信息
            </h4>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">证书名称：</span>
                <span class="info-value">{{ cerObj.certificateName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">颁发机构：</span>
                <span class="info-value">{{ cerObj.issuingOrganization }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">颁发日期：</span>
                <span class="info-value">{{ cerObj.achievedAt }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">有效期：</span>
                <span class="info-value">{{ cerObj.validFrom }} ~ {{ cerObj.validTo }}</span>
              </div>
              <!-- <div class="info-item">
                <span class="info-label">状态：</span>
                <el-tag
                  :type="getCertificateStatusType(cerObj.certificateStatus)"
                  size="small"
                >
                  {{ getCertificateStatusText(cerObj.certificateStatus) }}
                </el-tag>
              </div> -->
            </div>
          </div>

          <!-- PDF文件 -->
          <div v-if="cerObj.certificateFileUrl" class="pdf-card">
            <h4 class="card-title">
              证书预览
            </h4>
            <div class="pdf-container">
              <div class="pdf-preview" @click="openPdfPreview">
                <el-icon class="pdf-icon" size="48">
                  <Document />
                </el-icon>
                <span class="pdf-text">点击查看PDF证书</span>
              </div>
            </div>
          </div>

          <!-- 证书描述 -->
          <div class="description-card">
            <h4 class="card-title">
              证书描述
            </h4>
            <div class="description-content">
              {{ cerObj.certificateDescription || '暂无描述信息' }}
            </div>
          </div>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleClose">
              关闭
            </el-button>
            <!-- <el-button v-if="cerObj.certificateFileUrl" type="primary" @click="handleDownload">
              下载证书
            </el-button> -->
          </div>
        </template>
      </el-dialog>
    </div>
  </PageMain>
</template>

<style lang="scss" scoped>
.certificate-container {
  padding: 20px;

  .search-bar {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
  }

  .certificate-list {
    margin-bottom: 20px;

    .certificate-item {
      margin-bottom: 16px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .certificate-content {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .certificate-info {
          flex: 1;

          .certificate-name {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 8px 0;
          }

          .certificate-date {
            font-size: 14px;
            color: #909399;
            margin: 0 0 8px 0;
          }

          .certificate-status {
            margin-top: 8px;
          }
        }

        .certificate-action {
          .arrow-icon {
            font-size: 16px;
            color: #C0C4CC;
          }
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}

.certificate-detail {
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #EBEEF5;
  }

  .info-card {
    margin-bottom: 24px;

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .info-item {
        display: flex;
        align-items: center;

        .info-label {
          font-weight: 500;
          color: #606266;
          min-width: 80px;
        }

        .info-value {
          color: #303133;
          flex: 1;
        }
      }
    }
  }

  .preview-card {
    margin-bottom: 24px;

    .preview-container {
      .preview-thumbnail {
        width: 100%;
        max-width: 400px;
        height: 300px;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        border: 1px solid #EBEEF5;

        .preview-image {
          width: 100%;
          height: 100%;
        }

        .image-slot {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #C0C4CC;

          .el-icon {
            font-size: 48px;
            margin-bottom: 8px;
          }
        }
      }

      .preview-hint {
        font-size: 12px;
        color: #909399;
        text-align: center;
        margin-top: 8px;
      }
    }
  }

  .pdf-card {
    margin-bottom: 24px;

    .pdf-container {
      .pdf-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 120px;
        border: 2px dashed #DCDFE6;
        border-radius: 8px;
        background-color: #FAFAFA;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409EFF;
          background-color: #F0F9FF;
        }

        .pdf-icon {
          color: #409EFF;
          margin-bottom: 8px;
        }

        .pdf-text {
          font-size: 14px;
          color: #606266;
          font-weight: 500;
        }
      }
    }
  }

  .description-card {
    .description-content {
      font-size: 14px;
      color: #606266;
      line-height: 1.6;
      padding: 16px;
      background-color: #F5F7FA;
      border-radius: 8px;
      min-height: 60px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
