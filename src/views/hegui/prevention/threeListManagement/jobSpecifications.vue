<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import {
  Document,
  Download,
  Plus,
  QuestionFilled,
  Search,
  Upload,
  Warning,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import threeListApi from '@/api/complianceApi/prevention/threeList'
import ImportComponent from '@/components/import/ImportV2.vue'

const router = useRouter()
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)
const loading = ref(false)
const departmentChart = ref<HTMLElement>()
const positionTypeChart = ref<HTMLElement>()
// 导入弹窗状态
const importDialogVisible = ref(false)

// 查询参数
const searchForm = reactive({
  page: 0,
  size: 10,
  orgUnitName: '',
  postName: '',
  employeeName: '',
  basicDuty: '',
  riskLevel: '',
  postStatus: '',
  approvalStatus: '',
})

// 搜索关键词
const searchKeyword = ref('')

// 表格数据
const tableData = ref<any[]>([])

// 风险等级映射
const riskLevelMap: Record<string, string> = {
  HIGH: '高风险',
  MEDIUM: '中风险',
  LOW: '低风险',
}

// 审批状态映射
const approvalStatusMap: Record<string, string> = {
  DRAFT: '草稿',
  PENDING: '待审批',
  APPROVED: '已通过',
  REJECTED: '已拒绝',
}

// 岗位状态映射
const postStatusMap: Record<string, string> = {
  ACTIVE: '在职',
  INACTIVE: '离职',
}

// 获取职责清单列表
async function getDutyList() {
  try {
    loading.value = true
    const startTime = Date.now()

    const params = {
      page: currentPage.value - 1, // 后端从0开始
      size: pageSize.value,
    }
    // 如果有搜索关键词，添加到相应字段进行模糊查询
    // if (searchKeyword.value) {
    //   params.postName = searchKeyword.value
    //   params.basicDuty = searchKeyword.value
    //   params.employeeName = searchKeyword.value
    // }
    const response = await threeListApi.getDutyMainList(params)
    if (response) {
      tableData.value = response.content || []
      totalItems.value = response.totalElements || 0
      // 更新统计数据
      updateStatistics()
    }

    // 确保loading至少显示500ms，让用户能看到loading效果
    const elapsedTime = Date.now() - startTime
    if (elapsedTime < 500) {
      await new Promise(resolve => setTimeout(resolve, 500 - elapsedTime))
    }
  }
  catch (error) {
    console.error('获取职责清单列表失败:', error)
    ElMessage.error('获取数据失败')
  }
  finally {
    loading.value = false
  }
}

// 显示导入弹窗
function showImportDialog() {
  importDialogVisible.value = true
}

// 导入成功回调
function handleImportSuccess(result: any) {
  ElMessage.success(`导入成功！共处理 ${result.totalRows} 行，成功 ${result.successRows} 行`)
  // 刷新列表
  getDutyList()
}

// 导入失败回调
function handleImportError(error: any) {
  console.error('导入失败:', error)
}

// 更新统计数据
function updateStatistics() {
  // 这里可以根据实际数据更新右侧统计卡片的数据
  // 暂时保持原有的模拟数据
}

// 搜索功能
function handleSearch() {
  currentPage.value = 1
  getDutyList()
}

// 重置搜索
function handleReset() {
  searchKeyword.value = ''
  searchForm.orgUnitName = ''
  searchForm.postName = ''
  searchForm.employeeName = ''
  searchForm.basicDuty = ''
  searchForm.riskLevel = ''
  searchForm.postStatus = ''
  searchForm.approvalStatus = ''
  currentPage.value = 1
  getDutyList()
}

// 分页变化
function handlePageChange() {
  getDutyList()
}

function initDepartmentChart() {
  if (!departmentChart.value) {
    return
  }
  const chart = echarts.init(departmentChart.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: ['财务部', '采购部', '法务部', '人力资源部', 'IT部', '市场部', '销售部', '生产部', '质量部', '行政部'],
    },
    series: [
      {
        name: '部门分布',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 12, name: '财务部' },
          { value: 10, name: '采购部' },
          { value: 8, name: '法务部' },
          { value: 15, name: '人力资源部' },
          { value: 5, name: 'IT部' },
          { value: 10, name: '市场部' },
          { value: 12, name: '销售部' },
          { value: 8, name: '生产部' },
          { value: 4, name: '质量部' },
          { value: 2, name: '行政部' },
        ],
      },
    ],
  }
  chart.setOption(option)
}
function initPositionTypeChart() {
  if (!positionTypeChart.value) {
    return
  }
  const chart = echarts.init(positionTypeChart.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
    },
    yAxis: {
      type: 'category',
      data: ['管理岗', '专业岗', '操作岗', '服务岗', '其他'],
    },
    series: [
      {
        name: '岗位数量',
        type: 'bar',
        data: [25, 30, 15, 10, 6],
        itemStyle: {
          color(params: any) {
            const colorList = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE']
            return colorList[params.dataIndex]
          },
        },
      },
    ],
  }
  chart.setOption(option)
}
onMounted(() => {
  getDutyList()
  nextTick(() => {
    initDepartmentChart()
    initPositionTypeChart()
  })
})
function goAddEdit(item: any) {
  if (item?.dutyMainId) {
    // 编辑清单
    router.push({
      name: '/threeListManagement/jobSpecifications/edit',
      query: { id: item.dutyMainId },
    })
  }
  else {
    // 新增清单
    router.push({
      name: '/threeListManagement/jobSpecifications/edit',
    })
  }
}
// 查看职责清单详情
function goDetail(row: any) {
  router.push({
    name: '/threeListManagement/jobSpecifications/detail',
    query: { id: row.dutyMainId },
  })
}

// 格式化时间
function formatTime(instant: any) {
  if (!instant) {
    return '-'
  }
  try {
    if (typeof instant === 'object' && instant.seconds) {
      return new Date(instant.seconds * 1000).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
    }
    return new Date(instant).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }
  catch (error) {
    return '-'
  }
}

// 导出Excel
async function handleExport() {
  try {
    loading.value = true
    const params: any = {
      page: 0,
      size: 999999,
      sort: 'createdAt',
      direction: 'desc',
    }

    // 添加搜索条件
    if (searchForm.orgUnitName) {
      params.orgUnitName = searchForm.orgUnitName
    }
    if (searchKeyword.value) {
      params.postName = searchKeyword.value
      params.employeeName = searchKeyword.value
      params.basicDuty = searchKeyword.value
    }
    if (searchForm.riskLevel) {
      params.riskLevel = searchForm.riskLevel
    }
    if (searchForm.postStatus) {
      params.postStatus = searchForm.postStatus
    }
    if (searchForm.approvalStatus) {
      params.approvalStatus = searchForm.approvalStatus
    }

    const response = await threeListApi.exportDutyPositionList(params)

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 生成文件名
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_')
    link.download = `重点岗位职责清单_${timestamp}.xlsx`

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  }
  catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="w-full">
    <!-- 主内容区 -->
    <div class="mx-auto w-full px-4 py-6">
      <!-- 标题和操作区 -->
      <div class="mb-6 flex items-center justify-between">
        <h1 class="text-xl font-bold">
          重点岗位合规职责清单
        </h1>
        <div class="flex space-x-3">
          <el-button v-debounce="1000" v-auth="'threeListManagement/jobSpecifications/add'" type="primary" class="!rounded-button whitespace-nowrap" @click="goAddEdit">
            <!-- <el-icon class="mr-1">
              <Plus />
            </el-icon> -->
            新增岗位职责
          </el-button>
          <el-button v-debounce="1000" v-auth="'threeListManagement/jobSpecifications/import'" type="success" class="!rounded-button whitespace-nowrap" @click="showImportDialog">
            <!-- <el-icon class="mr-1">
              <Upload />
            </el-icon> -->
            批量导入
          </el-button>
          <el-button v-debounce="1000" v-auth="'threeListManagement/jobSpecifications/export'" type="warning" class="!rounded-button whitespace-nowrap" @click="handleExport">
            <!-- <el-icon class="mr-1">
              <Download />
            </el-icon> -->
            导出Excel
          </el-button>
        </div>
      </div>
      <!-- 搜索和筛选区 -->
      <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
        <div class="flex flex-wrap items-center gap-3">
          <div class="relative w-64">
            <el-input
              v-model="searchKeyword"
              type="text"
              placeholder="搜索岗位名称、职责..."
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon class="text-gray-400">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
          <el-select v-model="searchForm.riskLevel" placeholder="风险等级" clearable class="w-40" @change="handleSearch">
            <el-option label="高风险" value="HIGH" />
            <el-option label="中风险" value="MEDIUM" />
            <el-option label="低风险" value="LOW" />
          </el-select>
          <el-select v-model="searchForm.approvalStatus" placeholder="审批状态" clearable class="w-40" @change="handleSearch">
            <el-option label="草稿" value="DRAFT" />
            <el-option label="待审批" value="PENDING" />
            <el-option label="已通过" value="APPROVED" />
            <el-option label="已拒绝" value="REJECTED" />
          </el-select>
          <el-button v-debounce="1000" type="primary" @click="handleSearch">
            搜索
          </el-button>
          <el-button v-debounce="1000" @click="handleReset">
            重置
          </el-button>
        </div>
      </div>

      <!-- 导入弹窗 -->
      <ImportComponent
        v-model:visible="importDialogVisible"
        title="重点岗位职责清单导入"
        :download-template-api="threeListApi.downloadDutyPositionTemplate"
        :import-data-api="threeListApi.importDutyPositionList"
        template-file-name="重点岗位职责清单导入模板.xlsx"
        @success="handleImportSuccess"
        @error="handleImportError"
      />

      <!-- 主内容布局 -->
      <div class="flex space-x-6">
        <!-- 左侧列表区 -->
        <div class="flex-1 overflow-x-auto">
          <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
            :row-style="{ height: '60px' }"
          >
            <el-table-column type="selection" width="50" />
            <el-table-column prop="orgUnitName" label="部门" width="120" />
            <el-table-column prop="postName" label="岗位名称" width="120">
              <template #default="{ row }">
                <span>{{ row.postName }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="employeeName" label="任职人员" width="100" /> -->
            <el-table-column prop="basicDuty" label="基本职责" min-width="200" show-overflow-tooltip />
            <el-table-column prop="riskLevel" label="风险等级" width="90">
              <template #default="{ row }">
                <el-tag
                  v-if="row.riskControlInfo?.riskLevel"
                  size="small"
                  :type="
                    row.riskControlInfo.riskLevel === 'HIGH'
                      ? 'danger'
                      : row.riskControlInfo.riskLevel === 'MEDIUM'
                        ? 'warning'
                        : 'success'
                  "
                >
                  {{ riskLevelMap[row.riskControlInfo.riskLevel] || row.riskControlInfo.riskLevel }}
                </el-tag>
                <span v-else class="text-sm text-gray-400">未评估</span>
              </template>
            </el-table-column>
            <el-table-column prop="postStatus" label="岗位状态" width="90">
              <template #default="{ row }">
                <el-tag
                  v-if="row.postStatus"
                  :type="row.postStatus === 'ACTIVE' ? 'success' : 'info'"
                >
                  {{ postStatusMap[row.postStatus] || row.postStatus }}
                </el-tag>
                <span v-else class="text-sm text-gray-400">未设置</span>
              </template>
            </el-table-column>
            <el-table-column prop="approvalStatus" label="审批状态" width="90">
              <template #default="{ row }">
                <el-tag
                  :type="
                    row.approvalStatus === 'APPROVED'
                      ? 'success'
                      : row.approvalStatus === 'PENDING'
                        ? 'warning'
                        : row.approvalStatus === 'REJECTED'
                          ? 'danger'
                          : 'info'
                  "
                >
                  {{ approvalStatusMap[row.approvalStatus] || row.approvalStatus }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" width="120">
              <template #default="{ row }">
                {{ formatTime(row.createdAt) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="260" fixed="right">
              <template #default="{ row }">
                <div class="flex items-center gap-0.5">
                  <el-button v-debounce="1000" v-auth="'threeListManagement/jobSpecifications/view'" size="small" type="primary" plain @click="goDetail(row)">
                    查看
                  </el-button>
                  <el-button v-debounce="1000" v-auth="'threeListManagement/jobSpecifications/edit'" size="small" type="warning" plain @click="goAddEdit(row)">
                    编辑
                  </el-button>
                  <el-button v-debounce="1000" v-auth="'threeListManagement/jobSpecifications/confirm'" size="small" type="success" plain>
                    确认
                  </el-button>
                  <el-button v-debounce="1000" v-auth="'threeListManagement/jobSpecifications/delete'" size="small" type="danger" plain>
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="mt-4 flex justify-between">
            <div class="text-sm text-gray-500">
              共 {{ totalItems }} 条记录
            </div>
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="totalItems"
              layout="total, sizes, prev, pager, next, jumper"
              @current-change="handlePageChange"
              @size-change="handlePageChange"
            />
          </div>
        </div>
        <!-- 右侧统计区 -->
        <div v-if="false" class="w-80 shrink-0">
          <!-- 岗位概览卡片 -->
          <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
            <h3 class="mb-4 text-base font-bold">
              岗位概览
            </h3>
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-gray-600">总岗位数</span>
                <span class="text-2xl font-bold">86</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">高风险岗位</span>
                <span class="text-xl text-red-500 font-bold">12</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">中风险岗位</span>
                <span class="text-xl text-yellow-500 font-bold">34</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">低风险岗位</span>
                <span class="text-xl text-green-500 font-bold">40</span>
              </div>
            </div>
          </div>
          <!-- 部门岗位分布卡片 -->
          <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
            <h3 class="mb-4 text-base font-bold">
              部门岗位分布
            </h3>
            <div ref="departmentChart" class="h-60" />
          </div>
          <!-- 岗位类型分布卡片 -->
          <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
            <h3 class="mb-4 text-base font-bold">
              岗位类型分布
            </h3>
            <div ref="positionTypeChart" class="h-60" />
          </div>
          <!-- 快捷操作卡片 -->
          <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
            <h3 class="mb-4 text-base font-bold">
              快捷操作
            </h3>
            <div class="space-y-2">
              <a href="#" class="flex items-center text-sm text-gray-700 hover:underline">
                <el-icon class="mr-2"><Warning /></el-icon>
                重点岗位合规职责风险评估
              </a>
              <a href="#" class="flex items-center text-sm text-gray-700 hover:underline">
                <el-icon class="mr-2"><Document /></el-icon>
                职责合规分析
              </a>
              <a href="#" class="flex items-center text-sm text-gray-700 hover:underline">
                <el-icon class="mr-2"><QuestionFilled /></el-icon>
                帮助文档
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

  <style scoped>
  .el-table {
  --el-table-border-color: #f0f0f0;
  --el-table-header-bg-color: #fafafa;
  position: relative;
  }
  .el-table :deep(.el-table__fixed-right) {
  background-color: white;
  }
  .el-table :deep(.el-table__row:hover) {
  --el-table-tr-bg-color: #f0f5ff;
  }
  .el-pagination {
  --el-pagination-bg-color: transparent;
  }
  .el-tag {
  --el-tag-border-radius: 4px;
  }
  .el-select {
  --el-select-border-color-hover: #d9d9d9;
  }
  .el-input {
  --el-input-border-radius: 4px;
  }
  </style>
