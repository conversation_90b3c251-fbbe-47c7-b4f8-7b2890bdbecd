<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { reactive, ref } from 'vue'
import {
  Document as ElIconDocument,
  Files as ElIconFiles,
  Notebook as ElIconNotebook,
  OfficeBuilding as ElIconOfficeBuilding,
  Refresh as ElIconRefresh,
  Upload as ElIconUpload,
  User as ElIconUser,
} from '@element-plus/icons-vue'

const activeTab = ref('import')

// Import Dialog
const importDialog = reactive({
  visible: false,
  type: '',
  step: 1,
  strategy: 'create',
  conflict: 'skip',
  notify: true,
  fileName: '',
})

function showImportDialog(type: string) {
  importDialog.visible = true
  importDialog.type = type
  importDialog.step = 1
  importDialog.fileName = ''
}

function handleFileChange(file: any) {
  importDialog.fileName = file.name
}

function startImport() {
  importDialog.visible = false
  // 这里添加实际导入逻辑
}

// Export Dialog
const exportDialog = reactive({
  visible: false,
  type: '',
  scope: 'all',
  filter: '',
  fields: ['field1', 'field2', 'field3'],
  format: 'excel',
  fileName: '',
})

function showExportDialog(type: string) {
  exportDialog.visible = true
  exportDialog.type = type
  exportDialog.fileName = `${type}_${new Date().toISOString().slice(0, 10)}`
}

function startExport() {
  exportDialog.visible = false
  // 这里添加实际导出逻辑
}

// History Data
const filter = reactive({
  type: 'all',
  dataType: 'all',
  dateRange: [],
  status: 'all',
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 85,
})

const historyData = [
  { id: 'IMP20230001', type: '导入', dataType: '用户数据', fileName: 'user_data_2023.xlsx', operator: '张伟', time: '2023-06-15 09:30', status: '成功' },
  { id: 'EXP20230002', type: '导出', dataType: '组织数据', fileName: 'org_data_2023.csv', operator: '李娜', time: '2023-06-15 10:15', status: '成功' },
  { id: 'IMP20230003', type: '导入', dataType: '合规制度', fileName: 'compliance_policy_2023.xlsx', operator: '王芳', time: '2023-06-14 14:20', status: '失败' },
  { id: 'EXP20230004', type: '导出', dataType: '合规记录', fileName: 'compliance_records_2023.json', operator: '赵明', time: '2023-06-14 16:45', status: '成功' },
  { id: 'IMP20230005', type: '导入', dataType: '用户数据', fileName: 'new_users_2023.xlsx', operator: '张伟', time: '2023-06-13 11:10', status: '成功' },
  { id: 'EXP20230006', type: '导出', dataType: '组织数据', fileName: 'org_structure_2023.csv', operator: '李娜', time: '2023-06-12 15:30', status: '成功' },
  { id: 'IMP20230007', type: '导入', dataType: '合规制度', fileName: 'updated_policies_2023.xlsx', operator: '王芳', time: '2023-06-12 10:20', status: '成功' },
  { id: 'EXP20230008', type: '导出', dataType: '合规记录', fileName: 'audit_logs_2023.json', operator: '赵明', time: '2023-06-11 14:15', status: '进行中' },
  { id: 'IMP20230009', type: '导入', dataType: '用户数据', fileName: 'user_updates_2023.xlsx', operator: '张伟', time: '2023-06-10 09:45', status: '成功' },
  { id: 'EXP20230010', type: '导出', dataType: '组织数据', fileName: 'department_list_2023.csv', operator: '李娜', time: '2023-06-09 16:30', status: '成功' },
]

const previewData = [
  { field1: '数据1', field2: '数据2', field3: '数据3' },
  { field1: '数据4', field2: '数据5', field3: '数据6' },
  { field1: '数据7', field2: '数据8', field3: '数据9' },
  { field1: '数据10', field2: '数据11', field3: '数据12' },
  { field1: '数据13', field2: '数据14', field3: '数据15' },
]

const recentActivities = [
  { operator: '张伟', time: '10分钟前', action: '导入用户数据 (user_data_2023.xlsx)', status: '成功' },
  { operator: '李娜', time: '25分钟前', action: '导出组织数据 (org_structure_2023.csv)', status: '成功' },
  { operator: '王芳', time: '1小时前', action: '导入合规制度 (compliance_policy_2023.xlsx)', status: '失败' },
  { operator: '赵明', time: '2小时前', action: '导出合规记录 (audit_logs_2023.json)', status: '进行中' },
  { operator: '张伟', time: '3小时前', action: '导入用户数据 (new_users_2023.xlsx)', status: '成功' },
]

function getStatusTagType(status: string) {
  switch (status) {
    case '成功': return 'success'
    case '失败': return 'danger'
    case '进行中': return 'primary'
    default: return 'info'
  }
}

function viewDetail(row: any) {
  // 查看详情逻辑
}

function downloadFile(row: any) {
  // 下载文件逻辑
}

function deleteRecord(row: any) {
  // 删除记录逻辑
}
</script>

<template>
  <div class="absolute-container" style="">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              数据导入导出
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="space-x-3">
            <el-button class="!rounded-button whitespace-nowrap" type="primary" plain>
              <el-icon class="mr-1">
                <ElIconRefresh />
              </el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <el-tabs v-model="activeTab" class="rounded-md bg-white shadow-sm">
                <el-tab-pane label="数据导入" name="import">
                  <div class="grid grid-cols-2 gap-4 p-4">
                    <!-- User Import Card -->
                    <div class="border border-gray-200 rounded-md bg-white p-4 transition-shadow hover:shadow-md">
                      <div class="flex items-start">
                        <div class="mr-4 rounded-full bg-blue-100 p-3">
                          <el-icon class="text-xl text-blue-600">
                            <ElIconUser />
                          </el-icon>
                        </div>
                        <div class="flex-1">
                          <h3 class="text-gray-900 font-medium">
                            用户数据导入
                          </h3>
                          <p class="mt-1 text-sm text-gray-500">
                            导入用户信息，支持批量创建和更新用户
                          </p>
                          <div class="mt-4 flex space-x-2">
                            <button
                              v-auth="'dataImportAndExport/index/downloadTemplate'"
                              class="!rounded-button whitespace-nowrap border border-gray-300 px-3 py-1 text-sm text-gray-700 font-medium hover:bg-gray-50"
                            >
                              下载模板
                            </button>
                            <button
                              v-auth="'dataImportAndExport/index/importData'"
                              class="!rounded-button whitespace-nowrap bg-blue-600 px-3 py-1 text-sm text-white font-medium hover:bg-blue-700" @click="showImportDialog('user')"
                            >
                              导入数据
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Organization Import Card -->
                    <div class="border border-gray-200 rounded-md bg-white p-4 transition-shadow hover:shadow-md">
                      <div class="flex items-start">
                        <div class="mr-4 rounded-full bg-green-100 p-3">
                          <el-icon class="text-xl text-green-600">
                            <ElIconOfficeBuilding />
                          </el-icon>
                        </div>
                        <div class="flex-1">
                          <h3 class="text-gray-900 font-medium">
                            组织数据导入
                          </h3>
                          <p class="mt-1 text-sm text-gray-500">
                            导入组织架构信息，支持多层级组织
                          </p>
                          <div class="mt-4 flex space-x-2">
                            <button
                              v-auth="'dataImportAndExport/index/downloadTemplate'"
                              class="!rounded-button whitespace-nowrap border border-gray-300 px-3 py-1 text-sm text-gray-700 font-medium hover:bg-gray-50"
                            >
                              下载模板
                            </button>
                            <button
                              v-auth="'dataImportAndExport/index/importData'"
                              class="!rounded-button whitespace-nowrap bg-blue-600 px-3 py-1 text-sm text-white font-medium hover:bg-blue-700" @click="showImportDialog('organization')"
                            >
                              导入数据
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Compliance Import Card -->
                    <div class="border border-gray-200 rounded-md bg-white p-4 transition-shadow hover:shadow-md">
                      <div class="flex items-start">
                        <div class="mr-4 rounded-full bg-purple-100 p-3">
                          <el-icon class="text-xl text-purple-600">
                            <ElIconDocument />
                          </el-icon>
                        </div>
                        <div class="flex-1">
                          <h3 class="text-gray-900 font-medium">
                            合规制度导入
                          </h3>
                          <p class="mt-1 text-sm text-gray-500">
                            导入合规制度文档及相关元数据
                          </p>
                          <div class="mt-4 flex space-x-2">
                            <button
                              v-auth="'dataImportAndExport/index/downloadTemplate'"
                              class="!rounded-button whitespace-nowrap border border-gray-300 px-3 py-1 text-sm text-gray-700 font-medium hover:bg-gray-50"
                            >
                              下载模板
                            </button>
                            <button
                              v-auth="'dataImportAndExport/index/importData'"
                              class="!rounded-button whitespace-nowrap bg-blue-600 px-3 py-1 text-sm text-white font-medium hover:bg-blue-700" @click="showImportDialog('compliance')"
                            >
                              导入数据
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Other Data Import Card -->
                    <div class="border border-gray-200 rounded-md bg-white p-4 transition-shadow hover:shadow-md">
                      <div class="flex items-start">
                        <div class="mr-4 rounded-full bg-orange-100 p-3">
                          <el-icon class="text-xl text-orange-600">
                            <ElIconFiles />
                          </el-icon>
                        </div>
                        <div class="flex-1">
                          <h3 class="text-gray-900 font-medium">
                            其他数据导入
                          </h3>
                          <p class="mt-1 text-sm text-gray-500">
                            导入系统其他类型数据
                          </p>
                          <div class="mt-4 flex space-x-2">
                            <button
                              v-auth="'dataImportAndExport/index/downloadTemplate'"
                              class="!rounded-button whitespace-nowrap border border-gray-300 px-3 py-1 text-sm text-gray-700 font-medium hover:bg-gray-50"
                            >
                              下载模板
                            </button>
                            <button
                              v-auth="'dataImportAndExport/index/importData'"
                              class="!rounded-button whitespace-nowrap bg-blue-600 px-3 py-1 text-sm text-white font-medium hover:bg-blue-700" @click="showImportDialog('other')"
                            >
                              导入数据
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="数据导出" name="export">
                  <div class="grid grid-cols-2 gap-4 p-4">
                    <!-- User Export Card -->
                    <div class="border border-gray-200 rounded-md bg-white p-4 transition-shadow hover:shadow-md">
                      <div class="flex items-start">
                        <div class="mr-4 rounded-full bg-blue-100 p-3">
                          <el-icon class="text-xl text-blue-600">
                            <ElIconUser />
                          </el-icon>
                        </div>
                        <div class="flex-1">
                          <h3 class="text-gray-900 font-medium">
                            用户数据导出
                          </h3>
                          <p class="mt-1 text-sm text-gray-500">
                            导出用户信息，支持筛选和字段选择
                          </p>
                          <div class="mt-4">
                            <button
                              v-auth="'dataImportAndExport/index/exportData'"
                              class="!rounded-button whitespace-nowrap bg-blue-600 px-3 py-1 text-sm text-white font-medium hover:bg-blue-700"
                              @click="showExportDialog('user')"
                            >
                              导出数据
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Organization Export Card -->
                    <div class="border border-gray-200 rounded-md bg-white p-4 transition-shadow hover:shadow-md">
                      <div class="flex items-start">
                        <div class="mr-4 rounded-full bg-green-100 p-3">
                          <el-icon class="text-xl text-green-600">
                            <ElIconOfficeBuilding />
                          </el-icon>
                        </div>
                        <div class="flex-1">
                          <h3 class="text-gray-900 font-medium">
                            组织数据导出
                          </h3>
                          <p class="mt-1 text-sm text-gray-500">
                            导出组织架构信息，支持多层级组织
                          </p>
                          <div class="mt-4">
                            <button
                              v-auth="'dataImportAndExport/index/exportData'"
                              class="!rounded-button whitespace-nowrap bg-blue-600 px-3 py-1 text-sm text-white font-medium hover:bg-blue-700"
                              @click="showExportDialog('organization')"
                            >
                              导出数据
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Compliance Export Card -->
                    <div class="border border-gray-200 rounded-md bg-white p-4 transition-shadow hover:shadow-md">
                      <div class="flex items-start">
                        <div class="mr-4 rounded-full bg-purple-100 p-3">
                          <el-icon class="text-xl text-purple-600">
                            <ElIconDocument />
                          </el-icon>
                        </div>
                        <div class="flex-1">
                          <h3 class="text-gray-900 font-medium">
                            合规制度导出
                          </h3>
                          <p class="mt-1 text-sm text-gray-500">
                            导出合规制度文档及相关元数据
                          </p>
                          <div class="mt-4">
                            <button
                              v-auth="'dataImportAndExport/index/exportData'"
                              class="!rounded-button whitespace-nowrap bg-blue-600 px-3 py-1 text-sm text-white font-medium hover:bg-blue-700" @click="showExportDialog('compliance')"
                            >
                              导出数据
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Record Export Card -->
                    <div class="border border-gray-200 rounded-md bg-white p-4 transition-shadow hover:shadow-md">
                      <div class="flex items-start">
                        <div class="mr-4 rounded-full bg-yellow-100 p-3">
                          <el-icon class="text-xl text-yellow-600">
                            <ElIconNotebook />
                          </el-icon>
                        </div>
                        <div class="flex-1">
                          <h3 class="text-gray-900 font-medium">
                            合规记录导出
                          </h3>
                          <p class="mt-1 text-sm text-gray-500">
                            导出合规执行记录和审计日志
                          </p>
                          <div class="mt-4">
                            <button
                              v-auth="'dataImportAndExport/index/exportData'"
                              class="!rounded-button whitespace-nowrap bg-blue-600 px-3 py-1 text-sm text-white font-medium hover:bg-blue-700" @click="showExportDialog('record')"
                            >
                              导出数据
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="导入导出记录" name="history">
                  <div class="p-4">
                    <!-- Filter Area -->
                    <div class="mb-4 rounded-md bg-gray-50 p-4">
                      <div class="grid grid-cols-4 gap-4">
                        <div>
                          <label class="mb-1 block text-sm text-gray-700 font-medium">操作类型</label>
                          <el-select v-model="filter.type" placeholder="请选择" class="w-full">
                            <el-option label="全部" value="all" />
                            <el-option label="导入" value="import" />
                            <el-option label="导出" value="export" />
                          </el-select>
                        </div>
                        <div>
                          <label class="mb-1 block text-sm text-gray-700 font-medium">数据类型</label>
                          <el-select v-model="filter.dataType" placeholder="请选择" class="w-full">
                            <el-option label="全部" value="all" />
                            <el-option label="用户数据" value="user" />
                            <el-option label="组织数据" value="organization" />
                            <el-option label="合规制度" value="compliance" />
                            <el-option label="合规记录" value="record" />
                          </el-select>
                        </div>
                        <div>
                          <label class="mb-1 block text-sm text-gray-700 font-medium">时间范围</label>
                          <el-date-picker
                            v-model="filter.dateRange" type="daterange" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" class="w-full"
                          />
                        </div>
                        <div>
                          <label class="mb-1 block text-sm text-gray-700 font-medium">状态</label>
                          <el-select v-model="filter.status" placeholder="请选择" class="w-full">
                            <el-option label="全部" value="all" />
                            <el-option label="成功" value="success" />
                            <el-option label="失败" value="failed" />
                            <el-option label="进行中" value="processing" />
                          </el-select>
                        </div>
                      </div>
                      <div class="mt-4 flex justify-end">
                        <button
                          class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-sm text-white font-medium hover:bg-blue-700"
                        >
                          筛选
                        </button>
                      </div>
                    </div>

                    <!-- Table -->
                    <el-table :data="historyData" style="width: 100%;">
                      <el-table-column prop="id" label="操作ID" width="120" />
                      <el-table-column prop="type" label="操作类型" width="100">
                        <template #default="{ row }">
                          <span
                            :class="{ 'text-blue-500': row.type === '导入', 'text-green-500': row.type === '导出' }"
                          >{{ row.type }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="dataType" label="数据类型" width="120" />
                      <el-table-column prop="fileName" label="文件名" />
                      <el-table-column prop="operator" label="操作人" width="120" />
                      <el-table-column prop="time" label="操作时间" width="160" />
                      <el-table-column prop="status" label="状态" width="100">
                        <template #default="{ row }">
                          <el-tag :type="getStatusTagType(row.status)" size="small">
                            {{ row.status }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="180">
                        <template #default="{ row }">
                          <button v-auth="'dataImportAndExport/index/view'" type="primary" size="small" class="mr-2 text-sm text-blue-600 hover:text-blue-800" @click="viewDetail(row)">
                            查看详情
                          </button>
                          <button
                            type="success" size="small"
                            v-if="row.status === '成功'" v-auth="'dataImportAndExport/index/downloadFile'"
                            class="mr-2 text-sm text-blue-600 hover:text-blue-800"
                            @click="downloadFile(row)"
                          >
                            下载文件
                          </button>
                          <button
                            v-auth="'dataImportAndExport/index/delete'" type="danger" size="small"
                            @click="deleteRecord(row)"
                          >
                            删除
                          </button>
                        </template>
                      </el-table-column>
                    </el-table>

                    <!-- Pagination -->
                    <div class="mt-4 flex items-center justify-between">
                      <div class="text-sm text-gray-500">
                        显示 {{ (pagination.currentPage - 1) * pagination.pageSize + 1 }} 到
                        {{ Math.min(pagination.currentPage * pagination.pageSize, pagination.total) }} 条，共
                        {{ pagination.total }} 条记录
                      </div>
                      <el-pagination
                        v-model:current-page="pagination.currentPage" :page-size="pagination.pageSize"
                        :total="pagination.total" layout="prev, pager, next, jumper"
                      />
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  数据统计
                </div>
              </template>
              <div class="space-y-4">
                <div>
                  <div class="mb-1 flex items-center justify-between">
                    <span class="text-sm text-gray-600">今日导入/导出</span>
                    <span class="font-medium">12 次</span>
                  </div>
                  <div class="h-2 w-full rounded-full bg-gray-200">
                    <div class="h-2 rounded-full bg-blue-600" style="width: 60%;" />
                  </div>
                </div>
                <div>
                  <div class="mb-1 flex items-center justify-between">
                    <span class="text-sm text-gray-600">本月导入/导出</span>
                    <span class="font-medium">86 次</span>
                  </div>
                  <div class="h-2 w-full rounded-full bg-gray-200">
                    <div class="h-2 rounded-full bg-blue-600" style="width: 80%;" />
                  </div>
                </div>
                <div>
                  <div class="mb-1 flex items-center justify-between">
                    <span class="text-sm text-gray-600">导入导出成功率</span>
                    <span class="font-medium">95.6%</span>
                  </div>
                  <div class="h-2 w-full rounded-full bg-gray-200">
                    <div class="h-2 rounded-full bg-green-500" style="width: 95.6%;" />
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  近期操作
                </div>
              </template>
              <div class="space-y-4">
                <div v-for="(activity, index) in recentActivities" :key="index" class="flex">
                  <div class="mr-3 flex flex-col items-center">
                    <div
                      class="mt-1 h-2 w-2 rounded-full" :class="[{ 'bg-green-500': activity.status === '成功',
                                                                   'bg-red-500': activity.status === '失败',
                                                                   'bg-blue-500': activity.status === '进行中' }]"
                    />
                    <div v-if="index !== recentActivities.length - 1" class="h-8 w-px bg-gray-200" />
                  </div>
                  <div>
                    <div class="text-sm font-medium">
                      {{ activity.operator }}
                    </div>
                    <div class="text-xs text-gray-500">
                      {{ activity.time }}
                    </div>
                    <div class="mt-1 text-sm">
                      {{ activity.action }}
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <div class="">
          <!-- Import Dialog -->
          <el-dialog v-model="importDialog.visible" :title="`${importDialog.type}数据导入`" width="600px">
            <el-steps :active="importDialog.step" finish-status="success" simple>
              <el-step title="上传文件" />
              <el-step title="配置选项" />
              <el-step title="验证预览" />
            </el-steps>

            <div class="mt-6">
              <div v-if="importDialog.step === 1">
                <el-upload
                  drag action="https://jsonplaceholder.typicode.com/posts/" :on-change="handleFileChange"
                  :auto-upload="false" class="mb-4"
                >
                  <el-icon class="el-icon--upload">
                    <ElIconUpload />
                  </el-icon>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <template #tip>
                    <div class="el-upload__tip text-xs text-gray-500">
                      请上传Excel或CSV格式的文件，大小不超过10MB
                    </div>
                  </template>
                </el-upload>
              </div>

              <div v-if="importDialog.step === 2" class="space-y-4">
                <div>
                  <label class="mb-1 block text-sm text-gray-700 font-medium">导入策略</label>
                  <el-radio-group v-model="importDialog.strategy">
                    <el-radio label="create">
                      仅创建新记录
                    </el-radio>
                    <el-radio label="update">
                      仅更新现有记录
                    </el-radio>
                    <el-radio label="both">
                      创建新记录并更新现有记录
                    </el-radio>
                  </el-radio-group>
                </div>
                <div>
                  <label class="mb-1 block text-sm text-gray-700 font-medium">冲突处理</label>
                  <el-radio-group v-model="importDialog.conflict">
                    <el-radio label="skip">
                      跳过冲突记录
                    </el-radio>
                    <el-radio label="overwrite">
                      覆盖冲突记录
                    </el-radio>
                  </el-radio-group>
                </div>
                <div>
                  <label class="mb-1 block text-sm text-gray-700 font-medium">通知设置</label>
                  <el-checkbox v-model="importDialog.notify">
                    导入完成后发送通知邮件
                  </el-checkbox>
                </div>
              </div>

              <div v-if="importDialog.step === 3">
                <div class="rounded-md bg-gray-50 p-4">
                  <div class="mb-2 flex justify-between">
                    <span class="text-sm font-medium">文件验证结果</span>
                    <el-tag type="success" size="small">
                      验证通过
                    </el-tag>
                  </div>
                  <div class="text-sm text-gray-600">
                    <p class="mb-1">
                      文件: {{ importDialog.fileName }}
                    </p>
                    <p class="mb-1">
                      记录数: 128 条
                    </p>
                    <p>预计导入时间: 约 15 秒</p>
                  </div>
                </div>
                <div class="mt-4">
                  <el-table :data="previewData" height="200" style="width: 100%;">
                    <el-table-column prop="field1" label="字段1" />
                    <el-table-column prop="field2" label="字段2" />
                    <el-table-column prop="field3" label="字段3" />
                  </el-table>
                </div>
              </div>
            </div>

            <template #footer>
              <div class="flex justify-between">
                <button
                  v-if="importDialog.step > 1"
                  class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-2 text-sm text-gray-700 font-medium hover:bg-gray-50"
                  @click="importDialog.step--"
                >
                  上一步
                </button>
                <div v-else />
                <div>
                  <button
                    class="!rounded-button mr-2 whitespace-nowrap border border-gray-300 px-4 py-2 text-sm text-gray-700 font-medium hover:bg-gray-50"
                    @click="importDialog.visible = false"
                  >
                    取消
                  </button>
                  <button
                    v-if="importDialog.step < 3"
                    class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-sm text-white font-medium hover:bg-blue-700"
                    @click="importDialog.step++"
                  >
                    下一步
                  </button>
                  <button
                    v-else
                    class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-sm text-white font-medium hover:bg-blue-700"
                    @click="startImport"
                  >
                    开始导入
                  </button>
                </div>
              </div>
            </template>
          </el-dialog>

          <!-- Export Dialog -->
          <el-dialog v-model="exportDialog.visible" :title="`${exportDialog.type}数据导出`" width="600px">
            <div class="space-y-4">
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">数据范围</label>
                <el-radio-group v-model="exportDialog.scope">
                  <el-radio label="all">
                    全部数据
                  </el-radio>
                  <el-radio label="filtered">
                    筛选数据
                  </el-radio>
                  <el-radio label="custom">
                    自定义选择
                  </el-radio>
                </el-radio-group>
              </div>
              <div v-if="exportDialog.scope === 'filtered'">
                <label class="mb-1 block text-sm text-gray-700 font-medium">筛选条件</label>
                <el-input v-model="exportDialog.filter" placeholder="输入筛选条件" />
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">导出字段</label>
                <el-checkbox-group v-model="exportDialog.fields">
                  <el-checkbox label="field1">
                    字段1
                  </el-checkbox>
                  <el-checkbox label="field2">
                    字段2
                  </el-checkbox>
                  <el-checkbox label="field3">
                    字段3
                  </el-checkbox>
                  <el-checkbox label="field4">
                    字段4
                  </el-checkbox>
                </el-checkbox-group>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">导出格式</label>
                <el-radio-group v-model="exportDialog.format">
                  <el-radio label="excel">
                    Excel (.xlsx)
                  </el-radio>
                  <el-radio label="csv">
                    CSV (.csv)
                  </el-radio>
                  <el-radio label="json">
                    JSON (.json)
                  </el-radio>
                </el-radio-group>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">文件名</label>
                <el-input v-model="exportDialog.fileName" placeholder="输入文件名" />
              </div>
            </div>

            <template #footer>
              <div class="flex justify-end">
                <button
                  class="!rounded-button mr-2 whitespace-nowrap border border-gray-300 px-4 py-2 text-sm text-gray-700 font-medium hover:bg-gray-50"
                  @click="exportDialog.visible = false"
                >
                  取消
                </button>
                <button
                  class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-sm text-white font-medium hover:bg-blue-700"
                  @click="startExport"
                >
                  开始导出
                </button>
              </div>
            </template>
          </el-dialog>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-step.is-simple .el-step__title {
    font-size: 14px;
  }

  .el-upload-dragger {
    padding: 20px;
  }

  .el-upload__text {
    margin-top: 10px;
  }

  .el-upload__tip {
    margin-top: 10px;
  }
</style>
