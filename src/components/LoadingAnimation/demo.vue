<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import LoadingAnimation from './index.vue'
import { LoadingPresets, useLoading, withLoading, withProgressLoading } from './useLoading'

defineOptions({
  name: 'LoadingAnimationDemo',
})

// 演示配置
const demoConfig = reactive({
  visible: false,
  text: '加载中...',
  size: 'medium' as 'small' | 'medium' | 'large',
  fullscreen: true,
  showProgress: false,
  progress: 0,
})

// 局部加载演示
const showLocalDemo = ref(false)
const localLoading = ref(false)

// 当前选中的标签页
const activeTab = ref('basic')

// 预设示例方法
function showBasicLoading() {
  Object.assign(demoConfig, {
    visible: true,
    text: '加载中...',
    size: 'medium',
    fullscreen: true,
    showProgress: false,
  })

  setTimeout(() => {
    demoConfig.visible = false
  }, 3000)
}

function showProgressLoading() {
  Object.assign(demoConfig, {
    visible: true,
    text: '正在处理数据...',
    size: 'large',
    fullscreen: true,
    showProgress: true,
    progress: 0,
  })

  // 模拟进度更新
  const interval = setInterval(() => {
    demoConfig.progress += 10
    if (demoConfig.progress >= 100) {
      clearInterval(interval)
      setTimeout(() => {
        demoConfig.visible = false
        demoConfig.progress = 0
      }, 500)
    }
  }, 300)
}

function showLocalLoading() {
  showLocalDemo.value = true
  localLoading.value = true

  setTimeout(() => {
    localLoading.value = false
  }, 3000)
}

function showCustomLoading() {
  Object.assign(demoConfig, {
    visible: true,
    text: '正在为您准备精彩内容...',
    size: 'medium',
    fullscreen: true,
    showProgress: false,
  })

  setTimeout(() => {
    demoConfig.visible = false
  }, 3000)
}

function handleImageError() {
  ElMessage.error('Logo图片加载失败')
}

// 组合式函数演示
const _composableLoading = useLoading()

function showComposableDemo() {
  ElMessage.info('查看下方的组合式函数演示')
}

async function simulatePageLoad() {
  await withLoading(
    () => new Promise(resolve => setTimeout(resolve, 2000)),
    LoadingPresets.page,
  )
  ElMessage.success('页面加载完成')
}

async function simulateDataFetch() {
  await withLoading(
    () => new Promise(resolve => setTimeout(resolve, 1500)),
    LoadingPresets.data,
  )
  ElMessage.success('数据获取完成')
}

async function simulateFileUpload() {
  await withProgressLoading(
    (updateProgress) => {
      return new Promise((resolve) => {
        let progress = 0
        const interval = setInterval(() => {
          progress += 20
          updateProgress(progress)
          if (progress >= 100) {
            clearInterval(interval)
            resolve(undefined)
          }
        }, 300)
      })
    },
    LoadingPresets.upload,
  )
  ElMessage.success('文件上传完成')
}

async function simulateFormSubmit() {
  await withLoading(
    () => new Promise(resolve => setTimeout(resolve, 1000)),
    LoadingPresets.submit,
  )
  ElMessage.success('表单提交完成')
}

// 代码示例
const basicUsageCode = `<template>
  <LoadingAnimation
    :visible="loading"
    text="加载中..."
    size="medium"
    :fullscreen="true"
  />
</template>

<script setup>
import { ref } from 'vue'
import LoadingAnimation from '@/components/LoadingAnimation/index.vue'

const loading = ref(false)

function showLoading() {
  loading.value = true
  // 模拟异步操作
  setTimeout(() => {
    loading.value = false
  }, 3000)
}
<\/script>`

const progressUsageCode = `<template>
  <LoadingAnimation
    :visible="loading"
    text="正在处理数据..."
    size="large"
    :show-progress="true"
    :progress="progress"
  />
</template>

<script setup>
import { ref } from 'vue'
import LoadingAnimation from '@/components/LoadingAnimation/index.vue'

const loading = ref(false)
const progress = ref(0)

function startProgressLoading() {
  loading.value = true
  progress.value = 0

  const interval = setInterval(() => {
    progress.value += 10
    if (progress.value >= 100) {
      clearInterval(interval)
      loading.value = false
    }
  }, 300)
}
<\/script>`

const localUsageCode = `<template>
  <div class="relative-container">
    <div class="content">
      <!-- 你的内容 -->
    </div>

    <LoadingAnimation
      :visible="loading"
      :fullscreen="false"
      text="局部加载中..."
      size="medium"
    />
  </div>
</template>

<style scoped>
.relative-container {
  position: relative;
  min-height: 200px;
}
</style>`

const composableUsageCode = `<template>
  <div>
    <el-button @click="handleAsyncOperation">异步操作</el-button>
    <el-button @click="handleProgressOperation">进度操作</el-button>

    <LoadingAnimation v-bind="loading.state" />
  </div>
</template>

<script setup>
import { useLoading, withLoading, withProgressLoading, LoadingPresets } from '@/components/LoadingAnimation/useLoading'

// 创建加载实例
const loading = useLoading()

// 使用包装器函数
async function handleAsyncOperation() {
  await withLoading(
    () => fetch('/api/data').then(res => res.json()),
    LoadingPresets.data
  )
}

// 使用进度包装器
async function handleProgressOperation() {
  await withProgressLoading(
    (updateProgress) => {
      return new Promise(resolve => {
        let progress = 0
        const interval = setInterval(() => {
          progress += 10
          updateProgress(progress)
          if (progress >= 100) {
            clearInterval(interval)
            resolve()
          }
        }, 200)
      })
    },
    LoadingPresets.upload
  )
}
<\/script>`

// API文档数据
const propsData = [
  { name: 'visible', type: 'boolean', default: 'false', description: '是否显示加载动画' },
  { name: 'text', type: 'string', default: '加载中...', description: '加载提示文字' },
  { name: 'size', type: 'small | medium | large', default: 'medium', description: '组件尺寸' },
  { name: 'fullscreen', type: 'boolean', default: 'true', description: '是否全屏显示' },
  { name: 'showProgress', type: 'boolean', default: 'false', description: '是否显示进度条' },
  { name: 'progress', type: 'number', default: '0', description: '进度值 (0-100)' },
  { name: 'logoUrl', type: 'string', default: 'mbblogo.png', description: '自定义Logo地址' },
]

const eventsData = [
  { name: 'imageError', description: 'Logo图片加载失败时触发', params: '无' },
]
</script>

<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1 class="demo-title">
        LoadingAnimation 组件演示
      </h1>
      <p class="demo-description">
        定制化的项目加载动画组件，支持多种配置选项
      </p>
    </div>

    <!-- 控制面板 -->
    <el-card class="demo-controls">
      <template #header>
        <div class="card-header">
          <span>控制面板</span>
        </div>
      </template>

      <div class="controls-grid">
        <div class="control-item">
          <label>显示状态：</label>
          <el-switch v-model="demoConfig.visible" />
        </div>

        <div class="control-item">
          <label>尺寸：</label>
          <el-radio-group v-model="demoConfig.size">
            <el-radio-button label="small">
              小
            </el-radio-button>
            <el-radio-button label="medium">
              中
            </el-radio-button>
            <el-radio-button label="large">
              大
            </el-radio-button>
          </el-radio-group>
        </div>

        <div class="control-item">
          <label>全屏模式：</label>
          <el-switch v-model="demoConfig.fullscreen" />
        </div>

        <div class="control-item">
          <label>显示进度：</label>
          <el-switch v-model="demoConfig.showProgress" />
        </div>

        <div class="control-item">
          <label>加载文字：</label>
          <el-input v-model="demoConfig.text" placeholder="请输入加载文字" style="width: 200px;" />
        </div>

        <div v-if="demoConfig.showProgress" class="control-item">
          <label>进度值：</label>
          <el-slider v-model="demoConfig.progress" :max="100" style="width: 200px;" />
        </div>
      </div>
    </el-card>

    <!-- 预设示例 -->
    <el-card class="demo-examples">
      <template #header>
        <div class="card-header">
          <span>预设示例</span>
        </div>
      </template>

      <div class="examples-grid">
        <el-button type="primary" @click="showBasicLoading">
          基础加载
        </el-button>

        <el-button type="success" @click="showProgressLoading">
          进度加载
        </el-button>

        <el-button type="warning" @click="showLocalLoading">
          局部加载
        </el-button>

        <el-button type="info" @click="showCustomLoading">
          自定义文字
        </el-button>

        <el-button type="danger" @click="showComposableDemo">
          组合式函数演示
        </el-button>
      </div>
    </el-card>

    <!-- 组合式函数演示 -->
    <el-card class="demo-composable">
      <template #header>
        <div class="card-header">
          <span>组合式函数演示</span>
        </div>
      </template>

      <div class="composable-grid">
        <el-button @click="simulatePageLoad">
          模拟页面加载
        </el-button>

        <el-button @click="simulateDataFetch">
          模拟数据获取
        </el-button>

        <el-button @click="simulateFileUpload">
          模拟文件上传
        </el-button>

        <el-button @click="simulateFormSubmit">
          模拟表单提交
        </el-button>
      </div>
    </el-card>

    <!-- 代码示例 -->
    <el-card class="demo-code">
      <template #header>
        <div class="card-header">
          <span>代码示例</span>
        </div>
      </template>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="基础用法" name="basic">
          <pre><code>{{ basicUsageCode }}</code></pre>
        </el-tab-pane>

        <el-tab-pane label="进度模式" name="progress">
          <pre><code>{{ progressUsageCode }}</code></pre>
        </el-tab-pane>

        <el-tab-pane label="局部加载" name="local">
          <pre><code>{{ localUsageCode }}</code></pre>
        </el-tab-pane>

        <el-tab-pane label="组合式函数" name="composable">
          <pre><code>{{ composableUsageCode }}</code></pre>
        </el-tab-pane>

        <el-tab-pane label="API文档" name="api">
          <div class="api-docs">
            <h3>Props</h3>
            <el-table :data="propsData" border>
              <el-table-column prop="name" label="属性名" width="120" />
              <el-table-column prop="type" label="类型" width="150" />
              <el-table-column prop="default" label="默认值" width="120" />
              <el-table-column prop="description" label="说明" />
            </el-table>

            <h3 style="margin-top: 24px;">
              Events
            </h3>
            <el-table :data="eventsData" border>
              <el-table-column prop="name" label="事件名" width="120" />
              <el-table-column prop="description" label="说明" />
              <el-table-column prop="params" label="参数" />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 局部加载演示区域 -->
    <el-card v-if="showLocalDemo" class="demo-local-area">
      <template #header>
        <div class="card-header">
          <span>局部加载演示区域</span>
        </div>
      </template>

      <div class="local-demo-content">
        <p>这是一个演示局部加载的区域</p>
        <p>加载动画只会在这个区域内显示</p>

        <LoadingAnimation
          :visible="localLoading"
          :fullscreen="false"
          text="局部加载中..."
          size="medium"
        />
      </div>
    </el-card>

    <!-- 主加载动画 -->
    <LoadingAnimation
      :visible="demoConfig.visible"
      :text="demoConfig.text"
      :size="demoConfig.size"
      :fullscreen="demoConfig.fullscreen"
      :show-progress="demoConfig.showProgress"
      :progress="demoConfig.progress"
      @image-error="handleImageError"
    />
  </div>
</template>

<style scoped>
.demo-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
}

.demo-title {
  font-size: 28px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8px;
}

.demo-description {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.demo-controls,
.demo-examples,
.demo-composable,
.demo-code,
.demo-local-area {
  margin-bottom: 24px;
}

.card-header {
  font-weight: 600;
  color: #374151;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-item label {
  min-width: 80px;
  font-weight: 500;
  color: #374151;
}

.examples-grid,
.composable-grid {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.demo-code pre {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.5;
}

.demo-code code {
  color: #1e293b;
}

.api-docs h3 {
  color: #374151;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}

.local-demo-content {
  position: relative;
  min-height: 200px;
  padding: 24px;
  background: #f9fafb;
  border-radius: 8px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.local-demo-content p {
  margin: 8px 0;
  color: #6b7280;
}

@media (max-width: 768px) {
  .demo-container {
    padding: 16px;
  }

  .controls-grid {
    grid-template-columns: 1fr;
  }

  .examples-grid {
    flex-direction: column;
  }
}
</style>
