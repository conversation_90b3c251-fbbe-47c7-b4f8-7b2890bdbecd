<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import systemApi from '@/api/complianceApi/one/systemManagement.ts'
import MarkdownRenderer from '@/components/MarkdownRenderer/index.vue'

const router = useRouter()
const route = useRoute()

const detailData: any = ref({})
const loading = ref(false)

// 获取详情数据
function getDetail() {
  const id = route.query.id
  if (!id) {
    return
  }

  loading.value = true
  systemApi.regulatoryConversion({ id }, 'info').then((res: any) => {
    detailData.value = res
  }).catch((_error: any) => {
    // 处理错误
  }).finally(() => {
    loading.value = false
  })
}

// 返回列表
function goBack() {
  router.push({
    name: '/one/regulatoryConversion/index',
  })
}

// 编辑
function _goEdit() {
  const id = route.query.id
  router.push({
    name: '/one/regulatoryConversion/addEdit',
    query: { id },
  })
}

// 页面加载时获取详情
onMounted(() => {
  getDetail()
})

// 格式化状态
function formatStatus(status: string) {
  const statusMap: any = {
    DRAFT: '草稿',
    PROGRESS: '进行中',
    FINISHED: '已完成',
    OVERTIME: '已超时',
    CANCEL: '已取消',
  }
  return statusMap[status] || '未知'
}

// 格式化优先级
function formatLevel(level: string) {
  const levelMap: any = {
    HIGH: '高',
    MIDDLE: '中',
    LOW: '低',
  }
  return levelMap[level] || '未知'
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              转化任务详情
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button v-debounce="3000" type="primary" plain @click="goBack">
              <svg-icon name="ep:back" />
              <span class="ml-4">返回</span>
            </el-button>
            <!-- <el-button type="primary" @click="editTransform">
              <svg-icon name="ep:edit" />
              <span class="ml-4">编辑</span>
            </el-button> -->
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div v-loading="loading" class="space-y-6">
        <el-card class="card">
          <div class="space-y-6">
            <!-- 基本信息 -->
            <div class="mb-6">
              <h3 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
                基本信息
              </h3>
              <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">任务名称：</span>
                  <span class="flex-1">{{ detailData.taskName || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">法规名称：</span>
                  <span class="flex-1">{{ detailData.lawsRegulation?.title || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">企业制度：</span>
                  <span class="flex-1">{{ detailData.enterpriseRegulation || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">优先级：</span>
                  <span class="flex-1">
                    <el-tag v-if="detailData.level === 'HIGH'" type="danger">{{ formatLevel(detailData.level) }}</el-tag>
                    <el-tag v-else-if="detailData.level === 'MIDDLE'" type="warning">{{ formatLevel(detailData.level) }}</el-tag>
                    <el-tag v-else-if="detailData.level === 'LOW'" type="info">{{ formatLevel(detailData.level) }}</el-tag>
                    <span v-else>-</span>
                  </span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">状态：</span>
                  <span class="flex-1">
                    <el-tag v-if="detailData.status === 'PROGRESS'" type="primary">{{ formatStatus(detailData.status) }}</el-tag>
                    <el-tag v-else-if="detailData.status === 'FINISHED'" type="success">{{ formatStatus(detailData.status) }}</el-tag>
                    <el-tag v-else-if="detailData.status === 'OVERTIME'" type="warning">{{ formatStatus(detailData.status) }}</el-tag>
                    <el-tag v-else-if="detailData.status === 'CANCEL'" type="info">{{ formatStatus(detailData.status) }}</el-tag>
                    <el-tag v-else-if="detailData.status === 'DRAFT'" type="info">{{ formatStatus(detailData.status) }}</el-tag>
                    <span v-else>-</span>
                  </span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">创建人：</span>
                  <span class="flex-1">{{ detailData.createdBy || '-' }}</span>
                </div>
              </div>
            </div>

            <!-- 时间信息 -->
            <div class="mb-6">
              <h3 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
                时间信息
              </h3>
              <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">开始日期：</span>
                  <span class="flex-1">{{ detailData.startDate || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">完成日期：</span>
                  <span class="flex-1">{{ detailData.finishDate || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">创建时间：</span>
                  <span class="flex-1">{{ detailData.createdAt || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">更新时间：</span>
                  <span class="flex-1">{{ detailData.updatedAt || '-' }}</span>
                </div>
              </div>
            </div>

            <!-- 描述信息 -->
            <div v-if="detailData.content" class="mb-6">
              <h3 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
                描述信息
              </h3>
              <div class="border-l-4 border-blue-500 rounded bg-gray-50 p-4">
                <MarkdownRenderer :content="detailData.content" />
              </div>
            </div>
            <div v-if="detailData.lawsRegulations && detailData.lawsRegulations.length > 0" class="mb-6">
              <h3 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
                关联法规
              </h3>
              <div v-for="item in detailData.lawsRegulations" :key="item.id" class="border-l-4 border-blue-500 rounded bg-gray-50 p-4">
                <MarkdownRenderer :content="item.content" />
              </div>
            </div>

            <!-- 空状态 -->
            <!-- <div v-if="!detailData.taskName && !loading" class="py-10 text-center">
              <p class="text-gray-500">
                暂无数据
              </p>
            </div> -->
          </div>
        </el-card>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }
</style>
