<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import {
  Check, Close, Delete, Document,
  Download, Edit, Key, Menu, Plus, Refresh, Search, Upload, User, View,
} from '@element-plus/icons-vue'
import organizationalApi from '@/api/organizational/index'
import roleTree from '@/api/permissions/role'
import ImportComponent from '@/components/import/ImportV1.vue'

// 角色数据
const roleData = ref<any[]>([])
const loading = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 批量选择
const multipleSelection = ref([])
const selectedCount = ref(0)

// 弹窗相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const formRef = ref()

// 导入弹窗状态
const importDialogVisible = ref(false)

// 权限分配相关
const permissionDialogVisible = ref(false)
const permissionLoading = ref(false)
const permissionTreeData = ref([])
const _checkedPermissions = ref([])
const currentRoleForPermission = ref<any>(null)
const permissionTreeRef = ref()
const isPermissionEditing = ref(false) // 标识是否正在编辑权限（用于控制父子联动）

// 表单数据
const formData = ref({
  id: null,
  name: '',
  code: '',
  description: '',
  status: 1,
  metadata: '',
  version: 0,
  parent: null,
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
}

const chartRef = ref()
const roleUsage = ref([
  { name: '系统管理员', count: 3, color: 'bg-blue-500' },
  { name: '合规管理员', count: 8, color: 'bg-green-500' },
  { name: '部门管理员', count: 15, color: 'bg-purple-500' },
  { name: '审计员', count: 5, color: 'bg-yellow-500' },
  { name: '报表查看员', count: 12, color: 'bg-red-500' },
])

// 获取角色列表
async function fetchRoleData() {
  try {
    loading.value = true
    const arr = await organizationalApi.roleApi(
      { page: currentPage.value - 1, size: pageSize.value },
      { keyword: searchQuery.value },
      null,
    )
    const response = arr.content
    if (response && Array.isArray(response)) {
      roleData.value = response.map(item => ({
        ...item,
        statusText: item.status === 1 ? '启用' : '禁用',
        createTime: formatTime(item.createdAt),
      }))
      total.value = arr.totalElements
    }
    else {
      roleData.value = []
      total.value = 0
    }
  }
  catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
    roleData.value = []
    total.value = 0
  }
  finally {
    loading.value = false
  }
}

// 搜索角色
function handleSearch() {
  currentPage.value = 1
  fetchRoleData()
}

// 重置搜索
function handleReset() {
  searchQuery.value = ''
  currentPage.value = 1
  fetchRoleData()
}

// 分页处理
function handleSizeChange(val: any) {
  pageSize.value = val
  fetchRoleData()
}

function handleCurrentChange(val: any) {
  currentPage.value = val
  fetchRoleData()
}

// 选择处理
function handleSelectionChange(val: any) {
  multipleSelection.value = val
  selectedCount.value = val.length
}

function cancelSelection() {
  multipleSelection.value = []
  selectedCount.value = 0
}

// 重置表单
function resetForm() {
  formData.value = {
    id: null,
    name: '',
    code: '',
    description: '',
    status: 1,
    metadata: '',
    version: 0,
    parent: null,
  }
  formRef.value?.clearValidate()
}

// 新增角色
function handleAdd() {
  resetForm()
  dialogTitle.value = '新增角色'
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑角色
function handleEdit(row: any) {
  resetForm()
  formData.value = {
    id: row.id,
    name: row.name,
    code: row.code,
    description: row.description || '',
    status: row.status,
    metadata: row.metadata || '',
    version: row.version,
    parent: row.parent,
  }
  dialogTitle.value = '编辑角色'
  isEdit.value = true
  dialogVisible.value = true
}

// 保存角色
async function saveRole() {
  if (!formRef.value) {
    return
  }

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      // 更新角色
      await organizationalApi.roleApi(
        { id: formData.value.id },
        formData.value,
        'update',
      )
      ElMessage.success('更新角色成功')
    }
    else {
      // 创建角色
      await organizationalApi.roleApi(
        {},
        formData.value,
        'create',
      )
      ElMessage.success('创建角色成功')
    }

    dialogVisible.value = false
    await fetchRoleData()
  }
  catch (error) {
    console.error('保存角色失败:', error)
    ElMessage.error('保存角色失败')
  }
  finally {
    loading.value = false
  }
}

// 删除角色
async function handleDelete(row: any) {
  try {
    await ElMessageBox.confirm('确定要删除这个角色吗？删除后不可恢复！', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await organizationalApi.roleApi(
      { },
      { id: row.id },
      'delete',
    )
    ElMessage.success('删除成功')
    await fetchRoleData()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 切换状态
async function handleToggleStatus(row: any) {
  try {
    const newStatus = row.status === 1 ? 0 : 1
    await organizationalApi.roleApi(
      { id: row.id },
      { id: row.id, name: row.name, code: row.code, status: newStatus },
      'update',
    )
    row.status = newStatus
    row.statusText = newStatus === 1 ? '启用' : '禁用'
    ElMessage.success(`${newStatus === 1 ? '启用' : '禁用'}成功`)
  }
  catch (error) {
    console.error('切换状态失败:', error)
    ElMessage.error('操作失败')
  }
}

// 分配权限
async function handleAssignPermissions(row: any) {
  try {
    currentRoleForPermission.value = row
    permissionDialogVisible.value = true
    permissionLoading.value = true
    isPermissionEditing.value = false // 初始状态为非编辑模式

    // 获取所有权限树
    const allPermissions = await roleTree.rolePermissionsTree()
    console.log('所有权限树数据:', allPermissions)
    permissionTreeData.value = transformPermissionData(allPermissions)
    console.log('转换后的权限树数据:', permissionTreeData.value)

    // 获取角色已有权限
    const rolePermissions = await roleTree.rolePermissionsApi(row.id)
    console.log('角色已有权限数据:', rolePermissions)

    // 创建权限ID集合用于快速查找
    const rolePermissionIds = new Set()
    function collectPermissionIds(permissions: any) {
      permissions.forEach((permission: any) => {
        // 添加菜单权限ID
        rolePermissionIds.add(permission.id)
        console.log('添加菜单权限ID:', permission.id)

        // 添加按钮权限ID
        if (permission.auths && permission.auths.length > 0) {
          permission.auths.forEach((auth: any) => {
            rolePermissionIds.add(auth.id)
            console.log('添加按钮权限ID:', auth.id, auth.name)
          })
        }

        // 递归处理子菜单
        if (permission.children && permission.children.length > 0) {
          collectPermissionIds(permission.children)
        }
      })
    }
    collectPermissionIds(rolePermissions)
    console.log('收集到的所有权限ID:', Array.from(rolePermissionIds))

    // 设置选中状态 - 只选中真正拥有权限的节点
    await nextTick()
    const checkedKeys: any[] = []
    function checkPermissionInTree(nodes: any) {
      nodes.forEach((node: any) => {
        if (rolePermissionIds.has(node.originalId)) {
          checkedKeys.push(node.id)
          console.log('匹配到权限节点:', node.id, node.label, node.type, '原始ID:', node.originalId)
        }
        if (node.children && node.children.length > 0) {
          checkPermissionInTree(node.children)
        }
      })
    }
    checkPermissionInTree(permissionTreeData.value)
    console.log('最终选中的节点keys:', checkedKeys)

    if (permissionTreeRef.value) {
      permissionTreeRef.value.setCheckedKeys(checkedKeys)
    }
    isPermissionEditing.value = true // 编辑模式，父子联动效果
  }
  catch (error) {
    console.error('获取权限数据失败:', error)
    ElMessage.error('获取权限数据失败')
  }
  finally {
    permissionLoading.value = false
  }
}

// 开始编辑权限（启用父子联动）
function startEditingPermissions() {
  isPermissionEditing.value = true
}

// 权限树节点点击事件
function handlePermissionCheck() {
  // 当用户开始操作权限树时，自动进入编辑模式
  if (!isPermissionEditing.value) {
    isPermissionEditing.value = true
  }
}

// 重置权限选择（回到严格模式）
async function resetPermissionSelection() {
  try {
    isPermissionEditing.value = false

    // 重新获取角色已有权限并设置选中状态
    const rolePermissions = await roleTree.rolePermissionsApi(currentRoleForPermission.value?.id || '')

    // 创建权限ID集合用于快速查找
    const rolePermissionIds = new Set()
    function collectPermissionIds(permissions: any) {
      permissions.forEach((permission: any) => {
        // 添加菜单权限ID
        rolePermissionIds.add(permission.id)

        // 添加按钮权限ID
        if (permission.auths && permission.auths.length > 0) {
          permission.auths.forEach((auth: any) => {
            rolePermissionIds.add(auth.id)
          })
        }

        // 递归处理子菜单
        if (permission.children && permission.children.length > 0) {
          collectPermissionIds(permission.children)
        }
      })
    }
    collectPermissionIds(rolePermissions)

    // 设置选中状态 - 只选中真正拥有权限的节点
    await nextTick()
    const checkedKeys: any[] = []
    function checkPermissionInTree(nodes: any) {
      nodes.forEach((node: any) => {
        if (rolePermissionIds.has(node.originalId)) {
          checkedKeys.push(node.id)
        }
        if (node.children && node.children.length > 0) {
          checkPermissionInTree(node.children)
        }
      })
    }
    checkPermissionInTree(permissionTreeData.value)

    if (permissionTreeRef.value) {
      permissionTreeRef.value.setCheckedKeys(checkedKeys)
    }
  }
  catch (error) {
    console.error('重置权限选择失败:', error)
    ElMessage.error('重置权限选择失败')
  }
}

// 保存权限
async function savePermissions() {
  try {
    permissionLoading.value = true

    // 获取所有选中的节点（包括父节点和子节点）
    const checkedNodes = permissionTreeRef.value.getCheckedNodes(false, true)
    const permissionIds = checkedNodes.map((node: any) => node.originalId)
    // 按照要求的格式拼装权限数据
    const permissionData = permissionIds.map((permissionId: any) => ({
      permission: {
        id: permissionId,
      },
      role: {
        id: currentRoleForPermission.value?.id || '',
      },
    }))

    // 调用保存权限的API
    await roleTree.roleAndPermissions(permissionData)

    ElMessage.success('权限分配成功')
    permissionDialogVisible.value = false
  }
  catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error('保存权限失败')
  }
  finally {
    permissionLoading.value = false
  }
}

// 格式化时间
function formatTime(timeObj: any) {
  if (!timeObj || !timeObj.seconds) {
    return '-'
  }
  const date = new Date(timeObj.seconds * 1000)
  return date.toLocaleDateString('zh-CN')
}

// 解析meta字段
function parseMeta(metaStr: any) {
  try {
    return JSON.parse(metaStr || '{}')
  }
  catch {
    return {}
  }
}

// 转换权限数据为树形结构
function transformPermissionData(data: any) {
  return data.map((item: any) => {
    const meta = parseMeta(item.meta)
    const transformed: any = {
      id: `menu_${item.id}`,
      originalId: item.id,
      label: meta.title || item.name,
      type: 'menu',
      children: [],
    }

    // 处理子菜单
    if (item.children && item.children.length > 0) {
      transformed.children = transformPermissionData(item.children)
    }

    // 处理按钮权限
    if (item.auths && item.auths.length > 0) {
      const authNodes = item.auths.map((auth: any) => ({
        id: `auth_${auth.id}`,
        originalId: auth.id,
        label: auth.name,
        type: 'auth',
        value: auth.value,
      }))
      transformed.children.push(...(authNodes as any))
    }

    return transformed
  })
}

// 批量操作
function batchEnable() {
  // TODO: 实现批量启用逻辑
}

function batchDisable() {
  // TODO: 实现批量禁用逻辑
}

function batchDelete() {
  // TODO: 实现批量删除逻辑
}

// 显示导入弹窗
function showImportDialog() {
  importDialogVisible.value = true
}

// 导入成功回调
function handleImportSuccess(result: any) {
  // 刷新角色列表
  fetchRoleData()
}

// 导入失败回调
function handleImportError(error: any) {
  console.error('导入失败:', error)
}

onMounted(() => {
  fetchRoleData()
  nextTick(() => {
    if (chartRef.value) {
      const chart = echarts.init(chartRef.value)
      const option = {
        animation: false,
        tooltip: {
          trigger: 'item',
        },
        legend: {
          top: '5%',
          left: 'center',
        },
        series: [
          {
            name: '角色分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 3, name: '系统角色' },
              { value: 12, name: '自定义角色' },
            ],
            color: ['#1E88E5', '#7460EE'],
          },
        ],
      }
      chart.setOption(option)
      window.addEventListener('resize', () => {
        chart.resize()
      })
    }
  })
})
</script>

<template>
  <div class="absolute-container" style="">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              角色管理
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="['roleManagement/index/add']" type="primary" @click="handleAdd">
              <!-- <el-icon>
                <Plus />
              </el-icon> -->
              <span>新增角色</span>
            </el-button>
            <!-- 新增导入按钮 角色导入 -->
            <el-button v-auth="['roleManagement/index/import']" @click="showImportDialog">
              <!-- <el-icon>
                <Upload />
              </el-icon> -->
              <span>导入角色</span>
            </el-button>
            <!-- <el-button v-auth="['roleManagement/index/export']">
              <el-icon>
                <Download />
              </el-icon>
              <span>导出</span>
            </el-button> -->
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="24">
            <el-card shadow="hover" class="">
              <!--              <template #header>
                <div class="f-16 fw-600">基本信息</div>
              </template> -->
              <div class="flex items-center justify-between border-b p-4">
                <div class="flex items-center gap-2">
                  <el-input
                    v-model="searchQuery"
                    placeholder="搜索角色名称或编码"
                    class="w-64"
                    clearable
                    @keyup.enter="handleSearch"
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                  <el-button v-auth="['roleManagement/index/search']" type="primary" @click="handleSearch">
                    查询
                  </el-button>
                  <el-button v-auth="['roleManagement/index/reset']" @click="handleReset">
                    重置
                  </el-button>
                </div>
              </div>
              <el-table
                v-loading="loading" :data="roleData"
                style="width: 100%;"
                :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="50" />
                <el-table-column prop="code" label="角色" width="120">
                  <template #default="{ row }">
                    <span v-if="row.code === 'TENANT_ADMIN'">管理员</span>
                    <span v-else-if="row.code === 'STAFF'">普通员工</span>
                    <span v-else>{{ row.code }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="角色名称" width="150" />
                <el-table-column prop="description" label="角色描述" show-overflow-tooltip />
                <el-table-column prop="createdAt" label="创建时间" width="150" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                      {{ row.statusText }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="280">
                  <template #default="{ row }">
                    <div class="flex space-x-1">
                      <el-button v-auth="['roleManagement/index/edit']" size="small" type="primary" plain @click="handleEdit(row)">
                        编辑
                      </el-button>
                      <el-button v-auth="['roleManagement/index/toggleStatus']" size="small" plain :type="row.status === 1 ? 'warning' : 'success'" @click="handleToggleStatus(row)">
                        {{ row.status === 1 ? '禁用' : '启用' }}
                      </el-button>
                      <el-button v-auth="['roleManagement/index/assignPermissions']" size="small" type="success" plain @click="handleAssignPermissions(row)">
                        分配权限
                      </el-button>
                      <el-button v-auth="['roleManagement/index/delete']" size="small" type="danger" plain @click="handleDelete(row)">
                        删除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="flex items-center justify-between border-t p-4">
                <div v-if="false" class="flex items-center space-x-3">
                  <div class="text-sm text-gray-500">
                    <span>已选择 {{ selectedCount }} 项</span>
                  </div>
                  <el-button v-auth="['roleManagement/index/batchEnable']" size="small" class="!rounded-button whitespace-nowrap" type="success" plain @click="batchEnable">
                    <el-icon>
                      <Check />
                    </el-icon>
                    <span>批量启用</span>
                  </el-button>
                  <el-button v-auth="['roleManagement/index/batchDisable']" size="small" class="!rounded-button whitespace-nowrap" type="warning" plain @click="batchDisable">
                    <el-icon>
                      <Close />
                    </el-icon>
                    <span>批量禁用</span>
                  </el-button>
                  <el-button v-auth="['roleManagement/index/batchDelete']" size="small" class="!rounded-button whitespace-nowrap" type="danger" plain @click="batchDelete">
                    <el-icon>
                      <Delete />
                    </el-icon>
                    <span>批量删除</span>
                  </el-button>
                  <el-button v-auth="['roleManagement/index/cancelSelection']" size="small" class="!rounded-button whitespace-nowrap" text @click="cancelSelection">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                    <span>取消选择</span>
                  </el-button>
                </div>
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="total"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </el-col>
          <el-col v-if="false" :span="0">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  角色概览
                </div>
              </template>
              <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                  <div class="rounded bg-blue-50 p-3">
                    <div class="text-sm text-gray-500">
                      总角色数
                    </div>
                    <div class="mt-1 text-xl font-bold">
                      15
                    </div>
                  </div>
                  <div class="rounded bg-green-50 p-3">
                    <div class="text-sm text-gray-500">
                      系统角色
                    </div>
                    <div class="mt-1 text-xl font-bold">
                      3
                    </div>
                  </div>
                  <div class="rounded bg-purple-50 p-3">
                    <div class="text-sm text-gray-500">
                      自定义角色
                    </div>
                    <div class="mt-1 text-xl font-bold">
                      12
                    </div>
                  </div>
                  <div class="rounded bg-red-50 p-3">
                    <div class="text-sm text-gray-500">
                      禁用角色
                    </div>
                    <div class="mt-1 text-xl font-bold">
                      2
                    </div>
                  </div>
                </div>
                <div ref="chartRef" class="h-60" />
                <div>
                  <h3 class="mb-2 text-sm font-bold">
                    角色使用情况
                  </h3>
                  <div class="space-y-2">
                    <div v-for="item in roleUsage" :key="item.name" class="flex items-center justify-between text-sm">
                      <div class="flex items-center">
                        <div class="mr-2 h-2 w-2 rounded-full" :class="item.color" />
                        <span>{{ item.name }}</span>
                      </div>
                      <span>{{ item.count }}人</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <!-- <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">角色使用情况</div>
              </template>
            </el-card>
          </el-col> -->
          </el-col>
        </el-row>
      </div>
    </PageMain>

    <!-- 新增/编辑角色弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入角色名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="角色" prop="code">
          <el-select
            v-model="formData.code"
            placeholder="请选择角色编码"
            style="width: 100%"
          >
            <el-option
              label="管理员"
              value="TENANT_ADMIN"
            />
            <el-option
              label="普通员工"
              value="STAFF"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="角色描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入角色描述"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">
              启用
            </el-radio>
            <el-radio :label="0">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" :loading="loading" @click="saveRole">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限分配弹窗 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="分配权限"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-loading="permissionLoading">
        <el-alert
          title="权限说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        >
          <template #default>
            <div>请选择要分配给角色的权限：</div>
            <div style="margin-top: 8px">
              <el-icon style="margin-right: 4px">
                <Menu />
              </el-icon>
              菜单权限：控制用户可以访问的页面
            </div>
            <div style="margin-top: 4px">
              <el-icon style="margin-right: 4px">
                <Key />
              </el-icon>
              按钮权限：控制用户可以执行的操作
            </div>
          </template>
        </el-alert>

        <div class="permission-tree-container">
          <div class="mb-3 flex items-center justify-between">
            <div class="flex items-center gap-2">
              <el-tag v-if="isPermissionEditing" type="warning" size="small">
                编辑模式 - 支持父子联动
              </el-tag>
              <el-tag v-else type="info" size="small">
                查看模式 - 精确显示已有权限
              </el-tag>
            </div>
            <el-button v-if="isPermissionEditing" size="small" @click="resetPermissionSelection">
              重置为原始权限
            </el-button>
          </div>
          <el-tree
            ref="permissionTreeRef"
            :data="permissionTreeData"
            show-checkbox
            node-key="id"
            :default-expand-all="true"
            :check-strictly="!isPermissionEditing"
            class="permission-tree"
            @check="handlePermissionCheck"
            @node-click="startEditingPermissions"
          >
            <template #default="{ data }">
              <div class="custom-tree-node">
                <el-icon v-if="data.type === 'menu'" style="margin-right: 4px; color: #409eff">
                  <Menu />
                </el-icon>
                <el-icon v-else style="margin-right: 4px; color: #67c23a">
                  <Key />
                </el-icon>
                <span>{{ data.label }}</span>
                <el-tag v-if="data.type === 'auth'" size="small" type="success" style="margin-left: 8px">
                  按钮
                </el-tag>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" :loading="permissionLoading" @click="savePermissions">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入组件 -->
    <ImportComponent
      :visible="importDialogVisible"
      title="导入角色"
      :download-template-api="organizationalApi.downloadRoleTemplate"
      :import-data-api="organizationalApi.importRoles"
      template-file-name="角色导入模板.xlsx"
      accept-file-types=".xlsx,.xls"
      :max-file-size="10"
      :show-download-template="true"
      @success="handleImportSuccess"
      @error="handleImportError"
      @update:visible="importDialogVisible = $event"
    />
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  :deep(.el-table .cell) {
    padding-right: 10px;
    padding-left: 10px;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f5f7fa;
  }

  :deep(.el-table--border .el-table__cell) {
    border-right: none;
  }

  :deep(.el-table__inner-wrapper::before) {
    display: none;
  }

  :deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
    background-color: #1e88e5;
  }

  .permission-tree-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
  }

  .permission-tree {
    background: transparent;
  }

  .custom-tree-node {
    display: flex;
    align-items: center;
    font-size: 14px;
  }
</style>
