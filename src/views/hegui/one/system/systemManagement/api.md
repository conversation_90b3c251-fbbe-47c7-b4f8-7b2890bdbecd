---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 05-法律法规管理服务/企业内部制度

## POST 批量从初始化制度添加到当前租户

POST /whiskerguardregulatoryservice/api/enterprise/regulations/initialize/batch

描述：批量从初始化制度添加到当前租户

> Body 请求参数

```json
[
  0
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|array[integer]| 否 |none|

> 返回示例

> 200 Response

```json
{
  "successCount": 0,
  "updatedCount": 0,
  "failedCount": 0,
  "failedRecords": [
    {
      "data": {
        "id": 0,
        "regulationCode": "",
        "title": "",
        "regulationType": "",
        "summary": "",
        "content": "",
        "department": "",
        "effectiveDate": "",
        "expireDate": "",
        "version": 0,
        "status": "",
        "metadata": "",
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "categoryId": 0,
        "attachments": [
          {
            "id": 0,
            "regulationId": 0,
            "fileName": "",
            "filePath": "",
            "attachmentType": "",
            "fileSize": "",
            "metadata": "",
            "version": 0,
            "uploadedBy": "",
            "uploadedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          }
        ],
        "regulationIds": [
          0
        ],
        "changeLog": "",
        "categoryName": "",
        "lawsRegulationList": [
          {
            "id": 0,
            "title": "",
            "summary": "",
            "detailUrl": "",
            "content": "",
            "province": "",
            "department": "",
            "code": "",
            "pubDate": "",
            "useDate": "",
            "isTime": "",
            "level": "",
            "lawType": "",
            "noUse": "",
            "modifyAccording": "",
            "changeReference": "",
            "status": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "attachments": [
              {
                "id": 0,
                "regulationId": 0,
                "fileName": "",
                "filePath": "",
                "attachmentType": "",
                "fileSize": "",
                "metadata": "",
                "version": 0,
                "uploadedBy": "",
                "uploadedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              }
            ],
            "categoryName": "",
            "isCollect": false,
            "transformStatus": ""
          }
        ],
        "isCollect": false
      },
      "error": ""
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityImportResultDTOEnterpriseRegulationDTO](#schemaresponseentityimportresultdtoenterpriseregulationdto)|

# 数据模型

<h2 id="tocS_LawsRegulationAttachmentDTO">LawsRegulationAttachmentDTO</h2>

<a id="schemalawsregulationattachmentdto"></a>
<a id="schema_LawsRegulationAttachmentDTO"></a>
<a id="tocSlawsregulationattachmentdto"></a>
<a id="tocslawsregulationattachmentdto"></a>

```json
{
  "id": 0,
  "regulationId": 0,
  "fileName": "string",
  "filePath": "string",
  "attachmentType": "IMPLEMENTATION_RULES",
  "fileSize": "string",
  "metadata": "string",
  "version": 0,
  "uploadedBy": "string",
  "uploadedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|regulationId|integer(int64)|true|none||关联法规ID|
|fileName|string|true|none||附件名称|
|filePath|string|true|none||附件存储路径或URL|
|attachmentType|string|false|none||附件类型：实施细则、表格模板、其它|
|fileSize|string|false|none||附件大小|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|uploadedBy|string|false|none||上传者|
|uploadedAt|[Instant](#schemainstant)|false|none||上传时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|attachmentType|IMPLEMENTATION_RULES|
|attachmentType|TABLE_TEMPLATE|
|attachmentType|OTHER|

<h2 id="tocS_LawsRegulationDTO">LawsRegulationDTO</h2>

<a id="schemalawsregulationdto"></a>
<a id="schema_LawsRegulationDTO"></a>
<a id="tocSlawsregulationdto"></a>
<a id="tocslawsregulationdto"></a>

```json
{
  "id": 0,
  "title": "string",
  "summary": "string",
  "detailUrl": "string",
  "content": "string",
  "province": "string",
  "department": "string",
  "code": "string",
  "pubDate": "string",
  "useDate": "string",
  "isTime": "string",
  "level": "string",
  "lawType": "string",
  "noUse": "string",
  "modifyAccording": "string",
  "changeReference": "string",
  "status": "DRAFT",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachments": [
    {
      "id": 0,
      "regulationId": 0,
      "fileName": "string",
      "filePath": "string",
      "attachmentType": "IMPLEMENTATION_RULES",
      "fileSize": "string",
      "metadata": "string",
      "version": 0,
      "uploadedBy": "string",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ],
  "categoryName": "string",
  "isCollect": true,
  "transformStatus": "未转化",
  "isRelated": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|title|string|true|none||内部制度标题|
|summary|string|false|none||内部制度摘要或概述|
|detailUrl|string|false|none||详细内容地址|
|content|string|true|none||法律法规详细内容，支持大文本存储|
|province|string|false|none||发布省份|
|department|string|false|none||适用部门或发布部门|
|code|string|true|none||发布的文件号|
|pubDate|string|false|none||发布日期|
|useDate|string|false|none||使用日期|
|isTime|string|false|none||状态：现行有效、失效|
|level|string|false|none||级别|
|lawType|string|false|none||法律类型|
|noUse|string|false|none||不使用通知|
|modifyAccording|string|false|none||修改依据|
|changeReference|string|false|none||变更参考|
|status|string|false|none||状态：草稿、发布、失效|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|attachments|[[LawsRegulationAttachmentDTO](#schemalawsregulationattachmentdto)]|false|none||附件列表|
|categoryName|string|false|none||分类名称|
|isCollect|boolean|false|none||是否收藏|
|transformStatus|string|false|none||转化状态|
|isRelated|boolean|true|none||是否关联|

#### 枚举值

|属性|值|
|---|---|
|status|DRAFT|
|status|PUBLISHED|
|status|EFFECTIVE|
|status|EXPIRED|
|status|REVIEWING|
|status|MODIFYING|

<h2 id="tocS_EnterpriseRegulationAttachmentDTO">EnterpriseRegulationAttachmentDTO</h2>

<a id="schemaenterpriseregulationattachmentdto"></a>
<a id="schema_EnterpriseRegulationAttachmentDTO"></a>
<a id="tocSenterpriseregulationattachmentdto"></a>
<a id="tocsenterpriseregulationattachmentdto"></a>

```json
{
  "id": 0,
  "regulationId": 0,
  "fileName": "string",
  "filePath": "string",
  "attachmentType": "IMPLEMENTATION_RULES",
  "fileSize": "string",
  "metadata": "string",
  "version": 0,
  "uploadedBy": "string",
  "uploadedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|regulationId|integer(int64)|true|none||关联内部法规ID|
|fileName|string|true|none||附件名称|
|filePath|string|true|none||附件存储路径或URL|
|attachmentType|string|true|none||附件类型|
|fileSize|string|false|none||附件大小|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|uploadedBy|string|false|none||上传者|
|uploadedAt|[Instant](#schemainstant)|false|none||上传时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|attachmentType|IMPLEMENTATION_RULES|
|attachmentType|TABLE_TEMPLATE|
|attachmentType|OTHER|

<h2 id="tocS_EnterpriseRegulationDTO">EnterpriseRegulationDTO</h2>

<a id="schemaenterpriseregulationdto"></a>
<a id="schema_EnterpriseRegulationDTO"></a>
<a id="tocSenterpriseregulationdto"></a>
<a id="tocsenterpriseregulationdto"></a>

```json
{
  "id": 0,
  "regulationCode": "string",
  "title": "string",
  "regulationType": "REGULATION",
  "summary": "string",
  "content": "string",
  "department": "string",
  "effectiveDate": "string",
  "expireDate": "string",
  "version": 0,
  "status": "DRAFT",
  "metadata": "string",
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "categoryId": 0,
  "attachments": [
    {
      "id": 0,
      "regulationId": 0,
      "fileName": "string",
      "filePath": "string",
      "attachmentType": "IMPLEMENTATION_RULES",
      "fileSize": "string",
      "metadata": "string",
      "version": 0,
      "uploadedBy": "string",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ],
  "regulationIds": [
    0
  ],
  "changeLog": "string",
  "categoryName": "string",
  "lawsRegulationList": [
    {
      "id": 0,
      "title": "string",
      "summary": "string",
      "detailUrl": "string",
      "content": "string",
      "province": "string",
      "department": "string",
      "code": "string",
      "pubDate": "string",
      "useDate": "string",
      "isTime": "string",
      "level": "string",
      "lawType": "string",
      "noUse": "string",
      "modifyAccording": "string",
      "changeReference": "string",
      "status": "DRAFT",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "attachments": [
        {
          "id": 0,
          "regulationId": 0,
          "fileName": "string",
          "filePath": "string",
          "attachmentType": "IMPLEMENTATION_RULES",
          "fileSize": "string",
          "metadata": "string",
          "version": 0,
          "uploadedBy": "string",
          "uploadedAt": {
            "seconds": null,
            "nanos": null
          },
          "isDeleted": true
        }
      ],
      "categoryName": "string",
      "isCollect": true,
      "transformStatus": "未转化",
      "isRelated": true
    }
  ],
  "isCollect": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|regulationCode|string|true|none||内部制度编号，用于唯一标识|
|title|string|true|none||内部制度标题|
|regulationType|string|false|none||内部制度类型|
|summary|string|false|none||内部制度摘要或概述|
|content|string|true|none||内部制度详细内容，支持大文本存储|
|department|string|false|none||适用部门或发布部门|
|effectiveDate|string|false|none||生效日期|
|expireDate|string|false|none||失效日期|
|version|integer|true|none||当前版本号|
|status|string|false|none||状态：草稿、发布、失效|
|metadata|string|false|none||补充字段|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|categoryId|integer|false|none||分类id|
|attachments|[[EnterpriseRegulationAttachmentDTO](#schemaenterpriseregulationattachmentdto)]|false|none||附件信息|
|regulationIds|[integer]|false|none||关联的法规|
|changeLog|string|false|none||变更说明|
|categoryName|string|false|none||分类名称|
|lawsRegulationList|[[LawsRegulationDTO](#schemalawsregulationdto)]|false|none||关联的法规信息|
|isCollect|boolean|false|none||是否收藏|

#### 枚举值

|属性|值|
|---|---|
|regulationType|REGULATION|
|regulationType|MEASURES|
|regulationType|CONDUCT|
|status|DRAFT|
|status|PUBLISHED|
|status|EFFECTIVE|
|status|EXPIRED|
|status|REVIEWING|
|status|MODIFYING|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_FailedRecordEnterpriseRegulationDTO">FailedRecordEnterpriseRegulationDTO</h2>

<a id="schemafailedrecordenterpriseregulationdto"></a>
<a id="schema_FailedRecordEnterpriseRegulationDTO"></a>
<a id="tocSfailedrecordenterpriseregulationdto"></a>
<a id="tocsfailedrecordenterpriseregulationdto"></a>

```json
{
  "data": {
    "id": 0,
    "regulationCode": "string",
    "title": "string",
    "regulationType": "REGULATION",
    "summary": "string",
    "content": "string",
    "department": "string",
    "effectiveDate": "string",
    "expireDate": "string",
    "version": 0,
    "status": "DRAFT",
    "metadata": "string",
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "categoryId": 0,
    "attachments": [
      {
        "id": 0,
        "regulationId": 0,
        "fileName": "string",
        "filePath": "string",
        "attachmentType": "IMPLEMENTATION_RULES",
        "fileSize": "string",
        "metadata": "string",
        "version": 0,
        "uploadedBy": "string",
        "uploadedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true
      }
    ],
    "regulationIds": [
      0
    ],
    "changeLog": "string",
    "categoryName": "string",
    "lawsRegulationList": [
      {
        "id": 0,
        "title": "string",
        "summary": "string",
        "detailUrl": "string",
        "content": "string",
        "province": "string",
        "department": "string",
        "code": "string",
        "pubDate": "string",
        "useDate": "string",
        "isTime": "string",
        "level": "string",
        "lawType": "string",
        "noUse": "string",
        "modifyAccording": "string",
        "changeReference": "string",
        "status": "DRAFT",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "attachments": [
          {
            "id": null,
            "regulationId": null,
            "fileName": null,
            "filePath": null,
            "attachmentType": null,
            "fileSize": null,
            "metadata": null,
            "version": null,
            "uploadedBy": null,
            "uploadedAt": null,
            "isDeleted": null
          }
        ],
        "categoryName": "string",
        "isCollect": true,
        "transformStatus": "未转化",
        "isRelated": true
      }
    ],
    "isCollect": true
  },
  "error": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|data|[EnterpriseRegulationDTO](#schemaenterpriseregulationdto)|false|none||none|
|error|string|false|none||none|

<h2 id="tocS_ResponseEntityImportResultDTOEnterpriseRegulationDTO">ResponseEntityImportResultDTOEnterpriseRegulationDTO</h2>

<a id="schemaresponseentityimportresultdtoenterpriseregulationdto"></a>
<a id="schema_ResponseEntityImportResultDTOEnterpriseRegulationDTO"></a>
<a id="tocSresponseentityimportresultdtoenterpriseregulationdto"></a>
<a id="tocsresponseentityimportresultdtoenterpriseregulationdto"></a>

```json
{
  "successCount": 0,
  "updatedCount": 0,
  "failedCount": 0,
  "failedRecords": "new ArrayList<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|successCount|integer|false|none||none|
|updatedCount|integer|false|none||none|
|failedCount|integer|false|none||none|
|failedRecords|[[FailedRecordEnterpriseRegulationDTO](#schemafailedrecordenterpriseregulationdto)]|false|none||none|

