<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Search } from '@element-plus/icons-vue'
// 路由实例
const router = useRouter()
const trainingPage = ref(1)
const trainingPageSize = ref(10)
const trainingTotal = ref(35)
const trainingFilters = ref({
  status: 'all',
  target: 'all',
  dateRange: [],
  keyword: '',
})
const trainingPlans = ref([
  {
    id: 1,
    name: '2024年第一季度合规培训计划',
    startDate: '2024-01-01',
    endDate: '2024-03-31',
    target: '全体员工',
    courseCount: 5,
    completionRate: 45,
    status: 'in_progress',
  },
  {
    id: 2,
    name: '新员工合规培训计划',
    startDate: '2024-01-15',
    endDate: '长期计划',
    target: '新员工',
    courseCount: 3,
    completionRate: 85,
    status: 'in_progress',
  },
  {
    id: 3,
    name: '反洗钱专项培训计划',
    startDate: '2024-02-15',
    endDate: '2024-03-15',
    target: '财务部门',
    courseCount: 2,
    completionRate: 100,
    status: 'completed',
  },
  {
    id: 4,
    name: '信息安全意识培训',
    startDate: '2024-03-01',
    endDate: '2024-03-31',
    target: '全体员工',
    courseCount: 4,
    completionRate: 32,
    status: 'in_progress',
  },
  {
    id: 5,
    name: '管理层领导力培训',
    startDate: '2024-01-10',
    endDate: '2024-02-10',
    target: '管理层',
    courseCount: 6,
    completionRate: 100,
    status: 'completed',
  },
  {
    id: 6,
    name: '产品知识培训',
    startDate: '2024-04-01',
    endDate: '2024-04-30',
    target: '销售部门',
    courseCount: 3,
    completionRate: 0,
    status: 'not_started',
  },
  {
    id: 7,
    name: '客户服务培训',
    startDate: '2024-02-01',
    endDate: '2024-02-28',
    target: '客服部门',
    courseCount: 5,
    completionRate: 78,
    status: 'in_progress',
  },
])
// Assessment Data
const assessmentPage = ref(1)
const assessmentPageSize = ref(10)
const assessmentTotal = ref(28)
const assessmentFilters = ref({
  type: 'all',
  status: 'all',
  dateRange: [],
  keyword: '',
})
const assessments = ref([
  {
    id: 1,
    name: '反洗钱法规考核',
    relatedCourse: '反洗钱合规培训',
    type: 'course',
    startDate: '2024-01-20',
    endDate: '2024-01-31',
    passScore: 80,
    participants: 150,
    passRate: 85,
    status: 'completed',
  },
  {
    id: 2,
    name: '2024年Q1合规知识测试',
    relatedCourse: '季度合规培训',
    type: 'regular',
    startDate: '2024-03-25',
    endDate: '2024-03-31',
    passScore: 70,
    participants: 200,
    passRate: 75,
    status: 'completed',
  },
  {
    id: 3,
    name: '新员工合规考核',
    relatedCourse: '新员工合规培训',
    type: 'course',
    startDate: '2024-04-01',
    endDate: '2024-04-30',
    passScore: 60,
    participants: 25,
    passRate: 0,
    status: 'in_progress',
  },
  {
    id: 4,
    name: '信息安全知识测试',
    relatedCourse: '信息安全意识培训',
    type: 'course',
    startDate: '2024-03-15',
    endDate: '2024-03-31',
    passScore: 75,
    participants: 180,
    passRate: 82,
    status: 'completed',
  },
  {
    id: 5,
    name: '领导力评估',
    relatedCourse: '管理层领导力培训',
    type: 'special',
    startDate: '2024-02-15',
    endDate: '2024-02-28',
    passScore: 70,
    participants: 35,
    passRate: 91,
    status: 'completed',
  },
  {
    id: 6,
    name: '产品知识考核',
    relatedCourse: '产品知识培训',
    type: 'course',
    startDate: '2024-04-15',
    endDate: '2024-04-30',
    passScore: 65,
    participants: 45,
    passRate: 0,
    status: 'not_started',
  },
])
// Helper functions
const customColors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#6f7ad3', percentage: 100 },
]
function getStatusType(status: string) {
  switch (status) {
    case 'not_started':
      return 'info'
    case 'in_progress':
      return ''
    case 'completed':
      return 'success'
    case 'overdue':
      return 'danger'
    default:
      return 'info'
  }
}
function getStatusText(status: string) {
  switch (status) {
    case 'not_started':
      return '未开始'
    case 'in_progress':
      return '进行中'
    case 'completed':
      return '已完成'
    case 'overdue':
      return '已逾期'
    default:
      return status
  }
}
function getAssessmentTypeText(type: string) {
  switch (type) {
    case 'course':
      return '课程考核'
    case 'regular':
      return '定期考核'
    case 'special':
      return '专项考核'
    default:
      return type
  }
}
function goAddEdit(item: any) {
  if (item?.id) {
    // 编辑考核
    router.push({
      name: '/training/assessment/edit',
      query: { id: item.id },
    })
  }
  else {
    // 新增考核
    router.push({
      name: '/training/assessment/edit',
    })
  }
}
// 查看考核详情
function goDetail(row) {
  router.push({
    name: '/training/assessment/detail',
    query: { id: row.id },
  })
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <!-- Assessment Section -->
    <div>
      <page-header>
        <template #content>
          <div class="aic jcsb flex">
            <div class="f-20 c-['#000000']">
              <h2 class="text-xl text-gray-800 font-semibold">
                考核认证
              </h2>
            </div>
            <div class="aic flex">
              <div>
                <el-button v-auth="'/training/assessment/insert'" v-debounce="3000" type="primary" @click="goAddEdit(null)">
                  <svg-icon name="ep:plus" />
                  <span class="ml-4">新增考核认证</span>
                </el-button>
              </div>
            </div>
          </div>
        </template>
      </page-header>
      <!-- Assessment Filter -->
      <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
        <div class="flex flex-wrap gap-4">
          <el-select
            v-model="assessmentFilters.type"
            placeholder="考核类型"
            class="w-40"
          >
            <el-option label="全部" value="all" />
            <el-option label="课程考核" value="course" />
            <el-option label="定期考核" value="regular" />
            <el-option label="专项考核" value="special" />
          </el-select>
          <el-select
            v-model="assessmentFilters.status"
            placeholder="考核状态"
            class="w-40"
          >
            <el-option label="全部" value="all" />
            <el-option label="未开始" value="not_started" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已结束" value="completed" />
          </el-select>
          <el-date-picker
            v-model="assessmentFilters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="w-64"
          />
          <el-input
            v-model="assessmentFilters.keyword"
            placeholder="搜索考核名称或关键词"
            class="w-64"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
      <!-- Assessment Table -->
      <div class="overflow-hidden rounded-lg bg-white shadow-sm">
        <el-table :data="assessments" style="width: 100%" border>
          <el-table-column prop="name" label="考核名称" />
          <el-table-column prop="relatedCourse" label="关联课程" />
          <el-table-column prop="type" label="考核类型">
            <template #default="{ row }">
              <el-tag size="small" effect="plain">
                {{ getAssessmentTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="startDate" label="开始时间" />
          <el-table-column prop="endDate" label="结束时间" />
          <el-table-column prop="passScore" label="及格分数" />
          <el-table-column prop="participants" label="参与人数" />
          <el-table-column prop="passRate" label="通过率">
            <template #default="{ row }">
              {{ row.passRate }}%
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag
                effect="light"
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="210">
            <template #default="{ row }">
              <el-button v-debounce="3000" type="primary" plain size="small" @click="goDetail(row)">
                查看
              </el-button>
              <el-button v-auth="'/training/assessment/edit'" v-debounce="3000" type="warning" plain size="small" @click="goAddEdit(row)">
                编辑
              </el-button>
              <el-button v-auth="'/training/assessment/delete'" v-debounce="3000" type="danger" plain size="small" class="text-red-500">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- Pagination -->
        <div class="flex items-center justify-between p-4">
          <span class="text-sm text-gray-600">
            共 {{ assessmentTotal }} 条记录
          </span>
          <el-pagination
            v-model:current-page="assessmentPage"
            v-model:page-size="assessmentPageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="assessmentTotal"
            layout="prev, pager, next, sizes"
          />
        </div>
      </div>
    </div>
  </div>
</template>

  <style scoped>
  :deep(.el-table__header) th {
  background-color: #f8f8f9 !important;
  font-weight: 500;
  }
  :deep(.el-tabs__item) {
  font-weight: 500;
  }
  :deep(.el-progress-bar__outer) {
  background-color: #f5f7fa;
  }
  :deep(.el-pagination) {
  justify-content: flex-end;
  }
  </style>
