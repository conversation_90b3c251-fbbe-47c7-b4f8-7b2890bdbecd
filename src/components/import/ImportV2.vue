<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Upload } from '@element-plus/icons-vue'

// 格式2的返回数据类型
interface ImportV2Result {
  success: boolean
  totalRows?: number       // 总行数
  successRows?: number     // 成功行数
  failedRows?: number      // 失败行数
  skippedRows?: number     // 跳过行数
  message?: string         // 消息
  successIds?: any[]       // 成功的ID列表
  errorDetails?: Array<{
    rowNumber: number
    fieldName: string
    fieldValue: string
    errorMessage: string
    errorType: string
  }>
}

interface ImportV2Props {
  // 弹窗显示状态
  visible?: boolean
  // 弹窗标题
  title?: string
  // 下载模板的API函数
  downloadTemplateApi?: () => Promise<any>
  // 导入数据的API函数 - 返回格式2
  importDataApi?: (file: File) => Promise<ImportV2Result>
  // 模板文件名
  templateFileName?: string
  // 支持的文件类型
  acceptFileTypes?: string
  // 最大文件大小(MB)
  maxFileSize?: number
  // 是否显示下载模板按钮
  showDownloadTemplate?: boolean
}

const props = withDefaults(defineProps<ImportV2Props>(), {
  visible: false,
  title: '数据导入',
  templateFileName: '导入模板.xlsx',
  acceptFileTypes: '.xlsx,.xls',
  maxFileSize: 10,
  showDownloadTemplate: true,
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [result: ImportV2Result]
  error: [error: any]
}>()

// 响应式数据
const downloading = ref(false)
const uploading = ref(false)
const uploadRef = ref()

// 关闭弹窗
function handleClose() {
  emit('update:visible', false)
}

// 下载模板
async function handleDownloadTemplate() {
  if (!props.downloadTemplateApi) {
    ElMessage.warning('未配置下载模板接口')
    return
  }

  try {
    downloading.value = true
    const response = await props.downloadTemplateApi()

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = props.templateFileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  }
  catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败，请重试')
  }
  finally {
    downloading.value = false
  }
}

// 文件上传前的校验
function beforeUpload(file: File) {
  // 检查文件类型
  const fileExtension = file.name.substring(file.name.lastIndexOf('.'))
  const allowedTypes = props.acceptFileTypes.split(',')
  if (!allowedTypes.includes(fileExtension)) {
    ElMessage.error(`只支持 ${props.acceptFileTypes} 格式的文件`)
    return false
  }

  // 检查文件大小
  const fileSizeMB = file.size / 1024 / 1024
  if (fileSizeMB > props.maxFileSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxFileSize}MB`)
    return false
  }

  return true
}

// 处理文件上传
async function handleFileUpload(file: File) {
  if (!props.importDataApi) {
    ElMessage.warning('未配置导入接口')
    return
  }

  try {
    uploading.value = true
    const result = await props.importDataApi(file)

    console.log('导入结果:', result)

    const totalRows = result.totalRows || 0
    const successRows = result.successRows || 0
    const failedRows = result.failedRows || 0
    const skippedRows = result.skippedRows || 0

    if (result.success) {
      // 导入成功
      let messageParts = []
      if (successRows > 0) messageParts.push(`成功 ${successRows} 条`)
      if (skippedRows > 0) messageParts.push(`跳过 ${skippedRows} 条`)

      const message = totalRows > 0
        ? `导入成功！共处理 ${totalRows} 条数据，${messageParts.join('，')}`
        : '导入成功！'

      ElMessage.success(message)
      emit('success', result)
      handleClose()
    } else {
      // 导入失败
      const message = result.message || '导入失败，所有数据都有错误'

      let detailMessage = message
      if (result.errorDetails && result.errorDetails.length > 0) {
        const errorList = result.errorDetails.slice(0, 3).map((error) => {
          let errorText = `第${error.rowNumber}行 ${error.fieldName}: ${error.errorMessage}`
          if (error.fieldValue) {
            errorText += `\n   值: ${error.fieldValue}`
          }
          return errorText
        }).join('\n\n')

        detailMessage += `\n\n错误详情:\n${errorList}`
        if (result.errorDetails.length > 3) {
          detailMessage += `\n\n... 还有 ${result.errorDetails.length - 3} 个错误`
        }
      }

      ElMessageBox.alert(
        detailMessage,
        '导入失败',
        {
          type: 'error',
          dangerouslyUseHTMLString: false,
        },
      )
      emit('error', result)
    }
  }
  catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请重试')
    emit('error', error)
  }
  finally {
    uploading.value = false
    // 清空文件选择
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
  }
}

// 自定义上传处理
function customUpload(options: any) {
  handleFileUpload(options.file)
  return Promise.resolve()
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    width="500px"
    @update:model-value="handleClose"
    @close="handleClose"
  >
    <div class="import-dialog-content">
      <div class="mb-4 flex items-center justify-center space-x-3">
        <!-- 下载模板按钮 -->
        <el-button
          v-if="showDownloadTemplate"
          :loading="downloading"
          @click="handleDownloadTemplate"
        >
          <el-icon class="mr-1">
            <Download />
          </el-icon>
          下载模板
        </el-button>

        <!-- 导入按钮 -->
        <el-upload
          ref="uploadRef"
          :show-file-list="false"
          :http-request="customUpload"
          :before-upload="beforeUpload"
          :accept="acceptFileTypes"
        >
          <el-button :loading="uploading" type="primary">
            <el-icon class="mr-1">
              <Upload />
            </el-icon>
            {{ uploading ? '导入中...' : '导入数据' }}
          </el-button>
        </el-upload>
      </div>

      <div class="text-sm text-gray-500 text-center">
        <p>支持文件格式：{{ acceptFileTypes }}</p>
        <p>文件大小限制：{{ maxFileSize }}MB</p>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped>
.import-dialog-content {
  padding: 16px 0;
}
</style>
