<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Document, Plus } from '@element-plus/icons-vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import problemTaskApi, { type ProblemInvestigateRecordDTO } from '@/api/problemTask'
import dictApi from '@/api/modules/system/dict'

defineOptions({
  name: 'TaskReview',
})

const route = useRoute()
const router = useRouter()

// 表单数据
const formData = reactive<ProblemInvestigateRecordDTO>({
  investigateId: null,
  recordCode: '',
  location: '',
  recordType: '',
  content: '',
  discover: '',
  attachmentList: [],
  involveList: [],
})

// 表单验证规则
const formRules = {
  recordCode: [{ required: true, message: '请输入记录编号', trigger: 'blur' }],
  recordType: [{ required: true, message: '请选择记录类型', trigger: 'change' }],
  location: [{ required: true, message: '请输入地点或渠道', trigger: 'blur' }],
  content: [{ required: true, message: '请输入记录内容', trigger: 'blur' }],
  discover: [{ required: true, message: '请输入问题发现', trigger: 'blur' }],
}

// 记录类型选项
const recordTypeOptions = ref([

])

// 涉及类型选项
const involveTypeOptions = [
  { label: '员工', value: 1 },
  { label: '部门', value: 2 },
]

// 控制状态
const loading = ref(false)
const formRef = ref()

// 页面初始化
onMounted(async () => {
  const investigateId = route.query.investigateId
  if (investigateId) {
    formData.investigateId = Number(investigateId)
  }

  // 生成记录编号
  await generateRecordCode()
  recordTypeOptions.value = await dictApi.dictAll(39)
})

// 生成记录编号
async function generateRecordCode() {
  try {
    const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    formData.recordCode = `REC${timestamp}${random}`
  }
  catch (error) {
    console.error('生成记录编号失败:', error)
    ElMessage.warning('生成记录编号失败，请手动输入')
  }
}

// 添加涉及人员
function addInvolveItem() {
  formData.involveList?.push({
    involve: 1,
    involveId: 0,
    remark: '',
  })
}

// 删除涉及人员
function removeInvolveItem(index: number) {
  formData.involveList?.splice(index, 1)
}

// 处理文件上传失败
function handleUploadError(error: any) {
  console.error('文件上传失败:', error)
  ElMessage.error('文件上传失败')
}

// 提交表单
async function submitForm() {
  if (!formRef.value) {
    return
  }

  try {
    const valid = await formRef.value.validate()
    if (!valid) {
      return
    }

    // 验证涉及人员数据
    if (formData.involveList) {
      for (let i = 0; i < formData.involveList.length; i++) {
        const item = formData.involveList[i]
        if (!item.involveId || item.involveId <= 0) {
          ElMessage.error(`请输入第${i + 1}个涉及人员的有效ID`)
          return
        }
      }
    }

    loading.value = true

    const result = await problemTaskApi.createInvestigateRecord(formData)

    if (result) {
      ElMessage.success('调查记录提交成功')
      router.back()
    }
  }
  catch (error) {
    console.error('提交调查记录失败:', error)
    ElMessage.error('提交失败，请重试')
  }
  finally {
    loading.value = false
  }
}

// 重置表单
function resetForm() {
  if (!formRef.value) {
    return
  }
  formRef.value.resetFields()
  formData.attachmentList = []
  formData.involveList = []
}

// 返回上一页
function goBack() {
  router.back()
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航栏 -->
    <div class="bg-white shadow-sm">
      <div class="mx-auto max-w-7xl px-4 py-4">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-500">
          <span>应对之翼</span>
          <i class="el-icon-arrow-right mx-2 text-xs" />
          <span>违规问题调查</span>
          <i class="el-icon-arrow-right mx-2 text-xs" />
          <span>调查任务管理</span>
          <i class="el-icon-arrow-right mx-2 text-xs" />
          <span class="text-gray-800 font-medium">调查记录</span>
        </div>

        <!-- 页面标题和操作按钮 -->
        <div class="mt-4 flex items-center justify-between">
          <h1 class="text-xl font-bold">
            调查记录
          </h1>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" :loading="loading" @click="submitForm">
              <i class="el-icon-check mr-1" />提交调查记录
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="goBack">
              <i class="el-icon-close mr-1" />取消
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-7xl px-4 py-6">
      <!-- 基本信息编辑区 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            基本信息
          </div>
        </template>
        <el-form ref="formRef" :model="formData" :rules="formRules" label-position="right" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="记录编号" prop="recordCode">
                <el-input v-model="formData.recordCode" placeholder="请输入记录编号" class="h-8" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="记录类型" prop="recordType">
                <el-select v-model="formData.recordType" placeholder="请选择记录类型" class="h-8 w-full">
                  <el-option
                    v-for="option in recordTypeOptions"
                    :key="option.value"
                    :label="option.name"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地点或渠道" prop="location">
                <el-input v-model="formData.location" placeholder="请输入地点或渠道" class="h-8" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 调查内容编辑区 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            调查内容
          </div>
        </template>
        <el-form :model="formData" :rules="formRules" label-position="right" label-width="120px">
          <el-form-item label="记录内容" prop="content">
            <el-input
              v-model="formData.content"
              type="textarea"
              :rows="4"
              placeholder="请输入记录内容"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="问题发现" prop="discover">
            <el-input
              v-model="formData.discover"
              type="textarea"
              :rows="3"
              placeholder="请输入关键发现"
              maxlength="300"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 附件上传区 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            附件上传
          </div>
        </template>
        <UploadMbb
          v-model="formData.attachmentList"
          :max="1"
          :size="10"
          :auto-upload="true"
          :use-file-path="true"
          service-name="whiskerguardviolationservice"
          category-name="investigate-record"
          tip-text="支持上传图片、文档等文件，单个文件不超过10MB"
          @upload-error="handleUploadError"
        />
      </el-card>

      <!-- 涉及人员区 -->
      <el-card v-if="false" class="mb-6">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="text-base font-bold">
              涉及人员
            </div>
            <el-button size="small" type="primary" @click="addInvolveItem">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>
              添加人员
            </el-button>
          </div>
        </template>

        <div v-if="!formData.involveList?.length" class="py-8 text-center text-gray-500">
          暂无涉及人员
        </div>

        <div v-else class="space-y-4">
          <el-card
            v-for="(item, index) in formData.involveList"
            :key="index"
            class="relative"
            shadow="never"
          >
            <el-form :model="formData" label-position="right" label-width="120px">
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="涉及类型">
                    <el-select v-model="item.involve" placeholder="请选择涉及类型" class="h-8 w-full">
                      <el-option
                        v-for="option in involveTypeOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="人员ID">
                    <el-input-number
                      v-model="item.involveId"
                      placeholder="请输入人员ID"
                      class="h-8 w-full"
                      :min="1"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="备注">
                    <el-input
                      v-model="item.remark"
                      placeholder="请输入备注"
                      class="h-8"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

            <el-button
              type="danger"
              size="small"
              class="absolute right-2 top-2"
              @click="removeInvolveItem(index)"
            >
              <el-icon>
                <Delete />
              </el-icon>
            </el-button>
          </el-card>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.el-card {
  border: 1px solid #ebeef5;
}

.el-card :deep(.el-card__body) {
  padding: 16px;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.md\:grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.md\:grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.md\:col-span-2 {
  grid-column: span 2 / span 2;
}

.gap-4 {
  gap: 1rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }
}
</style>
