// 学习培训相关类型定义

// 时间戳类型
export interface Instant {
  seconds: number
  nanos: number
}

// 章节进度
export interface ChapterProgressDTO {
  chapterId: number
  chapterTitle: string
  chapterOrder: number
  durationMinutes: number
  studiedDurationSeconds: number
  completionStatus: string
  isCompleted: boolean
  lastAccessTime: Instant
  completedAt: Instant
}

// 课程进度
export interface CourseProgressDTO {
  userId: string
  courseId: number
  courseName: string
  coverImageUrl: string
  totalDurationMinutes: number
  studiedDurationSeconds: number
  studiedDurationMinutes: number
  progressPercent: number
  totalChapters: number
  completedChapters: number
  chapterCompletionRate: number
  isCompleted: boolean
  startTime: Instant
  lastAccessTime: Instant
  completedAt: Instant
  accessCount: number
  currentChapterId: number
  currentChapterTitle: string
  chapterProgresses: ChapterProgressDTO[]
}

// 月度学习统计
export interface MonthlyLearningStatsDTO {
  userId: string
  year: number
  month: number
  date: string
  learningDurationSeconds: number
  learningDurationMinutes: number
  learningDurationHours: number
  learningDays: number
  completedChapters: number
  completedCourses: number
  behaviorCount: number
  averageDailyLearningMinutes: number
}

// 课程进度汇总
export interface CourseProgressSummaryDTO {
  courseId: number
  courseName: string
  completedCount: number
  totalCount: number
  completionRate: number
}

// 部门进度
export interface DepartmentProgressDTO {
  deptId: string
  deptName: string
  memberCount: number
  completedCount: number
  completionRate: number
}

// 培训计划进度
export interface TrainingPlanProgressDTO {
  planId: number
  planName: string
  planCode: string
  trainingTarget: string
  responsiblePerson: string
  planStatus: string
  startDate: Instant
  endDate: Instant
  targetCompletionRate: number
  totalCourses: number
  totalParticipants: number
  completedCourseInstances: number
  totalCourseInstances: number
  overallCompletionRate: number
  averageProgress: number
  completedParticipants: number
  inProgressParticipants: number
  notStartedParticipants: number
  courseProgresses: CourseProgressSummaryDTO[]
  departmentProgresses: DepartmentProgressDTO[]
}

// 学习进度统计主要数据结构
export interface LearningProgressStatisticsDTO {
  userId: string
  statisticsTime: Instant
  totalLearningDurationSeconds: number
  totalLearningDurationHours: number
  monthlyLearningDurationSeconds: number
  monthlyLearningDurationHours: number
  totalCourses: number
  completedCourses: number
  inProgressCourses: number
  notStartedCourses: number
  courseCompletionRate: number
  totalChapters: number
  completedChapters: number
  chapterCompletionRate: number
  learningDays: number
  averageDailyLearningMinutes: number
  lastLearningTime: Instant
  consecutiveLearningDays: number
  activityLevel: string
  behaviorStats: Record<string, number>
  recentWeekStats: MonthlyLearningStatsDTO[]
  courseProgresses: CourseProgressDTO[]
  trainingPlanProgresses: TrainingPlanProgressDTO[]
}

// API响应包装类型
export interface ResponseEntityLearningProgressStatisticsDTO {
  data: LearningProgressStatisticsDTO
  message?: string
  code?: number
  success?: boolean
}

// 推荐课程类型（根据index.vue中的显示字段定义）
export interface RecommendedCourse {
  id: number
  courseName: string
  coverImageUrl: string
  courseType: string
  totalDurationMinutes: number
  durationHours: string // 格式化后的时长显示
  reason?: string // 推荐理由
  instructor?: string // 讲师
  rating?: number // 评分
}

// 课程列表查询参数
export interface CourseQueryParams {
  userId?: string
  courseName?: string
  courseType?: string
  status?: string
  page?: number
  size?: number
}

// 学习进度查询参数
export interface LearningProgressQueryParams {
  userId?: string
}

// 法规更新相关类型定义
export interface RegulationUpdate {
  id: number
  title: string
  content: string
  publishTime: string
  updateTime: string
  status: string
  category: string
  source: string
  summary?: string
}

// 法规更新查询参数
export interface RegulationQueryParams {
  page: number
  size: number
  title?: string
  category?: string
  status?: string
}

// 系统通知相关类型定义
export interface SystemNotification {
  id: number
  title: string
  content: string
  category: string
  priority: string
  status: string
  createTime: string
  updateTime: string
  userId?: string
  isRead?: boolean
}

// 系统通知查询参数
export interface NotificationQueryParams {
  page: number
  size: number
  category: string
  userId?: string
  status?: string
}

// API响应包装类型
export interface ApiResponse<T> {
  data: T
  message?: string
  code?: number
  success?: boolean
}

// 分页响应类型
export interface PageResponse<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 待办事件相关类型定义
export interface TodoEvent {
  id: number
  tenantId: number
  assigneeId: number
  businessId?: number
  eventType: string
  title: string
  description?: string
  status?: string
  approvalProcessId?: string
  approvalInstanceId?: string
  completedTime?: Instant
  metadata?: string
  version?: number
  createdBy?: string
  createdAt?: Instant
  updatedBy?: string
  updatedAt?: Instant
  isDeleted?: boolean
}

// 今日待办事件统计
export interface TodayTodoStats {
  todayPendingCount: number
  thisWeekCompletedCount: number
  totalTaskCompletionRate?: string // 任务完成率
  events?: TodoEvent[] // 具体的待办事件列表
}

// 待办事件查询参数
export interface TodoEventQueryParams {
  page?: number
  size?: number
  status?: string
  eventType?: string
  assigneeId?: number
}

// 岗位职责相关类型定义
export interface PositionDutyItem {
  orgUnitName: string // 部门名称
  postName: string // 岗位名称
  basicDuty: string // 基本职责
  complianceRequirement: string // 合规要求
  controlMeasures: string // 防控措施
  effectiveDate: string // 职责生效日期
  lastUpdateTime: string // 最后更新时间
  riskLevel: string // 岗位风险等级
  postStatus: string // 岗位状态
}

// 岗位职责统计响应
export interface DutyPositionStatsResponse {
  employeeName: string // 员工姓名
  employeeId: number // 员工ID
  positionDuties: PositionDutyItem[] // 岗位职责列表
}