<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Upload, UploadFilled } from '@element-plus/icons-vue'
import qaApi from '@/api/complianceApi/one/qa'
import useUserStore from '@/store/modules/user'
import PageHeader from '@/components/PageHeader/index.vue'
import PageMain from '@/components/PageMain/index.vue'
import MarkdownRenderer from '@/components/MarkdownRenderer/index.vue'

// 用户信息
const userStore = useUserStore()

// 响应式数据
const currentModelId = ref('')
const modelList = ref([])
const chatMessages = ref([])
const inputMessage = ref('')
const isAnswering = ref(false)
const conversationId = ref('')

// 文件管理
const uploadedFiles = ref([])
const selectedFileId = ref('')
const showUploadDialog = ref(false)
const uploadFileList = ref([])
const uploading = ref(false)

// 文件预览
const showPreviewDialog = ref(false)
const previewContent = ref('')
const previewLoading = ref(false)

// DOM引用
const messagesContainer = ref()
const uploadRef = ref()

// 计算属性
const userAvatar = computed(() => userStore.userDetail?.avatar || '/src/assets/images/user-avatar.png')
const aiAvatar = computed(() => '/src/assets/images/ai-avatar.png')

// 方法
async function loadModelList() {
  try {
    const res = await qaApi.getModelList()
    if (res && res.length > 0) {
      modelList.value = res.filter(model => model.status === 'AVAILABLE')
      if (modelList.value.length > 0) {
        // 默认选择第一个模型
        currentModelId.value = modelList.value[0].id
      }
    }
  }
  catch (error) {
    console.error('加载模型列表失败:', error)
    ElMessage.error('加载模型列表失败')
  }
}

async function getConversationId() {
  try {
    const res = await qaApi.getSessionId()
    conversationId.value = res?.conversationId || ''
  }
  catch (error) {
    console.error('获取会话ID失败:', error)
  }
}

function onModelChange(modelId: string) {
  currentModelId.value = modelId
  ElMessage.success('模型切换成功')
}

async function sendMessage() {
  if (!inputMessage.value.trim() || isAnswering.value) { return }

  // if (!currentModelId.value) {
  //   ElMessage.warning('请先选择AI模型')
  //   return
  // }

  const userMessage = {
    isUser: true,
    name: userStore.userDetail?.realName || '用户',
    content: inputMessage.value,
    time: new Date().toLocaleString(),
  }

  chatMessages.value.push(userMessage)
  const question = inputMessage.value
  inputMessage.value = ''

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 开始AI回答
  await doAnswer(question)
}

async function doAnswer(question: string) {
  isAnswering.value = true

  // 添加思考中的消息
  const thinkingMessage = {
    isUser: false,
    name: 'AI助手',
    content: '思考中...',
    time: new Date().toLocaleString(),
  }
  chatMessages.value.push(thinkingMessage)

  await nextTick()
  scrollToBottom()

  try {
    const params = {
      toolKey: 'kimi',
      prompt: question,
      employeeId: userStore.userId,
      streaming: true,
      conversationId: conversationId.value,
      fileId: selectedFileId.value || undefined,
    }

    const response = await qaApi.queryChatStream(params)

    // 处理流式响应
    let fullAnswer = ''
    let displayedAnswer = ''

    // 流式输出效果
    const processStreamData = (data: string) => {
      fullAnswer += data

      // 打字机效果
      const typewriterEffect = () => {
        if (displayedAnswer.length < fullAnswer.length) {
          displayedAnswer += fullAnswer[displayedAnswer.length]
          chatMessages.value[chatMessages.value.length - 1].content = displayedAnswer
          setTimeout(typewriterEffect, 30) // 稍微快一点的打字效果
        }
        else {
          isAnswering.value = false
        }
      }

      typewriterEffect()
    }

    // 处理实际的API响应
    chatMessages.value[chatMessages.value.length - 1].content = ''

    // 检查响应数据格式并处理
    if (response) {
      try {
        // 如果是字符串格式的SSE响应
        if (typeof response === 'string') {
          // 解析SSE格式的数据
          const lines = response.split('\n')
          let combinedContent = ''

          for (const line of lines) {
            if (line.startsWith('data:')) {
              try {
                const jsonStr = line.substring(5).trim() // 移除 "data:" 前缀
                if (jsonStr) {
                  const data = JSON.parse(jsonStr)
                  if (data.content) {
                    combinedContent += data.content
                  }
                }
              }
              catch (parseError) {
                console.warn('解析SSE数据失败:', parseError)
              }
            }
          }

          if (combinedContent) {
            processStreamData(combinedContent)
          }
          else {
            processStreamData('收到响应但内容为空')
          }
        }
        // 如果是对象格式
        else if (response.content) {
          processStreamData(response.content)
        }
        // 如果是其他格式
        else {
          processStreamData(JSON.stringify(response))
        }
      }
      catch (error) {
        console.error('处理响应数据失败:', error)
        processStreamData('响应数据处理失败，请重试')
      }
    }
    else {
      // 如果没有响应数据，使用默认消息
      processStreamData('抱歉，没有收到有效的回答，请重试。')
    }
  }
  catch (error) {
    console.error('AI回答失败:', error)
    chatMessages.value[chatMessages.value.length - 1].content = '抱歉，回答出现错误，请重试。'
    isAnswering.value = false
    ElMessage.error('AI回答失败')
  }
}

function scrollToBottom() {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 文件管理方法
function handleFileChange(file: any) {
  // 文件变化处理
}

function beforeUpload(file: File) {
  const isValidType = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/markdown',
    'application/javascript',
    'text/javascript',
    'application/json',
  ].includes(file.type)

  if (!isValidType) {
    ElMessage.error('不支持的文件格式')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB')
    return false
  }

  return false // 阻止自动上传
}

async function confirmUpload() {
  if (uploadFileList.value.length === 0) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  uploading.value = true

  try {
    for (const fileItem of uploadFileList.value) {
      const res = await qaApi.uploadFile(fileItem.raw)
      uploadedFiles.value.push({
        id: res.fileId,
        name: fileItem.name,
        size: fileItem.size,
        type: getFileType(fileItem.name),
        uploadTime: new Date().toLocaleString(),
      })
    }

    ElMessage.success('文件上传成功')
    showUploadDialog.value = false
    resetUploadDialog()
  }
  catch (error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败')
  }
  finally {
    uploading.value = false
  }
}

function resetUploadDialog() {
  uploadFileList.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

function selectFile(fileId: string) {
  selectedFileId.value = fileId
  ElMessage.success('已选择文档作为对话上下文')
}

async function previewFile(fileId: string) {
  showPreviewDialog.value = true
  previewLoading.value = true

  try {
    const res = await qaApi.previewFile(fileId)
    previewContent.value = res.content || '无法预览此文件内容'
  }
  catch (error) {
    console.error('文件预览失败:', error)
    previewContent.value = ''
  }
  finally {
    previewLoading.value = false
  }
}

async function deleteFile(fileId: string) {
  try {
    await ElMessageBox.confirm('确定要删除这个文件吗？', '确认删除', {
      type: 'warning',
    })

    await qaApi.deleteFile(fileId)
    uploadedFiles.value = uploadedFiles.value.filter(file => file.id !== fileId)

    if (selectedFileId.value === fileId) {
      selectedFileId.value = ''
    }

    ElMessage.success('文件删除成功')
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('文件删除失败:', error)
      ElMessage.error('文件删除失败')
    }
  }
}

function getFileType(fileName: string) {
  const ext = fileName.split('.').pop()?.toLowerCase()
  if (['pdf'].includes(ext)) { return 'pdf' }
  if (['doc', 'docx'].includes(ext)) { return 'doc' }
  if (['ppt', 'pptx'].includes(ext)) { return 'ppt' }
  if (['xls', 'xlsx'].includes(ext)) { return 'xls' }
  return 'txt'
}

function formatFileSize(bytes: number) {
  if (bytes === 0) { return '0 B' }
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
}

// 生命周期
onMounted(async () => {
  await getConversationId()
  await loadModelList()
})
</script>

<template>
  <div class="ai-qa-container">
    <!-- 页面头部 -->
    <PageHeader title="AI智能问答" />

    <!-- 主要内容区域 -->
    <PageMain>
      <div class="chat-layout">
        <!-- 左侧文件管理面板 -->
        <div class="file-panel">
          <div class="file-panel-header">
            <h3>文档管理</h3>
            <el-button type="primary" size="small" @click="showUploadDialog = true">
              <el-icon>
                <Upload />
              </el-icon>
              上传文档
            </el-button>
          </div>

          <div class="file-list">
            <div v-if="uploadedFiles.length === 0" class="empty-files">
              <el-empty description="暂无上传文档" />
            </div>
            <div v-else>
              <div
                v-for="file in uploadedFiles" :key="file.id" class="file-item"
                :class="{ active: selectedFileId === file.id }" @click="selectFile(file.id)"
              >
                <div class="file-info">
                  <el-icon class="file-icon">
                    <Document v-if="file.type === 'pdf'" />
                    <Document v-else-if="file.type === 'doc'" />
                    <Document v-else-if="file.type === 'ppt'" />
                    <Document v-else-if="file.type === 'xls'" />
                    <Document v-else />
                  </el-icon>
                  <div class="file-details">
                    <div class="file-name">
                      {{ file.name }}
                    </div>
                    <div class="file-size">
                      {{ formatFileSize(file.size) }}
                    </div>
                  </div>
                </div>
                <div class="file-actions">
                  <el-button type="text" size="small" @click.stop="previewFile(file.id)">
                    预览
                  </el-button>
                  <el-button type="text" size="small" @click.stop="deleteFile(file.id)">
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧聊天区域 -->
        <div class="chat-panel">
          <!-- 模型选择器 -->
          <div class="model-selector">
            <span class="w-[100px]">AI模型：</span>
            <el-select v-model="currentModelId" placeholder="选择AI模型" @change="onModelChange">
              <el-option v-for="model in modelList" :key="model.id" :label="model.name" :value="model.id" />
            </el-select>
          </div>

          <!-- 聊天消息区域 -->
          <div ref="messagesContainer" class="chat-messages">
            <div v-if="chatMessages.length === 0" class="empty-chat">
              <el-empty description="开始您的AI对话吧！" />
            </div>
            <div v-else>
              <div
                v-for="(message, index) in chatMessages" :key="index" class="message-item"
                :class="{ 'user-message': message.isUser, 'ai-message': !message.isUser }"
              >
                <div class="message-avatar">
                  <el-avatar :src="message.isUser ? userAvatar : aiAvatar" :size="40" />
                </div>
                <div class="message-content">
                  <div class="message-header">
                    <span class="message-name">{{ message.name }}</span>
                    <span class="message-time">{{ message.time }}</span>
                  </div>
                  <div class="message-body">
                    <!-- AI消息使用Markdown渲染 -->
                    <MarkdownRenderer v-if="!message.isUser" :content="message.content" class="markdown-content" />
                    <!-- 用户消息使用普通文本 -->
                    <div v-else class="text-content">
                      {{ message.content }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="chat-input-area">
            <div class="input-container">
              <el-input
                v-model="inputMessage" type="textarea" :rows="3" placeholder="请输入您的问题..."
                :disabled="isAnswering" @keydown.ctrl.enter="sendMessage"
              />
              <div class="input-actions">
                <el-button type="primary" :loading="isAnswering" :disabled="!inputMessage.trim()" @click="sendMessage">
                  {{ isAnswering ? '回答中...' : '发送' }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageMain>

    <!-- 文件上传对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传文档" width="600px" @close="resetUploadDialog">
      <el-upload
        ref="uploadRef" class="upload-demo" drag :auto-upload="false" :on-change="handleFileChange"
        :before-upload="beforeUpload" :file-list="uploadFileList" multiple
        accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.md,.js,.ts,.vue,.html,.css,.json"
      >
        <el-icon class="el-icon--upload">
          <UploadFilled />
        </el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 PDF、DOC、PPT、XLS、TXT 及代码文件，单个文件不超过 10MB
          </div>
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUploadDialog = false">取消</el-button>
          <el-button type="primary" :loading="uploading" @click="confirmUpload">
            确认上传
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog v-model="showPreviewDialog" title="文件预览" width="80%" top="5vh">
      <div class="file-preview-content">
        <div v-if="previewLoading" class="preview-loading">
          <el-loading text="加载中..." />
        </div>
        <div v-else-if="previewContent" class="preview-text">
          <pre>{{ previewContent }}</pre>
        </div>
        <div v-else class="preview-error">
          <el-empty description="无法预览此文件" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.ai-qa-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-layout {
  display: flex;
  height: calc(100vh - 160px);
  gap: 16px;
  min-width: 0; /* 防止flex子项撑开父容器 */
  overflow: hidden; /* 防止内容溢出 */
}

.file-panel {
  width: 320px;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.file-panel-header {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.file-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-files {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;

  &:hover {
    border-color: #409eff;
    background-color: #f0f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }

  &.active {
    border-color: #409eff;
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.file-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.file-icon {
  margin-right: 8px;
  color: #606266;
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.chat-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  background: #fff;
  min-width: 0; /* 允许收缩 */
  overflow: hidden; /* 防止内容溢出 */
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.model-selector {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.model-label {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-chat {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-item {
  display: flex;
  margin-bottom: 24px;
  animation: fadeInUp 0.3s ease-out;

  &.user-message {
    flex-direction: row-reverse;

    .message-content {
      margin-right: 12px;
      margin-left: 0;
    }

    .message-header {
      text-align: right;
    }

    .message-body {
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      color: white;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }
  }

  &.ai-message {
    .message-content {
      margin-left: 12px;
    }

    .message-body {
      background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);
      color: #303133;
      border: 1px solid #e4e7ed;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  max-width: 70%;
  min-width: 0; /* 允许内容收缩 */
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.message-header {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-name {
  font-weight: 500;
  font-size: 14px;
  color: #303133;
}

.message-time {
  font-size: 12px;
  color: #909399;
}

.message-body {
  padding: 12px 16px;
  border-radius: 12px;
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  box-sizing: border-box;
}

.markdown-content {
  /* Markdown样式由MarkdownRenderer组件处理 */
  max-width: 100%;
  overflow-x: auto;
  word-wrap: break-word;
  overflow-wrap: break-word;

  /* 处理Markdown中的代码块和表格 */
  :deep(pre) {
    max-width: 100%;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  :deep(table) {
    max-width: 100%;
    table-layout: fixed;
    word-wrap: break-word;
  }

  :deep(code) {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
  }
}

.text-content {
  word-break: break-word;
}

.chat-input-area {
  border-top: 1px solid #e4e7ed;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-actions .el-button {
  min-width: 100px;
}

.file-preview-content {
  max-height: 60vh;
  overflow-y: auto;
}

.preview-loading {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-text {
  pre {
    white-space: pre-wrap;
    word-break: break-word;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
  }
}

.preview-error {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-dialog {
  .el-dialog__header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__body {
    padding: 30px;
  }

  .upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    background: #fafafa;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    .el-icon {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }

    .upload-text {
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .upload-tip {
    margin-top: 16px;
    color: #909399;
    font-size: 12px;
    text-align: center;
  }
}
</style>
