<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import systemApi from '@/api/complianceApi/one/systemManagement'

const router = useRouter()
onMounted(() => {
  getList()
})
// 搜索参数
const searchParams = ref({
  title: null,
  isTime: null,
})

// 表格数据
const tableData = ref([])

// 分页数据
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0,
})

// 加载状态
const loading = ref(false)

// 订阅状态管理
const subscribingIds = ref(new Set<number>())

// 组织架构数据（示例）
const groupList = ref([
  {
    value: '1',
    label: '银保监会',
    children: [
      { value: '1-1', label: '银行监管部' },
      { value: '1-2', label: '保险监管部' },
    ],
  },
  {
    value: '2',
    label: '商务部',
    children: [
      { value: '2-1', label: '市场建设司' },
      { value: '2-2', label: '外贸司' },
    ],
  },
  {
    value: '3',
    label: '证监会',
    children: [
      { value: '3-1', label: '市场监管部' },
      { value: '3-2', label: '发行监管部' },
    ],
  },
])

const props1 = {
  expandTrigger: 'hover' as const,
  value: 'value',
  label: 'label',
  children: 'children',
}

// 获取列表数据
async function getList() {
  try {
    loading.value = true

    const params = {
      ...searchParams.value,
      page: pagination.value.page,
      limit: pagination.value.limit,
    }

    const response = await systemApi.tenantRegulations(params)

    if (response) {
      tableData.value = response.content
      pagination.value.total = response.totalElements || 0
    }
  }
  catch (error) {
    ElMessage.error('获取租户法规列表失败')
  }
  finally {
    loading.value = false
  }
}

// 搜索功能
function handleSearch() {
  pagination.value.page = 1
  getList()
}

// 重置搜索
function resetSearch() {
  searchParams.value = {
    title: null,
    isTime: null,
  }
  pagination.value.page = 1
  getList()
}

// 查看详情
function viewDetail(row: any) {
  router.push({
    path: '/database/laws/detail',
    query: { id: row.lawsRegulation.id },
  })
}

// 订阅
async function subscribe(row: any) {
  // 防止重复点击
  if (subscribingIds.value.has(row.id)) {
    return
  }

  try {
    subscribingIds.value.add(row.id)

    // 检查是否已订阅
    if (row.isCollect) {
      // 已订阅，执行取消订阅
      await unsubscribe(row)
      return
    }

    // 执行订阅
    const params = {
      regulationId: row.id,
    }

    await systemApi.isSubscribe(params)
    ElMessage.success(`已订阅：${row.title}`)

    // 更新本地状态
    row.isCollect = true
  }
  catch (error) {
    console.error('订阅失败:', error)
    ElMessage.error('订阅失败，请稍后重试')
  }
  finally {
    subscribingIds.value.delete(row.id)
  }
}

// 取消订阅
async function unsubscribe(row: any) {
  try {
    await ElMessageBox.confirm(
      `确定要取消订阅「${row.title}」吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await systemApi.deleteSubscribe(row.id)
    ElMessage.success(`已取消订阅：${row.title}`)

    // 更新本地状态
    row.isCollect = false
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('取消订阅失败:', error)
      ElMessage.error('取消订阅失败，请稍后重试')
    }
  }
}

// 编辑
function editRegulation(row: any) {
  router.push({
    path: '/database/laws/addEdit',
    query: { id: row.id },
  })
}

// 删除
async function deleteRegulation(row: any) {
  try {
    await ElMessageBox.confirm(
      `确定要删除「${row.lawsRegulation?.title || row.title}」吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 调用删除订阅接口，传入regulationId
    await systemApi.deleteSubscribe(row.regulationId)
    ElMessage.success('删除成功')
    getList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

// 更多操作
function moreActions(row: any, action: string) {
  switch (action) {
    case 'edit':
      editRegulation(row)
      break
    case 'delete':
      deleteRegulation(row)
      break
    case 'download':
      ElMessage.info(`下载：${row.title}`)
      // 这里后续添加下载逻辑
      break
  }
}

// 分页改变
function handlePageChange(page: number) {
  pagination.value.page = page
  getList()
}

function handleSizeChange(size: number) {
  pagination.value.limit = size
  pagination.value.page = 1
  getList()
}

// 格式化状态
function formatStatus(isTime: string) {
  return isTime === '现行有效' ? 'success' : 'danger'
}

// 格式化级别
function formatLevel(level: string) {
  const levelMap: Record<string, string> = {
    法律: 'danger',
    行政法规: 'warning',
    部门规章: 'primary',
    规范性文件: 'info',
  }
  return levelMap[level] || 'info'
}

// 组织架构变化
function changegroup(_value: any) {
  // 处理组织架构变化
}

// 批量导出
function batchExport() {
  ElMessage.info('批量导出功能开发中...')
}
</script>

<template>
  <div class="absolute-container">
    <PageHeader>
      <template #content>
        <div class="card flex p-2">
          <!-- <div>
            <el-button type="primary" plain @click="batchExport">
              <svg-icon name="ep:download" />
              <span class="ml-4">批量导出</span>
            </el-button>
          </div> -->
          <div class="ml-4">
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item label="名称:">
                <el-input v-model="searchParams.title" style="width: 200px;" clearable placeholder="搜索法规名称..." @clear="searchParams.title = null" />
              </el-form-item>

              <el-form-item label="全部状态:" style="width: 200px;">
                <el-select v-model="searchParams.isTime" clearable placeholder="请选择状态" @clear="searchParams.isTime = null">
                  <el-option label="现行有效" value="现行有效" />
                  <el-option label="失效" value="失效" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" :loading="loading" @click="handleSearch">
                  <template #icon>
                    <svg-icon name="ep:search" />
                  </template>
                  查询
                </el-button>
                <el-button @click="resetSearch">
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
    </PageHeader>
    <PageMain style="background-color: transparent;">
      <div class="conBox mt-10">
        <div class="rounded-lg bg-white px-6 py-4 shadow-sm">
          <!-- 列表头部 -->
          <div class="mb-6 flex items-center justify-between border-b border-gray-200 pb-4">
            <div>
              <h3 class="text-lg text-gray-800 font-semibold">
                专属法规列表
              </h3>
              <div class="mt-1 text-sm text-gray-500">
                共 {{ pagination.total }} 条记录
              </div>
            </div>
          </div>

          <!-- 表格 -->
          <el-table
            v-loading="loading"
            :data="tableData"
            stripe
            class="w-full"
            :header-cell-style="{ backgroundColor: '#f8fafc', color: '#374151', fontWeight: '500' }"
            :row-style="{ transition: 'all 0.2s' }"
          >
            <el-table-column prop="lawsRegulation.title" label="法规名称" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <el-link type="primary" :underline="false" style="color: black;" @click="viewDetail(row)">
                  {{ row.lawsRegulation.title }}
                </el-link>
                <!-- <div
                  class="cursor-pointer text-blue-600 font-medium transition-colors duration-200 hover:text-blue-800"
                  @click="viewDetail(row)"
                >
                  {{ row.lawsRegulation?.title }}
                </div> -->
              </template>
            </el-table-column>

            <el-table-column prop="lawsRegulation.department" label="发布部门" width="120">
              <template #default="{ row }">
                <span class="text-gray-700">{{ row.lawsRegulation?.department }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="lawsRegulation.province" label="发布省份" width="100">
              <template #default="{ row }">
                <span class="text-gray-700">{{ row.lawsRegulation?.province }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="lawsRegulation.code" label="文件号" width="150" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="text-sm text-gray-600 font-mono">{{ row.lawsRegulation?.code }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="lawsRegulation.pubDate" label="发布日期" width="110">
              <template #default="{ row }">
                <span class="text-sm text-gray-600">{{ row.lawsRegulation?.pubDate }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="lawsRegulation.useDate" label="使用日期" width="110">
              <template #default="{ row }">
                <span class="text-sm text-gray-600">{{ row.lawsRegulation?.useDate }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="lawsRegulation.isTime" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="formatStatus(row.lawsRegulation?.isTime)" size="small" class="font-medium">
                  {{ row.lawsRegulation?.isTime }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="lawsRegulation.level" show-overflow-tooltip label="级别" width="120">
              <template #default="{ row }">
                <el-tag :type="formatLevel(row.lawsRegulation?.level)" size="small" class="font-medium">
                  {{ row.lawsRegulation?.level }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="180" fixed="right">
              <template #default="{ row }">
                <div class="flex flex-wrap items-center gap-2">
                  <el-button
                    v-auth="['database/tenantRegulations/detail']"
                    type="primary" plain
                    size="small"
                    class="mb-1"
                    @click="viewDetail(row)"
                  >
                    查看详情
                  </el-button>
                  <el-button
                    v-auth="['database/tenantRegulations/del']"
                    type="danger" plain
                    size="small"
                    class="mb-1"
                    @click="deleteRegulation(row)"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="mt-6 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.limit"
              :page-sizes="[10, 20, 50, 100]"
              :small="false"
              :disabled="loading"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.total"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
            />
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";
</style>
