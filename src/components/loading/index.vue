<script setup lang="ts">
// 基于 SpinKit https://tobiasahlin.com/spinkit/
import 'spinkit/spinkit.min.css'

/**
 * Loading组件使用示例：
 *
 * 1. 进度条loading（推荐）：
 * loadingRef.value.open(true, {
 *   type: 'progress',
 *   text: '正在处理中...',
 *   subText: '正在进行智能分析，请稍候'
 * })
 *
 * 2. 自定义圆形loading：
 * loadingRef.value.open(true, { type: 'custom' })
 *
 * 3. SpinKit loading：
 * loadingRef.value.open(true, {
 *   type: 'chase',
 *   size: 50,
 *   color: '#409eff',
 *   background: 'rgba(0,0,0,0.7)'
 * })
 *
 * 4. 关闭loading：
 * loadingRef.value.close()
 */

defineOptions({
  name: 'SpinkitLoading',
})

const show: any = ref(false)
// type?: 'plane' | 'chase' | 'bounce' | 'wave' | 'pulse' | 'flow' | 'swing' | 'circle' | 'circle-fade' | 'grid' | 'fold' | 'wander' | 'progress'
const type: any = ref('plane')
const size: any = ref(50)
const color: any = ref('#fff')
const fixed: any = ref(true)
const background: any = ref('#000000')

// 进度条相关状态
const progress = ref(0)
const estimatedTime = ref(5)
const loadingText = ref('正在处理中...')
const loadingSubText = ref('正在进行智能分析，请稍候')
const progressInterval = ref<any>(null)

// 模拟进度条动画
function simulateProgress() {
  progress.value = 0
  estimatedTime.value = Math.floor(Math.random() * 3) + 4 // 4-6秒随机

  const stages = [
    { end: 20, speed: 0.8, texts: ['正在初始化...', '正在连接服务器...'] },
    { end: 45, speed: 0.6, texts: ['正在分析数据...', '正在处理请求...'] },
    { end: 70, speed: 0.4, texts: ['正在生成结果...', '正在优化内容...'] },
    { end: 90, speed: 0.2, texts: ['正在完善细节...', '即将完成...'] },
    { end: 95, speed: 0.1, texts: ['最后检查...', '准备就绪...'] }
  ]

  let currentStage = 0

  progressInterval.value = setInterval(() => {
    if (progress.value < 95 && currentStage < stages.length) {
      const stage = stages[currentStage]

      // 模拟真实的进度变化：不同阶段不同速度
      const baseIncrement = stage.speed + Math.random() * 0.3
      const randomFactor = Math.random() < 0.3 ? 0.5 : 1 // 偶尔暂停一下
      const increment = baseIncrement * randomFactor

      progress.value = Math.min(stage.end, progress.value + increment)

      // 更新预计时间（非线性递减）
      const progressRatio = progress.value / 100
      const timeReduction = 0.05 + (progressRatio * 0.1)
      estimatedTime.value = Math.max(0, estimatedTime.value - timeReduction)

      // 随机更新加载文本（每个阶段有特定的文本）
      if (Math.random() < 0.15) {
        const stageTexts = stage.texts
        loadingSubText.value = stageTexts[Math.floor(Math.random() * stageTexts.length)]
      }

      // 检查是否进入下一阶段
      if (progress.value >= stage.end) {
        currentStage++
      }
    }
  }, 150 + Math.random() * 100) // 随机间隔，更真实
}

function open(time: any, system: any) {
  show.value = true
  if (system) {
    type.value = system.type ? system.type : 'plane'
    size.value = system.size ? system.size : 50
    color.value = system.color ? system.color : '#fff'
    background.value = system.background ? system.background : '#000'
    fixed.value = system.fixed
    loadingText.value = system.text || '正在处理中...'
    loadingSubText.value = system.subText || '正在进行智能分析，请稍候'
  }
  else {
    type.value = 'custom'
  }

  // 如果是进度条类型，启动进度模拟
  if (type.value === 'progress') {
    simulateProgress()
  }

  if (time) {
    if (time !== true) {
      setTimeout(() => {
        show.value = false
        if (progressInterval.value) {
          clearInterval(progressInterval.value)
        }
      }, time)
    }
  }
  else {
    setTimeout(() => {
      show.value = false
      if (progressInterval.value) {
        clearInterval(progressInterval.value)
      }
    }, 0)
  }
}

// 手动关闭
function close() {
  show.value = false
  if (progressInterval.value) {
    clearInterval(progressInterval.value)
  }
}

// 抛出方法
defineExpose({
  open,
  close,
})
</script>

<template>
  <!-- 自定义圆形loading -->
  <div v-if="show && type === 'custom'" class="loaderbox">
    <div class="loader" />
    <div class="text">
      Loading...
    </div>
  </div>

  <!-- 新的进度条loading -->
  <div v-if="show && type === 'progress'" class="progress-loading-container">
    <div class="progress-loading-modal">
      <div class="progress-loading-content">
        <!-- Logo with rotation animation -->
        <div class="logo-container">
          <img
            src="https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png"
            alt="Logo"
            class="logo-image"
          >
        </div>

        <!-- Title -->
        <h3 class="loading-title">
          {{ loadingText }}
        </h3>
        <p class="loading-subtitle">
          {{ loadingSubText }}
        </p>

        <!-- Progress bar -->
        <div class="progress-container">
          <div class="progress-info">
            <span>任务进度</span>
            <span>{{ Math.round(progress) }}%</span>
          </div>
          <div class="progress-bar-bg">
            <div
              class="progress-bar-fill"
              :style="{ width: `${progress}%` }"
            />
          </div>
        </div>

        <!-- Estimated time -->
        <p class="estimated-time">
          预计剩余时间: {{ Math.ceil(estimatedTime) }}秒
        </p>
      </div>
    </div>
  </div>

  <!-- 原有的SpinKit loading -->
  <transition v-if="show && type !== 'custom' && type !== 'progress'" name="spinkit-transition">
    <div class="spinkit-container" :style="{ position: fixed ? 'fixed' : 'absolute', background }">
      <div class="spinkit" :style="{ '--sk-size': `${size}px`, '--sk-color': color }">
        <div v-if="type === 'plane'" class="sk-plane" />
        <div v-if="type === 'chase'" class="sk-chase">
          <div class="sk-chase-dot" />
          <div class="sk-chase-dot" />
          <div class="sk-chase-dot" />
          <div class="sk-chase-dot" />
          <div class="sk-chase-dot" />
          <div class="sk-chase-dot" />
        </div>
        <div v-if="type === 'bounce'" class="sk-bounce">
          <div class="sk-bounce-dot" />
          <div class="sk-bounce-dot" />
        </div>
        <div v-if="type === 'wave'" class="sk-wave">
          <div class="sk-wave-rect" />
          <div class="sk-wave-rect" />
          <div class="sk-wave-rect" />
          <div class="sk-wave-rect" />
          <div class="sk-wave-rect" />
        </div>
        <div v-if="type === 'pulse'" class="sk-pulse" />
        <div v-if="type === 'flow'" class="sk-flow">
          <div class="sk-flow-dot" />
          <div class="sk-flow-dot" />
          <div class="sk-flow-dot" />
        </div>
        <div v-if="type === 'swing'" class="sk-swing">
          <div class="sk-swing-dot" />
          <div class="sk-swing-dot" />
        </div>
        <div v-if="type === 'circle'" class="sk-circle">
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
        </div>
        <div v-if="type === 'circle-fade'" class="sk-circle-fade">
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
        </div>
        <div v-if="type === 'grid'" class="sk-grid">
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
        </div>
        <div v-if="type === 'fold'" class="sk-fold">
          <div class="sk-fold-cube" />
          <div class="sk-fold-cube" />
          <div class="sk-fold-cube" />
          <div class="sk-fold-cube" />
        </div>
        <div v-if="type === 'wander'" class="sk-wander">
          <div class="sk-wander-cube" />
          <div class="sk-wander-cube" />
          <div class="sk-wander-cube" />
        </div>
      </div>
    </div>
  </transition>
</template>

<style scoped lang="scss">
  /* stylelint-disable */
  .loaderbox {
    background-color: rgba($color: #f5f5f5, $alpha: 1);
    z-index: 9999;
    position: absolute;
    right: 0 !important;
    left: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .loader {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    position: relative;
    border: 4px solid transparent;
    border-top-color: #4bc0c8;
    animation: run 2s linear infinite;
  }

  /* 利用伪元素 */
  .loader::before {
    content: "";
    position: absolute;
    inset: 5px;
    border-radius: 50%;
    border: 4px solid transparent;
    border-top-color: #c779d0;

    /* 动画时间不同 */
    animation: run 3s linear infinite;
  }

  .loader::after {
    content: "";
    position: absolute;
    inset: 15px;
    border-radius: 50%;
    border: 4px solid transparent;
    border-top-color: #feac5e;

    /* 动画时间不同 */
    animation: run 1.5s linear infinite;
  }

  .text {
    color: var(--el-text-color-primary);
    font-size: 12px;
    position: absolute;
  }

  /* 定义动画 */
  @keyframes run {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  /* 系统样式 */

  .spinkit-container {
    /* position: fixed; */
    width: 100%;
    inset: 0;
    z-index: 10001;
    // background-color: rgb(0 0 0 / 70%);
  }

  .spinkit {
    @include position-center(xy);
  }

  .spinkit-transition-leave-active,
  .spinkit-transition-enter-active {
    transition: all 0.3s;
  }

  .spinkit-transition-enter,
  .spinkit-transition-leave-to {
    opacity: 0;
  }

  /* 进度条loading样式 */
  .progress-loading-container {
    position: fixed;
    inset: 0;
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }

  .progress-loading-modal {
    width: 450px;
    overflow: hidden;
    border-radius: 12px;
    background: white;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    animation: modalSlideIn 0.3s ease-out;
  }

  .progress-loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
  }

  .logo-container {
    margin-bottom: 1.5rem;
    animation: logoSpin 2s linear infinite;
  }

  .logo-image {
    height: 4rem;
    width: 4rem;
    border-radius: 50%;
    object-fit: cover;
  }

  .loading-title {
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
  }

  .loading-subtitle {
    margin-bottom: 1.5rem;
    color: #6b7280;
    text-align: center;
  }

  .progress-container {
    width: 100%;
    margin-bottom: 1rem;
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
  }

  .progress-bar-bg {
    height: 8px;
    width: 100%;
    overflow: hidden;
    border-radius: 9999px;
    background-color: #e5e7eb;
  }

  .progress-bar-fill {
    height: 100%;
    background: linear-gradient(45deg, #60a5fa, #a855f7, #ec4899);
    background-size: 200% 100%;
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 9999px;
    position: relative;
    animation: gradientShift 3s ease-in-out infinite;
  }

  .progress-bar-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 9999px;
    animation: progressShimmer 2s ease-in-out infinite;
  }

  .progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: progressPulse 1s ease-in-out infinite;
  }

  .estimated-time {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
  }

  /* 动画定义 */
  @keyframes logoSpin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes progressShimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes gradientShift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  @keyframes progressPulse {
    0%, 100% {
      opacity: 0.6;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.5);
    }
  }
</style>
