<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import experienceApi from '@/api/monitor/exprience'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import HandlingMeasuresSelector from '@/components/HandlingMeasuresSelector/index.vue'
import dictApi from '@/api/modules/system/dict'

const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref()

// 页面状态
const loading = ref(false)
const isEdit = computed(() => !!route.query.id)

// 页面标题
const pageTitle = computed(() => {
  return isEdit.value ? '编辑经验教训' : '新增经验教训'
})

// 表单数据 - 根据接口文档字段结构
const formData = ref({
  id: null,
  tenantId: null,
  title: '', // 标题
  experienceCode: '', // 编号
  experienceType: '', // 类型：PROCESS_ISSUE、PERSONNEL_ISSUE、INSTITUTIONAL_ISSUE、SYSTEM_ISSUE、OTHER
  experienceSource: '', // 来源：AUDIT_FINDING、EMPLOYEE_SUBMISSION、PROCESSING_RESULT
  level: '', // 优先级：LOW、MIDDLE、HIGH
  sourceCode: '', // 来源编号
  sourceName: '', // 来源名称（用于显示）
  dutyEmployeeOrgId: null, // 责任部门id
  label: '', // 标签，以逗号分隔
  status: 'DRAFT', // 状态：DRAFT、PUBLISHED、SAVE
  problemBackground: '', // 问题背景
  eventCourse: '', // 事件经过
  problemAnalysis: '', // 问题分析
  reasonCategory: '', // 原因分类，以逗号分隔
  experienceLearned: '', // 经验教训
  improvementSuggestion: '', // 改进建议
  metadata: '', // 补充字段
  attachmentList: [], // 附件列表
})

// 选项数据
const typeOptions = ref([
])

const sourceOptions = ref([

])

const levelOptions = ref([
  { label: '低', value: 'LOW' },
  { label: '中', value: 'MIDDLE' },
  { label: '高', value: 'HIGH' },
])

// 原因分类选项
const reasonCategories = ref([
  { label: '人员因素', value: '人员因素' },
  { label: '流程因素', value: '流程因素' },
  { label: '制度因素', value: '制度因素' },
  { label: '系统因素', value: '系统因素' },
  { label: '外部因素', value: '外部因素' },
  { label: '其他', value: '其他' },
])

const selectedReasonCategories = ref([])

// 标签相关
const tags = ref([])
const tagInput = ref('')

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  experienceCode: [{ required: true, message: '请输入编号', trigger: 'blur' }],
  experienceType: [{ required: true, message: '请选择类型', trigger: 'change' }],
  experienceSource: [{ required: true, message: '请选择来源', trigger: 'change' }],
  level: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  dutyEmployeeOrgId: [{ required: true, message: '请选择责任部门', trigger: 'change' }],
  problemBackground: [{ required: true, message: '请输入问题背景', trigger: 'blur' }],
  eventCourse: [{ required: true, message: '请输入事件经过', trigger: 'blur' }],
  problemAnalysis: [{ required: true, message: '请输入问题分析', trigger: 'blur' }],
  experienceLearned: [{ required: true, message: '请输入经验教训', trigger: 'blur' }],
  improvementSuggestion: [{ required: true, message: '请输入改进建议', trigger: 'blur' }],
}

// 经验来源
async function fetchDictOptions() {
  try {
    // 获取调查类型选项 (34)
    const typeResponse = await dictApi.dictAll(27)
    const sourceResponse = await dictApi.dictAll(28)
    if (typeResponse) {
      sourceOptions.value = typeResponse
    }
    if (sourceResponse) {
      typeOptions.value = sourceResponse
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
    ElMessage.error('获取字典数据失败')
  }
}
// 自动生成编号
async function generateNumber() {
  try {
    loading.value = true
    const response = await dictApi.getCode('CONTINUOUS_EXPERIENCE')
    if (response) {
      formData.value.experienceCode = response
    }
  }
  catch (error) {
    ElMessage.error('编号生成失败')
  }
  finally {
    loading.value = false
  }
}

// 处理关联来源选择变化
function handleSourceChange(value: any, row: any) {
  console.log('选中的处理措施:', value, row)
  if (row) {
    // 设置来源编号和名称
    formData.value.sourceCode = value
    formData.value.sourceName = row.title
    ElMessage.success(`已关联处理措施: ${row.title}`)
  }
  else {
    // 清空时同时清空两个字段
    formData.value.sourceCode = ''
    formData.value.sourceName = ''
  }
}

// 添加标签
function addTag() {
  if (tagInput.value && !tags.value.includes(tagInput.value)) {
    tags.value.push(tagInput.value)
    formData.value.label = tags.value.join(',')
    tagInput.value = ''
  }
}

// 移除标签
function removeTag(tag: string) {
  const index = tags.value.indexOf(tag)
  if (index > -1) {
    tags.value.splice(index, 1)
    formData.value.label = tags.value.join(',')
  }
}

// 更新原因分类
function updateReasonCategories() {
  formData.value.reasonCategory = selectedReasonCategories.value.join(',')
}

// 文件上传成功回调
function handleUploadSuccess(_fileInfo: any) {
  // 附件上传成功，循环数据添加attachmentType字段
  formData.value.attachmentList.forEach((item) => {
    if (!item.relatedType) {
      item.relatedType = 1
    }
  })
}
// 文件上传失败回调
function handleUploadError(error: any) {
  ElMessage.error('文件上传失败')
  console.error('上传错误:', error)
}

// 保存草稿
async function saveDraft() {
  try {
    loading.value = true
    formData.value.status = 'DRAFT'

    if (isEdit.value) {
      await experienceApi.updateExperience(route.query.id as string, formData.value)
      ElMessage.success('草稿保存成功')
    }
    else {
      await experienceApi.createExperience(formData.value)
      ElMessage.success('草稿保存成功')
      router.push('/hegui/respond/improveAndOptimize/experienceAndLesson')
    }
  }
  catch (error) {
    ElMessage.error('保存失败')
  }
  finally {
    loading.value = false
  }
}

// 发布
async function publish() {
  try {
    // 验证表单
    await formRef.value?.validate()

    loading.value = true
    formData.value.status = 'PUBLISHED'

    if (isEdit.value) {
      await experienceApi.updateExperience(route.query.id as string, formData.value)
      ElMessage.success('发布成功')
    }
    else {
      await experienceApi.createExperience(formData.value)
      ElMessage.success('发布成功')
      router.push('/hegui/respond/improveAndOptimize/experienceAndLesson')
    }
  }
  catch (error) {
    if (error === false) {
      ElMessage.error('请完善必填信息')
    }
    else {
      ElMessage.error('发布失败')
    }
  }
  finally {
    loading.value = false
  }
}

// 保存
async function save() {
  try {
    // 验证表单
    await formRef.value?.validate()

    loading.value = true

    if (isEdit.value) {
      await experienceApi.updateExperience(route.query.id as string, formData.value)
      ElMessage.success('保存成功')
      router.back()
    }
    else {
      await experienceApi.createExperience(formData.value)
      ElMessage.success('保存成功')
      router.back()
    }
  }
  catch (error) {
    if (error === false) {
      ElMessage.error('请完善必填信息')
    }
    else {
      ElMessage.error('保存失败')
    }
  }
  finally {
    loading.value = false
  }
}

// 取消
function cancel() {
  ElMessageBox.confirm('确定要取消吗？未保存的数据将丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    router.back()
  })
}

// 预览
function preview() {
  ElMessage.info('预览功能待实现')
}

// 初始化数据
async function initData() {
  fetchDictOptions()
  if (isEdit.value) {
    try {
      loading.value = true
      const response = await experienceApi.getExperienceDetail(route.query.id as string)
      if (response) {
        Object.assign(formData.value, response)
        // 处理标签
        if (formData.value.label) {
          tags.value = formData.value.label.split(',')
        }
        // 处理原因分类
        if (formData.value.reasonCategory) {
          selectedReasonCategories.value = formData.value.reasonCategory.split(',')
        }
      }
    }
    catch (error) {
      ElMessage.error('获取数据失败')
    }
    finally {
      loading.value = false
    }
  }
  else {
    generateNumber()
  }
}

// 页面初始化
onMounted(() => {
  initData()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{ pageTitle }}
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" :loading="loading" @click="save">
              <i class="el-icon-check mr-1" />保存
            </el-button>
            <!-- <el-button plain class="!rounded-button whitespace-nowrap" :loading="loading" @click="saveDraft">
              <i class="el-icon-check mr-1" />保存草稿
            </el-button> -->
            <el-button plain class="!rounded-button whitespace-nowrap" @click="cancel">
              <i class="el-icon-close mr-1" />返回
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <div class="min-h-screen bg-gray-50">
          <!-- 主内容区 -->
          <div class="mx-auto flex px-6 py-8 container">
            <!-- 左侧编辑区 -->
            <div class="w-4/4 pr-6">
              <!-- 基本信息卡片 -->
              <div class="mb-6 rounded-lg bg-white p-20 shadow-sm">
                <h2 class="mb-6 text-lg font-bold">
                  基本信息
                </h2>
                <div class="grid grid-cols-2 gap-6">
                  <div>
                    <label class="mb-1 block text-sm text-gray-600">标题 <span class="text-red-500">*</span></label>
                    <el-input v-model="formData.title" />
                  </div>
                  <div class="flex items-end">
                    <div class="flex-1">
                      <label class="mb-1 block text-sm text-gray-600">编号</label>
                      <el-input v-model="formData.experienceCode" readonly />
                    </div>
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-600">类型 <span class="text-red-500">*</span></label>
                    <el-select v-model="formData.experienceType" class="w-full">
                      <el-option
                        v-for="item in typeOptions"
                        :key="item.value"
                        :label="item.name"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-600">来源</label>
                    <el-select v-model="formData.experienceSource" class="w-full">
                      <el-option
                        v-for="item in sourceOptions"
                        :key="item.value"
                        :label="item.name"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                  <div class="col-span-2">
                    <label class="mb-1 block text-sm text-gray-600">关联来源</label>
                    <HandlingMeasuresSelector
                      v-model="formData.sourceCode"
                      :display-value="formData.sourceName"
                      value-key="dealCode"
                      display-key="title"
                      placeholder="请选择关联的处理措施"
                      @change="handleSourceChange"
                    />
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-600">优先级 <span class="text-red-500">*</span></label>
                    <el-select v-model="formData.level" class="w-full" placeholder="请选择优先级">
                      <el-option
                        v-for="item in levelOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-600">责任部门</label>
                    <DepartmentTreeSelect
                      v-model="formData.dutyEmployeeOrgId"
                      placeholder="请选择责任部门"
                      clearable
                    />
                  </div>
                </div>
              </div>
              <!-- 内容编辑区 -->
              <div class="mb-6 rounded-lg bg-white p-20 shadow-sm">
                <h2 class="mb-6 text-lg font-bold">
                  内容编辑
                </h2>
                <!-- 问题背景 -->
                <div class="mb-6">
                  <div class="mb-3 flex items-center justify-between">
                    <h3 class="text-sm font-bold">
                      问题背景
                    </h3>
                    <!-- <span class="text-xs text-gray-500">建议 200-300 字</span> -->
                  </div>
                  <div class="border border-gray-300 rounded p-3">
                    <el-input v-model="formData.problemBackground" type="textarea" :rows="8" placeholder="请输入问题背景" />
                  </div>
                </div>
                <!-- 事件经过 -->
                <div class="mb-6">
                  <div class="mb-3 flex items-center justify-between">
                    <h3 class="text-sm font-bold">
                      事件经过
                    </h3>
                  </div>
                  <div class="border border-gray-300 rounded p-3">
                    <el-input v-model="formData.eventCourse" type="textarea" :rows="10" placeholder="请输入事件经过" />
                  </div>
                </div>
                <!-- 问题分析 -->
                <div class="mb-6">
                  <div class="mb-3 flex items-center justify-between">
                    <h3 class="text-sm font-bold">
                      问题分析
                    </h3>
                  </div>
                  <div class="border border-gray-300 rounded p-3">
                    <el-input v-model="formData.problemAnalysis" type="textarea" :rows="10" placeholder="请输入问题分析" />
                    <div class="mt-3">
                      <h4 class="mb-2 text-xs font-medium">
                        原因分类
                      </h4>
                      <div class="grid">
                        <el-checkbox-group v-model="selectedReasonCategories" @change="updateReasonCategories">
                          <el-checkbox v-for="item in reasonCategories" :key="item.value" :label="item.value">
                            {{ item.label }}
                          </el-checkbox>
                        </el-checkbox-group>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 经验教训 -->
                <div class="mb-6">
                  <div class="mb-3 flex items-center justify-between">
                    <h3 class="text-sm font-bold">
                      经验教训
                    </h3>
                  </div>
                  <div class="border border-gray-300 rounded p-3">
                    <el-input v-model="formData.experienceLearned" type="textarea" :rows="10" placeholder="请输入经验教训" />
                  </div>
                </div>
                <!-- 改进建议 -->
                <div class="mb-6">
                  <div class="mb-3 flex items-center justify-between">
                    <h3 class="text-sm font-bold">
                      改进建议
                    </h3>
                  </div>
                  <div class="border border-gray-300 rounded p-3">
                    <el-input v-model="formData.improvementSuggestion" type="textarea" :rows="10" placeholder="请输入改进建议" />
                  </div>
                </div>
              </div>
              <!-- 相关资料 -->
              <div class="mb-6 rounded-lg bg-white p-20 shadow-sm">
                <h2 class="mb-6 text-lg font-bold">
                  相关资料
                </h2>
                <UploadMbb
                  v-model="formData.attachmentList"
                  :max="10"
                  :size="10"
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png"
                  tip-text="支持 PDF、DOC、XLS、PPT、JPG、PNG 格式文件，大小不超过 10MB"
                  service-name="whiskerguardviolationservice"
                  category-name="experience"
                  :auto-upload="true"
                  :use-file-path="true"
                  @upload-success="handleUploadSuccess"
                  @upload-error="handleUploadError"
                />
              </div>
              <!-- 关联信息 -->
              <div v-if="false" class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                <h2 class="mb-6 text-lg font-bold">
                  关联信息
                </h2>
                <div class="space-y-4">
                  <div>
                    <h3 class="mb-2 text-sm font-medium">
                      关联风险
                    </h3>
                    <div class="border border-gray-300 rounded p-3">
                      <div class="mb-2 flex items-center justify-between">
                        <span class="text-xs text-gray-500">已关联 0 个风险</span>
                        <button

                          class="!rounded-button whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                        >
                          添加风险
                        </button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 class="mb-2 text-sm font-medium">
                      关联制度
                    </h3>
                    <div class="border border-gray-300 rounded p-3">
                      <div class="mb-2 flex items-center justify-between">
                        <span class="text-xs text-gray-500">已关联 0 个制度</span>
                        <button

                          class="!rounded-button whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                        >
                          添加制度
                        </button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 class="mb-2 text-sm font-medium">
                      关联岗位
                    </h3>
                    <div class="border border-gray-300 rounded p-3">
                      <div class="mb-2 flex items-center justify-between">
                        <span class="text-xs text-gray-500">已关联 0 个岗位</span>
                        <button

                          class="!rounded-button whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                        >
                          添加岗位
                        </button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 class="mb-2 text-sm font-medium">
                      关联流程
                    </h3>
                    <div class="border border-gray-300 rounded p-3">
                      <div class="mb-2 flex items-center justify-between">
                        <span class="text-xs text-gray-500">已关联 0 个流程</span>
                        <button

                          class="!rounded-button whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                        >
                          添加流程
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 发布设置 -->
              <div v-if="false" class="rounded-lg bg-white p-6 shadow-sm">
                <h2 class="mb-6 text-lg font-bold">
                  发布设置
                </h2>
                <div class="space-y-4">
                  <div>
                    <h3 class="mb-2 text-sm font-medium">
                      发布范围
                    </h3>
                    <div class="flex space-x-4">
                      <label class="flex items-center">
                        <el-radio v-model="formData.publishScope" label="company" />
                        <span class="text-sm">全公司</span>
                      </label>
                      <label class="flex items-center">
                        <el-radio v-model="formData.publishScope" label="department" />
                        <span class="text-sm">指定部门</span>
                      </label>
                      <label class="flex items-center">
                        <el-radio v-model="formData.publishScope" label="department" />
                        <span class="text-sm">指定人员</span>
                      </label>
                    </div>
                  </div>
                  <div>
                    <label class="flex items-center">
                      <el-checkbox v-model="formData.isImportant" />
                      <span class="text-sm">标记为重要经验教训</span>
                    </label>
                  </div>
                  <div>
                    <label class="flex items-center">
                      <el-checkbox v-model="formData.isImportant" />
                      <span class="text-sm">发布时推送通知</span>
                    </label>
                    <div class="ml-6 mt-2 border-l-2 border-gray-200 pl-4">
                      <div class="mb-2">
                        <h4 class="mb-1 text-xs font-medium">
                          通知对象
                        </h4>
                        <div class="flex">
                          <el-input v-model="formData.metadata" />
                          <button

                            class="!rounded-button h-8 whitespace-nowrap border border-l-0 border-gray-300 rounded-r bg-gray-100 px-3"
                          >
                            选择
                          </button>
                        </div>
                      </div>
                      <div class="mb-2">
                        <h4 class="mb-1 text-xs font-medium">
                          通知方式
                        </h4>
                        <div class="grid grid-cols-2 gap-2">
                          <label class="flex items-center">
                            <el-checkbox v-model="formData.isImportant" />
                            <span class="text-xs">系统消息</span>
                          </label>
                          <label class="flex items-center">
                            <el-checkbox v-model="formData.isImportant" />
                            <span class="text-xs">电子邮件</span>
                          </label>
                          <label class="flex items-center">
                            <el-checkbox v-model="formData.isImportant" />
                            <span class="text-xs">短信</span>
                          </label>
                          <label class="flex items-center">
                            <el-checkbox v-model="formData.isImportant" />
                            <span class="text-xs">其他</span>
                          </label>
                        </div>
                      </div>
                      <div>
                        <h4 class="mb-1 text-xs font-medium">
                          通知内容
                        </h4>
                        <el-input
                          v-model="formData.metadata" type="textarea" :rows="5"
                          placeholder="自定义通知内容模板"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .container {
    max-width: 1440px;
  }
</style>
