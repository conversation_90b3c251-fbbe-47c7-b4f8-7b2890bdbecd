<script setup lang="ts">
import { computed, ref } from 'vue'

defineOptions({
  name: 'LoadingAnimation',
})

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  text: '加载中...',
  size: 'medium',
  fullscreen: true,
  showProgress: false,
  progress: 0,
  logoUrl: 'https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png',
})

const emit = defineEmits<{
  imageError: []
}>()

interface Props {
  visible?: boolean
  text?: string
  size?: 'small' | 'medium' | 'large'
  fullscreen?: boolean
  showProgress?: boolean
  progress?: number
  logoUrl?: string
}

const imageError = ref(false)

function handleImageError() {
  imageError.value = true
  emit('imageError')
}
</script>

<template>
  <div
    v-if="visible"
    class="loading-animation" :class="[
      {
        'loading-animation--fullscreen': fullscreen,
        'loading-animation--local': !fullscreen,
      },
    ]"
  >
    <!-- 遮罩层 -->
    <div class="loading-mask" />

    <!-- 加载内容 -->
    <div class="loading-content">
      <!-- Logo动画 -->
      <div class="logo-container" :class="[`logo-container--${size}`]">
        <!-- 围绕旋转的装饰环 -->
        <div class="rotating-ring">
          <div class="ring-dot ring-dot-1" />
          <div class="ring-dot ring-dot-2" />
          <div class="ring-dot ring-dot-3" />
          <div class="ring-dot ring-dot-4" />
        </div>

        <img
          :src="logoUrl"
          alt="Loading"
          class="logo-image"
          @error="handleImageError"
        >
        <div class="logo-glow" />
      </div>

      <!-- 加载文字 -->
      <div v-if="text" class="loading-text" :class="[`loading-text--${size}`]">
        {{ text }}
      </div>

      <!-- 进度条（可选） -->
      <div v-if="showProgress" class="progress-container" :class="[`progress-container--${size}`]">
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: `${progress}%` }"
          />
        </div>
        <div class="progress-text">
          {{ progress }}%
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.loading-animation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-animation--fullscreen {
  position: fixed;
}

.loading-animation--local {
  position: absolute;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(1px);
}

.loading-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

/* Logo容器 */
.logo-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.logo-container--small {
  width: 48px;
  height: 48px;
}

.logo-container--medium {
  width: 64px;
  height: 64px;
}

.logo-container--large {
  width: 80px;
  height: 80px;
}

/* Logo图片 */
.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  /* animation: logoSpin 2s linear infinite, logoPulse 1.5s ease-in-out infinite alternate; */
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

/* Logo光晕效果 */
.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glowPulse 2s ease-in-out infinite;
}

/* 围绕旋转的装饰环 */
.rotating-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140%;
  height: 140%;
  animation: ringRotate 3s linear infinite;
}

.ring-dot {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
}

.ring-dot-1 {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  animation: dotPulse 1.5s ease-in-out infinite;
}

.ring-dot-2 {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  animation: dotPulse 1.5s ease-in-out infinite 0.375s;
}

.ring-dot-3 {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  animation: dotPulse 1.5s ease-in-out infinite 0.75s;
}

.ring-dot-4 {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  animation: dotPulse 1.5s ease-in-out infinite 1.125s;
}

/* 加载文字 */
.loading-text {
  color: #374151;
  font-weight: 500;
  text-align: center;
  animation: textFade 1.5s ease-in-out infinite alternate;
}

.loading-text--small {
  font-size: 12px;
}

.loading-text--medium {
  font-size: 14px;
}

.loading-text--large {
  font-size: 16px;
}

/* 进度条 */
.progress-container {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.progress-container--small {
  width: 120px;
}

.progress-container--medium {
  width: 160px;
}

.progress-container--large {
  width: 200px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  transition: width 0.3s ease;
  animation: progressShimmer 1.5s ease-in-out infinite;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

/* 动画定义 */
@keyframes logoSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes logoPulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

@keyframes glowPulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes ringRotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes dotPulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes textFade {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

@keyframes progressShimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .logo-container--small {
    width: 40px;
    height: 40px;
  }

  .logo-container--medium {
    width: 56px;
    height: 56px;
  }

  .logo-container--large {
    width: 72px;
    height: 72px;
  }

  .loading-text--small {
    font-size: 11px;
  }

  .loading-text--medium {
    font-size: 13px;
  }

  .loading-text--large {
    font-size: 15px;
  }

  .progress-container--small {
    width: 100px;
  }

  .progress-container--medium {
    width: 140px;
  }

  .progress-container--large {
    width: 180px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .loading-mask {
    background: rgba(17, 24, 39, 0.4);
  }

  .loading-text {
    color: #f3f4f6;
  }

  .progress-text {
    color: #9ca3af;
  }

  .progress-bar {
    background: #374151;
  }
}
</style>
