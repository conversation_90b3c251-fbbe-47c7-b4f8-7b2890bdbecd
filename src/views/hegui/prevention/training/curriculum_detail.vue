<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import trainingCurriculum from '@/api/complianceApi/prevention/trainingCurriculum'
import uploadApi from '@/api/mbbUpload'
import UploadMbb from '@/components/uploadMbb/index.vue'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const courseDetail = ref<any>({})
const form = ref<{
  id?: string
  name?: string
  description?: string
  type?: string
  department?: string
  deadline?: string
}>({})
const activeName = ref('first')

const responseMeasures = ref(false)
const rateNum = ref(3.5)

// 存储真实的图片URL
const realCoverImageUrl = ref('')

// 课程类型映射
const courseTypeMap: Record<string, string> = {
  video: '视频课程',
  document: '文档课程',
  mixed: '混合课程',
  1: '合规培训',
  2: '业务培训',
  3: '技能培训',
  4: '管理能力',
  5: '其他',
}

// 培训对象映射
const applicableRoleMap: Record<string, string> = {
  all: '全体员工',
  management: '管理层',
  department: '特定部门',
  position: '特定岗位',
  new: '新员工',
  other: '其他',
}

// 课程状态映射
const statusMap: Record<string, string> = {
  draft: '草稿',
  published: '已发布',
  offline: '已下线',
}

// 获取课程类型显示名称
function getCourseTypeLabel(type: string) {
  return courseTypeMap[type] || type || '未分类'
}

// 获取课程状态显示名称
function getStatusLabel(status: string) {
  return statusMap[status] || status || '未知'
}

async function getCourseDetail() {
  try {
    loading.value = true
    const courseId = route.query.id as string
    if (!courseId) {
      return
    }
    const res = await trainingCurriculum.courseInfo({}, { id: courseId }, 'info')
    if (res) {
      courseDetail.value = {
        ...res,
        applicableRole: res.applicableRole ? res.applicableRole.split(',') : [],
        courseChapters: res.courseChapters || [],
        courseAttachments: res.courseAttachments || [],
      }
      // 获取真实的封面图片URL
      await getRealCoverImageUrl(res.coverImageUrl)
    }
  }
  catch (error) {
    console.error('获取课程详情失败', error)
    ElMessage.error('获取课程详情失败')
  }
  finally {
    loading.value = false
  }
}

// 获取真实的封面图片URL
async function getRealCoverImageUrl(coverImageUrl: string) {
  if (coverImageUrl && coverImageUrl !== '') {
    try {
      // 如果url看起来已经是完整的URL，直接使用
      if (coverImageUrl.startsWith('http://') || coverImageUrl.startsWith('https://')) {
        realCoverImageUrl.value = coverImageUrl
      }
      else {
        // 否则通过API获取真实地址
        const realUrl = await uploadApi.getFileUrl(coverImageUrl)
        realCoverImageUrl.value = realUrl
      }
    }
    catch (error) {
      console.error('获取封面图片真实地址失败:', error)
      // 如果获取失败，使用默认图片
      realCoverImageUrl.value = ''
    }
  }
  else {
    realCoverImageUrl.value = ''
  }
}

function handleClick(_tab: any, _event: any) {
  // Tab click handler
}

function goEdit(course: any) {
  router.push({
    path: '/training/curriculum/edit',
    query: { id: course.id },
  })
}

function startLearning(row: any) {
  if (!row || !row.id) {
    console.warn('无效的数据对象或缺少ID')
    return
  }
  router.push({
    path: '/training/learningCenter/detail',
    query: { id: row.id },
  })
}

onMounted(() => {
  getCourseDetail()
})
</script>

<template>
  <div v-loading="loading">
    <el-form :model="form" label-width="110px" label-position="left">
      <page-header title="" content="">
        <template #content>
          <div class="aic jcsb flex">
            <div class="f-28">
              <span class="mr-10 c-[#000] lh-[36px]">{{ courseDetail.courseName || '课程详情' }}</span>
              <!-- <el-button type="danger">高风险流程</el-button> -->
            </div>
            <div>
              <div class="aic flex">
                <div>
                  <el-button type="primary" @click="startLearning(courseDetail)">
                    <svg-icon name="ep:video-play" />
                    <span class="ml-4">开始学习</span>
                  </el-button>
                </div>
                <div class="ml-14">
                  <el-button type="primary" plain @click="goEdit(courseDetail)">
                    <svg-icon name="ep:edit" />
                    <span class="ml-4">编辑课程</span>
                  </el-button>
                </div>
                <!-- <div class="ml-14">
                  <el-button type="danger" plain @click="deleteCourse">
                    <svg-icon name="ep:delete" />
                    <span class="ml-4">删除课程</span>
                  </el-button>
                </div> -->
              </div>
            </div>
          </div>
        </template>
      </page-header>
      <page-main style="background-color: transparent;">
        <el-row>
          <el-col :span="24" class="pr-10">
            <el-card shadow="hover">
              <!-- <template #header>
                <div class="f-16 fw-600">基本信息</div>
              </template>
              <div class="f-14">
                <el-row>
                  <el-col :span="8">
                    <span class="c-[#999]">清单编号：</span>
                    <span>FR-2024-001</span>
                  </el-col>
                  <el-col :span="8">
                    <span class="c-[#999]">流程名称：</span>
                    <span>采购申请与审批流程</span>
                  </el-col>
                  <el-col :span="8">
                    <span class="c-[#999]">业务领域：</span>
                    <span>采购管理</span>
                  </el-col>
                </el-row>
              </div>
              <div class="mt-24 f-14">
                <el-row>
                  <el-col :span="8">
                    <span class="c-[#999]">风险等级：</span>
                    <span>高风险</span>
                  </el-col>
                  <el-col :span="8">
                    <span class="c-[#999]">责任部门：</span>
                    <span>采购部</span>
                  </el-col>
                  <el-col :span="8">
                    <span class="c-[#999]">流程负责人：</span>
                    <span>王明</span>
                  </el-col>
                </el-row>
              </div>
              <div class="mt-24 f-14">
                <el-row>
                  <el-col :span="8">
                    <span class="c-[#999]">创建日期：</span>
                    <span>2024-02-10</span>
                  </el-col>
                  <el-col :span="8">
                    <span class="c-[#999]">最后更新时间：</span>
                    <span>2024-02-10</span>
                  </el-col>
                </el-row>
              </div> -->
              <div class="flex">
                <div style="width: 200px;height: 112px;">
                  <img
                    style="width: 100%;height: 100%;border-radius: 8px;object-fit: cover;"
                    :src="realCoverImageUrl"
                    :alt="courseDetail.courseName || '课程封面'"
                  >
                </div>
                <div class="f-14 ml-20 flex-1">
                  <el-row>
                    <el-col :span="12">
                      <div>
                        课程时长：{{ courseDetail.durationMinutes || 0 }}分钟
                      </div>
                      <div class="mt-16">
                        创建日期：{{ courseDetail.createdAt }}
                      </div>
                      <div class="mt-16">
                        课程类型：{{ getCourseTypeLabel(courseDetail.courseType) }}
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div>
                        培训对象：{{ courseDetail.applicableRoleName }}
                      </div>
                      <div class="mt-16">
                        更新日期：{{ courseDetail.updatedAt }}
                      </div>
                      <div class="mt-16">
                        课程状态：{{ getStatusLabel(courseDetail.status) }}
                      </div>
                    </el-col>
                  </el-row>
                  <!-- <div class="mt-16">
                    学习进度
                  </div>
                  <div class="mt-6">
                    <el-progress :percentage="75" :show-text="true" />
                  </div> -->
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
                <el-tab-pane label="课程介绍" name="first">
                  <div class="p-20">
                    <h3 class="mb-16">
                      课程概述
                    </h3>
                    <p>{{ courseDetail.courseOverview || '暂无课程概述' }}</p>

                    <h3 class="mb-16 mt-24">
                      学习目标
                    </h3>
                    <p>{{ courseDetail.learningObjective || '暂无学习目标' }}</p>

                    <h3 class="mb-16 mt-24">
                      先修要求
                    </h3>
                    <p>{{ courseDetail.prerequisites || '无先修要求' }}</p>

                    <!-- <h3 class="mb-16 mt-24">
                      认证信息
                    </h3>
                    <p>{{ courseDetail.certificationInfo || '暂无认证信息' }}</p> -->
                  </div>
                </el-tab-pane>
                <el-tab-pane label="课程内容" name="second">
                  <div class="p-20">
                    <div v-if="courseDetail.courseChapters && courseDetail.courseChapters.length > 0">
                      <div v-for="(chapter, index) in courseDetail.courseChapters" :key="chapter.id || index" class="mb-6">
                        <div class="flex items-center justify-between rounded bg-gray-50 p-4">
                          <div class="flex items-center">
                            <span class="mr-3 text-lg font-medium">{{ chapter.chapterOrder || index + 1 }}.</span>
                            <div>
                              <h4 class="font-medium">
                                {{ chapter.chapterTitle || '未命名章节' }}
                              </h4>
                              <p v-if="chapter.chapterDescription" class="mt-1 text-sm text-gray-600">
                                {{ chapter.chapterDescription }}
                              </p>
                            </div>
                          </div>
                          <div class="flex items-center space-x-2">
                            <el-tag :type="chapter.chapterType === 'VIDEO' ? 'primary' : 'info'" size="small">
                              {{ chapter.chapterType === 'VIDEO' ? '视频' : '文档' }}
                            </el-tag>
                            <span class="text-sm text-gray-500">{{ chapter.durationMinutes || 0 }}分钟</span>
                            <el-button v-if="chapter.contentUrl" size="small" type="primary" @click="() => { (window as any).open(chapter.contentUrl) }">
                              <svg-icon :name="chapter.chapterType === 'VIDEO' ? 'ep:video-play' : 'ep:document'" class="mr-1" />
                              {{ chapter.chapterType === 'VIDEO' ? '播放' : '查看' }}
                            </el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <el-empty description="暂无课程内容" />
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="学习资料" name="third">
                  <div class="p-20">
                    <UploadMbb
                      v-model="courseDetail.courseAttachments"
                      :readonly="true"
                    />
                  </div>
                </el-tab-pane>
                <el-tab-pane v-if="false" label="考核信息" name="fourth">
                  <div class="p-20">
                    <el-empty description="暂无考核信息" />
                  </div>
                </el-tab-pane>

                <el-tab-pane v-if="false" label="学习记录" name="fifth">
                  <el-timeline>
                    <el-timeline-item timestamp="2018/4/12" placement="top">
                      <el-card>
                        <h4>更新人员：李四</h4>
                        <p>完成课程学习，获得90分的成绩。</p>
                      </el-card>
                    </el-timeline-item>
                    <el-timeline-item timestamp="2018/4/3" placement="top">
                      <el-card>
                        <h4>更新人员：李四</h4>
                        <p>开始学习课程。</p>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col v-if="false" :span="8" class="pl-10">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  相关课程
                </div>
              </template>
              <div v-if="courseDetail.relatedCourses && courseDetail.relatedCourses.length > 0">
                <div v-for="(item, index) in courseDetail.relatedCourses" :key="index" class="br-4 p-12" :style="index !== courseDetail.relatedCourses.length - 1 ? 'border-bottom: 1px solid var(--el-border-color);' : ''">
                  <div class="f-14 aic jcsb flex">
                    <span>{{ item.courseName }}</span>
                    <span>{{ '>' }}</span>
                  </div>
                  <div class="f-12 aic jcsb mt-6 flex c-[#6B7280]">
                    <div>
                      <el-tag type="info">
                        {{ item.courseType || '基础课程' }}
                      </el-tag>
                    </div>
                    <div class="f-14 c-[#666666]">
                      {{ item.studentsCount || 0 }}人学习
                    </div>
                  </div>
                </div>
              </div>
              <div v-else>
                <el-empty description="暂无相关课程" />
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  学习评价
                </div>
              </template>
              <div>
                <div class="f-14 jcc flex">
                  <el-rate v-model="rateNum" allow-half :disabled="true" size="large" />
                </div>
                <div class="f-14 jcc flex">
                  4.5分（120人评价）
                </div>
              </div>
              <div>
                <div class="br-4 p-12" style="border-bottom: 1px solid var(--el-border-color);">
                  <div class="f-14 aic flex">
                    <div style="width: 100px;">
                      张三
                    </div>
                    <el-rate v-model="rateNum" allow-half :disabled="true" size="large" />
                  </div>
                  <div class="f-14 c-[#666666]">
                    课程内容非常实用，讲解清晰
                  </div>
                  <div class="f-14 mt-6 c-[#666666]">
                    2024-01-15
                  </div>
                </div>
                <div class="br-4 p-12" style="border-bottom: 1px solid var(--el-border-color);">
                  <div class="f-14 aic flex">
                    <div style="width: 100px;">
                      张三
                    </div>
                    <el-rate v-model="rateNum" allow-half :disabled="true" size="large" />
                  </div>
                  <div class="f-14 c-[#666666]">
                    课程内容非常实用，讲解清晰
                  </div>
                  <div class="f-14 mt-6 c-[#666666]">
                    2024-01-15
                  </div>
                </div>
                <div class="br-4 p-12">
                  <div class="f-14 aic flex">
                    <div style="width: 100px;">
                      张三
                    </div>
                    <el-rate v-model="rateNum" allow-half :disabled="true" size="large" />
                  </div>
                  <div class="f-14 c-[#666666]">
                    课程内容非常实用，讲解清晰
                  </div>
                  <div class="f-14 mt-6 c-[#666666]">
                    2024-01-15
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </page-main>
    </el-form>

    <HDialog v-model="responseMeasures" :title="form.id ? '创建应对措施' : '创建应对措施'" modulewidth="50vw">
      <el-form :model="form" class="px-2" label-position="top" label-width="66px">
        <div class="mb-10">
          <el-button color="#1677FF">
            AI辅助
          </el-button>
        </div>
        <el-form-item label="措施名称：" prop="name">
          <el-input v-model="form.name" size="large" placeholder="请输入对应措施名称" clearable />
        </el-form-item>
        <el-form-item label="措施描述:" prop="description">
          <el-select v-model="form.description" class="m-2" placeholder="请选择措施描述">
            <el-option label="预防措施" value="prevention" />
            <el-option label="纠正措施" value="correction" />
          </el-select>
        </el-form-item>
        <el-form-item label="措施类型:" prop="type">
          <el-select v-model="form.type" class="m-2" placeholder="请选择措施类型">
            <el-option label="流程优化" value="process" />
            <el-option label="制度完善" value="system" />
          </el-select>
        </el-form-item>
        <el-form-item label="责任部门:" prop="department">
          <el-select v-model="form.department" class="m-2" placeholder="请选择责任部门">
            <el-option label="人事部" value="hr" />
            <el-option label="财务部" value="finance" />
          </el-select>
        </el-form-item>
        <el-form-item label="完成期限：" prop="deadline" label-width="100">
          <el-date-picker
            v-model="form.deadline" value-format="x" style="width: 100%;" type="date"
            placeholder="请选择完成期限"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="fotterbtn">
          <el-button class="cancel" @click="() => { responseMeasures = false; form = {}; }">
            取消
          </el-button>
          <el-button type="primary" @click="() => {}">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
