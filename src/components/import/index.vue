<!--
  导入组件 - 默认使用 ImportV1 格式

  如需使用不同格式，请直接导入：
  - ImportV1: 适用于返回格式包含 successCount, updatedCount, failedCount, failedRecords
  - ImportV2: 适用于返回格式包含 success, totalRows, successRows, failedRows, errorDetails
-->
<script lang="ts" setup>
import ImportV1 from './ImportV1.vue'

// 直接使用 ImportV1 的接口
interface ImportProps {
  visible?: boolean
  title?: string
  downloadTemplateApi?: () => Promise<any>
  importDataApi?: (file: File) => Promise<any>
  templateFileName?: string
  acceptFileTypes?: string
  maxFileSize?: number
  showDownloadTemplate?: boolean
}

const props = withDefaults(defineProps<ImportProps>(), {
  visible: false,
  title: '数据导入',
  templateFileName: '导入模板.xlsx',
  acceptFileTypes: '.xlsx,.xls',
  maxFileSize: 10,
  showDownloadTemplate: true,
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': [result: any]
  'error': [error: any]
}>()
</script>

<template>
  <ImportV1
    :visible="props.visible"
    :title="props.title"
    :download-template-api="props.downloadTemplateApi"
    :import-data-api="props.importDataApi"
    :template-file-name="props.templateFileName"
    :accept-file-types="props.acceptFileTypes"
    :max-file-size="props.maxFileSize"
    :show-download-template="props.showDownloadTemplate"
    @update:visible="emit('update:visible', $event)"
    @success="emit('success', $event)"
    @error="emit('error', $event)"
  />
</template>
