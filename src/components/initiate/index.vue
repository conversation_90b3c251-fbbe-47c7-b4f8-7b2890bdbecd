<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dictApi from '@/api/modules/system/dict'
import roleApi from '@/api/permissions/role'
import contractApi from '@/api/review/contract'
import { disablePastDates } from '@/utils/dateUtils'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'

// Props
interface Props {
  visible: boolean
  objectId?: number | string
  reviewType?: string
  complianceReview?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  reviewType: 'CONTRACT',
  complianceReview: null,
})

const emit = defineEmits<Emits>()

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const reviewTypes = ref([])
const selectedReviewTypeId = ref('')
const responsiblePersonId = ref('')
const responsiblePersonName = ref('')
const showEmployeeDialog = ref(false)
const selectedDepartmentId = ref(null)
const selectedDepartmentName = ref('')
const departmentEmployees = ref([])
const loadingEmployees = ref(false)
const memberList = ref([
  // {
  //   id: 1,
  //   departmentId: '',
  //   departmentName: '',
  //   employeeId: '',
  //   employeeName: '',
  //   employees: [],
  //   loadingEmployees: false,
  // },
])
const deadline = ref('')
const description = ref('')
const showEditControls = ref(true)
const isApprovalSaved = ref(false)

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initDialog()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})

// 初始化弹窗
async function initDialog() {
  await getReviewTypes()
  await loadExistingApprovalProcess()

  // 如果有现有的complianceReview数据，填充表单
  if (props.complianceReview) {
    selectedReviewTypeId.value = props.complianceReview.periodType || ''
    deadline.value = props.complianceReview.deadlineTime || ''
    description.value = props.complianceReview.remark || ''
    responsiblePersonId.value = props.complianceReview.director || ''

    // 如果有负责人ID，需要获取其姓名
    if (props.complianceReview.director) {
      await loadResponsiblePersonName(props.complianceReview.director)
    }
  }
}

// 获取审查类型字典数据
async function getReviewTypes() {
  try {
    const response = await dictApi.dictAll(89)
    if (response) {
      reviewTypes.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取审查类型字典数据失败:', error)
    ElMessage.error('获取审查类型字典数据失败')
  }
}

// 获取动态的流程类型和名称
function getProcessConfig() {
  const processTypeMap = {
    contract: 'CONTRACT',
    decision: 'DECISION',
    supplemental: 'SUPPLEMENTAL',
  }

  const processNameMap = {
    contract: '合同审批流程',
    decision: '重大决策审批流程',
    supplemental: '其它审批流程',
  }

  const type = props.reviewType.toLowerCase()
  return {
    processType: processTypeMap[type] || props.reviewType,
    processName: processNameMap[type] || `${props.reviewType}审批流程`,
  }
}

// 加载现有审批流程
async function loadExistingApprovalProcess() {
  try {
    const { processType } = getProcessConfig()
    loading.value = true
    const response = await contractApi.getApprovalProcess(processType)

    if (response && response.approverList && response.approverList.length > 0) {
      showEditControls.value = false
      memberList.value = []

      for (let i = 0; i < response.approverList.length; i++) {
        const approver = response.approverList[i]

        let employeeName = ''
        let employees = []
        try {
          const empResponse = await roleApi.getEmpByUnitId(approver.orgUnitId)
          employees = empResponse.content || []
          const employee = employees.find((emp: any) => emp.id === approver.employeeId)
          if (employee) {
            employeeName = employee.realName
          }
        }
        catch (error) {
          console.error('获取员工信息失败:', error)
        }

        memberList.value.push({
          id: Date.now() + i,
          departmentId: approver.orgUnitId || null,
          departmentName: '',
          employeeId: approver.employeeId || null,
          employeeName,
          employees,
          loadingEmployees: false,
        })
      }
    }
    else {
      showEditControls.value = true
      isApprovalSaved.value = false
    }
    loading.value = false
    console.log('审批流程:', memberList.value)
  }
  catch (error) {
    console.error('获取审批流程失败:', error)
    showEditControls.value = true
    loading.value = false
  }
}

// 添加成员
function addMember() {
  const newId = Date.now()
  memberList.value.push({
    id: newId,
    departmentId: null,
    departmentName: '',
    employeeId: null,
    employeeName: '',
    employees: [],
    loadingEmployees: false,
  })
  if (showEditControls.value) {
    isApprovalSaved.value = false
  }
}

// 删除成员
function removeMember(index: number) {
  if (memberList.value.length > 1) {
    memberList.value.splice(index, 1)
    if (showEditControls.value) {
      isApprovalSaved.value = false
    }
  }
  else {
    ElMessage.warning('至少保留一个成员')
  }
}

// 部门选择变化
async function onDepartmentChange(index: number, departmentId: string) {
  memberList.value[index].departmentId = departmentId || null
  memberList.value[index].employeeId = null
  memberList.value[index].employeeName = ''
  memberList.value[index].loadingEmployees = true

  if (showEditControls.value) {
    isApprovalSaved.value = false
  }

  try {
    const response = await roleApi.getEmpByUnitId(departmentId)
    const employees = response.content || []
    memberList.value[index].employees = employees
    memberList.value[index].loadingEmployees = false
  }
  catch (error) {
    console.error('获取员工列表失败:', error)
    ElMessage.error('获取员工列表失败')
    memberList.value[index].employees = []
    memberList.value[index].loadingEmployees = false
  }
}

// 员工选择变化
function onEmployeeChange(index: number, employeeId: string) {
  if (memberList.value[index].loadingEmployees) {
    ElMessage.warning('员工数据加载中，请稍候')
    return
  }

  if (!memberList.value[index].employees || memberList.value[index].employees.length === 0) {
    ElMessage.warning('该部门暂无员工数据')
    return
  }

  const selectedEmployee = memberList.value[index].employees.find((emp: any) => emp.id === employeeId)
  if (selectedEmployee) {
    memberList.value[index].employeeId = selectedEmployee.id
    memberList.value[index].employeeName = selectedEmployee.realName
    if (showEditControls.value) {
      isApprovalSaved.value = false
    }
  }
}

// 根据部门获取员工列表
async function getEmployeesByDepartment(departmentId: string) {
  try {
    loadingEmployees.value = true
    const response = await roleApi.getEmpByUnitId(departmentId)
    departmentEmployees.value = response.content || []
  }
  catch (error) {
    console.error('获取员工列表失败:', error)
    departmentEmployees.value = []
    ElMessage.error('获取员工列表失败')
  }
  finally {
    loadingEmployees.value = false
  }
}

// 根据员工ID加载负责人姓名
async function loadResponsiblePersonName(employeeId: string) {
  try {
    // 获取部门树，然后遍历所有部门查找该员工
    const unitTreeResponse = await roleApi.unitTree()
    if (unitTreeResponse && unitTreeResponse.length > 0) {
      const employee = await findEmployeeInDepartments(unitTreeResponse, employeeId)
      if (employee) {
        responsiblePersonName.value = employee.realName
        return
      }
    }

    // 如果没有找到员工，保持ID但清空姓名
    responsiblePersonName.value = ''
  }
  catch (error) {
    console.error('加载负责人姓名失败:', error)
    responsiblePersonName.value = ''
  }
}

// 在部门树中递归查找员工
async function findEmployeeInDepartments(departments: any[], employeeId: string): Promise<any> {
  for (const dept of departments) {
    try {
      // 获取当前部门的员工列表
      const empResponse = await roleApi.getEmpByUnitId(dept.id)
      const employees = empResponse.content || []

      // 在当前部门中查找员工
      const employee = employees.find((emp: any) => emp.id === employeeId)
      if (employee) {
        return employee
      }
    }
    catch (error) {
      console.error(`获取部门 ${dept.id} 员工列表失败:`, error)
    }

    // 如果当前部门没有找到，递归查找子部门
    if (dept.children && dept.children.length > 0) {
      const employee = await findEmployeeInDepartments(dept.children, employeeId)
      if (employee) {
        return employee
      }
    }
  }

  return null
}

// 部门选择变化（员工选择弹窗中）
async function onResponsiblePersonDepartmentChange(departmentId: any, departmentName: any) {
  selectedDepartmentId.value = departmentId || null
  selectedDepartmentName.value = departmentName
  if (departmentId) {
    await getEmployeesByDepartment(departmentId)
  }
  else {
    departmentEmployees.value = []
  }
}

// 打开员工选择弹窗
function openEmployeeDialog() {
  // 重置选择状态
  selectedDepartmentId.value = null
  selectedDepartmentName.value = ''
  departmentEmployees.value = []
  showEmployeeDialog.value = true
}

// 选择员工
function selectEmployee(employee: any) {
  responsiblePersonId.value = employee.id
  responsiblePersonName.value = employee.realName
  showEmployeeDialog.value = false
}

// 关闭员工选择弹窗
function closeEmployeeDialog() {
  showEmployeeDialog.value = false
}

// 保存审批流程
async function saveApproval() {
  const validMembers = memberList.value.filter(member =>
    member.departmentId && member.employeeId,
  )

  if (validMembers.length === 0) {
    ElMessage.warning('请至少添加一个审查成员')
    return
  }

  const { processType, processName } = getProcessConfig()
  const approverList = validMembers.map((member, index) => ({
    employeeId: member.employeeId,
    orgUnitId: member.departmentId,
    level: index + 1,
    processType,
  }))

  const params = {
    processName,
    processType,
    approverList,
  }

  try {
    loading.value = true
    await contractApi.createApproval(params)
    isApprovalSaved.value = true
    ElMessage.success('保存成功')
  }
  catch (error) {
    console.error('保存审批流程失败:', error)
    ElMessage.error('保存失败')
  }
  finally {
    loading.value = false
  }
}

// 提交审查
async function submitReview() {
  try {
    // 表单验证
    if (!responsiblePersonId.value) {
      ElMessage.error('请选择审查负责人')
      return
    }

    if (!deadline.value) {
      ElMessage.error('请选择截止日期')
      return
    }

    const validMembers = memberList.value.filter(member =>
      member.departmentId && member.employeeId,
    )
    if (validMembers.length === 0) {
      ElMessage.error('请至少选择一个审查成员')
      return
    }

    if (!description.value.trim()) {
      ElMessage.error('请输入审查说明')
      return
    }

    // 检查是否需要保存审批流程
    if (showEditControls.value && !isApprovalSaved.value) {
      const result = await ElMessageBox.confirm(
        '您还未保存审批流程，是否继续提交？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      )
      if (!result) { return }
    }

    loading.value = true
    const { processType } = getProcessConfig()

    const params = {
      objectId: Number.parseInt(props.objectId as string),
      reviewType: processType,
      periodType: selectedReviewTypeId.value || 'MONTHLY',
      director: responsiblePersonId.value,
      deadlineTime: deadline.value,
      remark: description.value,
    }

    const result = await contractApi.createReview(params)
    if (result) {
      ElMessage.success('提交成功')
      handleClose()
      emit('success')
    }
  }
  catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  }
  finally {
    loading.value = false
  }
}

// 关闭弹窗
function handleClose() {
  dialogVisible.value = false
  // 重置表单数据
  resetForm()
}

// 重置表单数据
function resetForm() {
  selectedReviewTypeId.value = null
  responsiblePersonId.value = null
  responsiblePersonName.value = ''
  selectedDepartmentId.value = null
  selectedDepartmentName.value = ''
  departmentEmployees.value = []
  deadline.value = ''
  description.value = ''
  memberList.value = []
  showEditControls.value = true
  isApprovalSaved.value = false
}
</script>

<template>
  <!-- 审查发起弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    title="发起审查"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form v-loading="loading" :model="{}" label-width="120px">
      <!-- 审查类型 -->
      <el-form-item label="审查类型" required>
        <el-select v-model="selectedReviewTypeId" placeholder="请选择审查类型" style="width: 100%">
          <el-option
            v-for="item in reviewTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 审查负责人 -->
      <el-form-item label="审查负责人" required>
        <div style="width: 100%; display: flex; align-items: center;">
          <el-input
            v-model="responsiblePersonName"
            placeholder="请选择审查负责人"
            readonly
            style="width: 100%"
          />
          <el-button type="primary" style="margin-left: 10px" @click="openEmployeeDialog">
            选择员工
          </el-button>
        </div>
      </el-form-item>

      <!-- 审查成员 -->
      <el-form-item label="审查成员">
        <div style="width: 100%">
          <div v-for="(member, index) in memberList" :key="index" style="display: flex; align-items: center; margin-bottom: 10px;">
            <DepartmentTreeSelect
              v-model="member.departmentId"
              placeholder="请选择部门"
              style="width: 200px; margin-right: 10px;"
              @change="onDepartmentChange(index, $event)"
            />
            <el-select
              v-model="member.employeeId"
              placeholder="请选择员工"
              style="width: 200px; margin-right: 10px;"
              @change="onEmployeeChange(index, $event)"
            >
              <el-option
                v-for="emp in member.employees"
                :key="emp.id"
                :label="emp.realName"
                :value="emp.id"
              />
            </el-select>
            <el-button v-if="showEditControls" type="danger" size="small" @click="removeMember(index)">
              删除
            </el-button>
          </div>
          <el-button v-if="showEditControls" type="primary" size="small" @click="addMember">
            添加成员
          </el-button>
          <el-button v-if="showEditControls" type="primary" size="small" @click="saveApproval">
            保存
          </el-button>
        </div>
      </el-form-item>

      <!-- 截止日期 -->
      <el-form-item label="截止日期" required>
        <el-date-picker
          v-model="deadline"
          type="date"
          :disabled-date="disablePastDates"
          placeholder="请选择截止日期"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 审查说明 -->
      <el-form-item label="审查说明">
        <el-input
          v-model="description"
          type="textarea"
          :rows="4"
          placeholder="请输入审查说明"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <!-- <el-button v-if="showEditControls" type="info" @click="saveApproval">
          保存草稿
        </el-button> -->
        <el-button type="primary" @click="submitReview">
          提交审查
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 员工选择弹窗 -->
  <el-dialog
    v-model="showEmployeeDialog"
    title="选择员工"
    width="700px"
    :close-on-click-modal="false"
    @close="closeEmployeeDialog"
  >
    <div>
      <!-- 部门选择 -->
      <el-form-item label="选择部门" style="margin-bottom: 20px;">
        <DepartmentTreeSelect
          v-model="selectedDepartmentId"
          placeholder="请选择部门"
          style="width: 100%"
          @change="onResponsiblePersonDepartmentChange($event, selectedDepartmentName)"
        />
      </el-form-item>

      <!-- 员工列表 -->
      <div v-if="selectedDepartmentId" v-loading="loadingEmployees">
        <el-table
          :data="departmentEmployees"
          style="width: 100%"
          max-height="400px"
          @row-click="selectEmployee"
        >
          <el-table-column prop="realName" label="姓名" width="150" />
          <el-table-column prop="position" label="职位" width="150" />
          <el-table-column prop="phone" label="联系电话" width="150" />
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="selectEmployee(row)">
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="!loadingEmployees && departmentEmployees.length === 0" style="text-align: center; padding: 20px; color: #999;">
          该部门暂无员工
        </div>
      </div>
      <div v-else style="text-align: center; padding: 40px; color: #999;">
        请先选择部门
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeEmployeeDialog">
          取消
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
