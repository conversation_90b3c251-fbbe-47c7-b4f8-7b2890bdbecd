# 应用配置面板
VITE_APP_SETTING = true
# 页面标题
VITE_APP_TITLE = 猫伯伯合规管理系统


# 接口请求地址，会设置到 axios 的 baseURL 参数上


VITE_APP_API_BASEURL = http://129.211.168.150:8080/services
VITE_APP_API_BASEURL = https://dev.api.mbbhg.com/services/
# VITE_APP_API_BASEURL = https://www.front.mbbhg.com/services/

# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =

# 是否开启代理
VITE_OPEN_PROXY = true
# 是否开启开发者工具
VITE_OPEN_DEVTOOLS = false

# 图片回显域名
VITE_APP_API_BASEURLIMG =

