<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import PageHeader from '@/components/PageHeader/index.vue'
import PageMain from '@/components/PageMain/index.vue'
import problemTaskApi from '@/api/problemTask'
import dictApi from '@/api/modules/system/dict'

const router = useRouter()

// 分页数据
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
})

// 查询参数
const queryParams = reactive({
  title: '',
  reportCode: '',
  reportType: '',
  investigateSource: '',
  level: '',
  establishDate: '',
  status: '',
  createdAtStart: '',
  createdAtEnd: '',
})

// 表格数据
const tableData = ref<any[]>([])
const loading = ref(false)

// 报告类型选项
const reportTypeOptions = ref<Array<{ name: string, value: string }>>([])

// 弹窗相关
const dialogVisible = ref(false)
const _form = ref<any>({})
const _dialogRef = ref<any>(null)

// 统计数据
const statistics = reactive({
  total: 0,
  modify: 0,
  pending: 0,
  published: 0,
  reviewing: 0,
  revoke: 0,
  myResponsible: 0,
  myCompleted: 0,
  myInProgress: 0,
})

// 获取表格数据
async function fetchTableData() {
  loading.value = true
  try {
    // 处理查询参数，将空字符串转换为null
    const processedParams = Object.keys(queryParams).reduce((acc: any, key) => {
      const value = queryParams[key as keyof typeof queryParams]
      acc[key] = value === '' ? null : value
      return acc
    }, {})

    const res = await problemTaskApi.searchReports(processedParams, pagination.page, pagination.size)
    if (res) {
      tableData.value = (res as any).content || []
      pagination.total = (res as any).totalElements || 0

      // 更新统计数据
      updateStatistics((res as any).content || [])
    }
    else {
      ElMessage.error('获取数据失败')
    }
  }
  catch (error) {
    console.error('获取数据失败', error)
    ElMessage.error('获取数据失败')
  }
  finally {
    loading.value = false
  }
}

// 更新统计数据
function updateStatistics(data: any[]) {
  statistics.total = data.length
  statistics.modify = data.filter(item => item.status === 'MODIFY').length
  statistics.pending = data.filter(item => item.status === 'PENDING').length
  statistics.published = data.filter(item => item.status === 'PUBLISHED').length
  statistics.reviewing = data.filter(item => item.status === 'REVIEWING').length
  statistics.revoke = data.filter(item => item.status === 'REVOKE').length

  // 模拟我负责的数据
  const currentUser = 'currentUser' // 假设当前用户ID
  const myReports = data.filter(item => item.employeeId === currentUser)
  statistics.myResponsible = myReports.length
  statistics.myCompleted = myReports.filter(item => item.status === 'PUBLISHED').length
  statistics.myInProgress = myReports.filter(item => ['MODIFY', 'PENDING', 'REVIEWING'].includes(item.status)).length
}

// 处理页码变化
function handleCurrentChange(page: number) {
  pagination.page = page - 1 // 后端从0开始计数
  fetchTableData()
}

// 处理每页条数变化
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 0
  fetchTableData()
}

// 搜索
function handleSearch() {
  pagination.page = 0
  fetchTableData()
}

// 重置
function handleReset() {
  queryParams.title = ''
  queryParams.reportCode = ''
  queryParams.reportType = ''
  queryParams.investigateSource = ''
  queryParams.level = ''
  queryParams.establishDate = ''
  queryParams.status = ''
  queryParams.createdAtStart = ''
  queryParams.createdAtEnd = ''
  pagination.page = 0
  fetchTableData()
}

// 跳转到新增/编辑页面
function goAddEdit(id?: string, type?: string) {
  if (type === 'edit' && id) {
    // 编辑调查报告
    router.push({
      path: '/respond/violationIssues/reportManagement/addEdit',
      query: { id },
    })
  }
  else {
    // 新增调查报告
    router.push({
      path: '/respond/violationIssues/reportManagement/addEdit',
    })
  }
}

// 查看详情
function viewDetail(id: string) {
  router.push({
    path: '/respond/violationIssues/reportManagement/detail',
    query: { id },
  })
}

// 提交表单
function _submitForm() {
  // 提交表单逻辑
}

// 打开弹窗
// 暂时未使用，保留以备后续功能扩展
function _openDialog(id?: string) {
  dialogVisible.value = true
  if (id) {
    // 加载数据逻辑
  }
}

// 格式化状态
function formatStatus(status: string) {
  const statusMap: Record<string, { type: 'info' | 'success' | 'primary' | 'warning' | 'danger', label: string }> = {
    MODIFY: { type: 'warning', label: '需修改' },
    PENDING: { type: 'info', label: '待审查' },
    PUBLISHED: { type: 'success', label: '已发布' },
    REVIEWING: { type: 'primary', label: '审核中' },
    REVOKE: { type: 'danger', label: '已撤回' },
  }
  return statusMap[status] || { type: 'info', label: '未知' }
}

// 格式化优先级
function formatLevel(level: string) {
  const levelMap: Record<string, { type: 'info' | 'warning' | 'danger', label: string }> = {
    LOW: { type: 'info', label: '低' },
    MIDDLE: { type: 'warning', label: '中' },
    HIGH: { type: 'danger', label: '高' },
  }
  return levelMap[level] || { type: 'info', label: '未知' }
}

// 格式化报告类型
function formatReportType(type: string) {
  const option = reportTypeOptions.value.find(item => item.value === type)
  return option ? option.name : '未知'
}

// 格式化调查来源
function formatInvestigateSource(source: string) {
  const sourceMap: Record<string, string> = {
    MARKETING: '市场部',
    PROCUREMENT: '采购部',
    HR: '人力资源部',
    FINANCE: '财务部',
  }
  return sourceMap[source] || '未知'
}

onMounted(async () => {
  // 加载报告类型选项
  try {
    reportTypeOptions.value = await dictApi.dictAll(32)
  }
  catch (error) {
    console.error('加载报告类型选项失败', error)
  }
  fetchTableData()
})
</script>

<template>
  <div class="absolute-container">
    <PageHeader title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              违规问题调查报告
            </h1>
          </div>
          <div class="flex items-center space-x-3">
            <el-button v-auth="['violationIssues/reportManagement/index/add']" type="primary" class="!rounded-button" @click="goAddEdit()">
              新增调查报告
            </el-button>
          </div>
        </div>
      </template>
    </PageHeader>
    <PageMain style="background-color: transparent;">
      <div>
        <el-card class="mt-10" shadow="never">
          <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
            <div class="grid grid-cols-4 mb-4 gap-4">
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">报告状态</label>
                <el-select v-model="queryParams.status" clearable placeholder="全部状态" class="w-full">
                  <el-option label="需修改" value="MODIFY" />
                  <el-option label="待审查" value="PENDING" />
                  <el-option label="已发布" value="PUBLISHED" />
                  <el-option label="审核中" value="REVIEWING" />
                  <el-option label="已撤回" value="REVOKE" />
                </el-select>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">优先级</label>
                <el-select v-model="queryParams.level" clearable placeholder="全部优先级" class="w-full">
                  <el-option label="高" value="HIGH" />
                  <el-option label="中" value="MIDDLE" />
                  <el-option label="低" value="LOW" />
                </el-select>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">来源部门</label>
                <el-select v-model="queryParams.investigateSource" clearable placeholder="全部部门" class="w-full">
                  <el-option label="财务部" value="FINANCE" />
                  <el-option label="市场部" value="MARKETING" />
                  <el-option label="人力资源部" value="HR" />
                  <el-option label="采购部" value="PROCUREMENT" />
                </el-select>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">报告类型</label>
                <el-select v-model="queryParams.reportType" clearable placeholder="全部类型" class="w-full">
                  <el-option
                    v-for="option in reportTypeOptions"
                    :key="option.value"
                    :label="option.name"
                    :value="option.value"
                  />
                </el-select>
              </div>
            </div>
            <div class="grid grid-cols-2 mb-4 gap-4">
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">编制日期</label>
                <div class="flex items-center">
                  <el-date-picker
                    v-model="queryParams.createdAtStart"
                    type="date"
                    placeholder="开始日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="w-full"
                  />
                  <span class="mx-2 text-gray-500">至</span>
                  <el-date-picker
                    v-model="queryParams.createdAtEnd"
                    type="date"
                    placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="w-full"
                  />
                </div>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">关键词</label>
                <el-input
                  v-model="queryParams.title"
                  placeholder="输入报告标题或编号搜索..."
                  clearable
                />
              </div>
            </div>
            <div class="flex items-center justify-end">
              <div class="flex space-x-2">
                <el-button
                  class="!rounded-button whitespace-nowrap"
                  plain
                  @click="handleReset"
                >
                  <i class="fas fa-redo mr-2" />重置
                </el-button>
                <el-button
                  type="primary"
                  class="!rounded-button whitespace-nowrap"
                  @click="handleSearch"
                >
                  <i class="fas fa-search mr-2" />搜索
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
        <el-card class="mt-10" shadow="never">
          <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
            class="mt-4"
            element-loading-text="加载中..."
          >
            <el-table-column prop="reportCode" label="报告编号" width="120" />
            <el-table-column prop="title" label="报告标题" min-width="200" />
            <el-table-column prop="reportType" label="报告类型" width="120">
              <template #default="scope">
                {{ formatReportType(scope.row.reportType) }}
              </template>
            </el-table-column>
            <el-table-column prop="level" label="优先级" width="100">
              <template #default="scope">
                <el-tag
                  :type="formatLevel(scope.row.level).type"
                  size="small"
                >
                  {{ formatLevel(scope.row.level).label }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="investigateSource" label="来源" width="120">
              <template #default="scope">
                {{ formatInvestigateSource(scope.row.investigateSource) }}
              </template>
            </el-table-column>
            <el-table-column prop="establishDate" label="编制日期" width="120">
              <template #default="scope">
                {{ scope.row.establishDate ? dayjs(scope.row.establishDate).format('YYYY-MM-DD') : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag
                  :type="formatStatus(scope.row.status).type"
                  size="small"
                >
                  {{ formatStatus(scope.row.status).label }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createdBy" label="编制人" width="120" />
            <el-table-column fixed="right" label="操作" width="220">
              <template #default="scope">
                <el-button
                  v-auth="['violationIssues/reportManagement/index/view']"
                  size="small" plain
                  @click="viewDetail(scope.row.id)"
                >
                  查看
                </el-button>
                <el-button
                  v-auth="['violationIssues/reportManagement/index/edit']"
                  type="primary"plain
                  size="small"
                  @click="goAddEdit(scope.row.id, 'edit')"
                >
                  编辑
                </el-button>
                <el-button
                  v-auth="['violationIssues/reportManagement/index/delete']"
                  type="danger" plain
                  size="small"
                  @click="goAddEdit(scope.row.id, 'edit')"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="mt-4 flex justify-end">
            <el-pagination
              :current-page="pagination.page + 1"
              :page-size="pagination.size"
              :page-sizes="[10, 20, 30, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/toolsCss";
</style>
