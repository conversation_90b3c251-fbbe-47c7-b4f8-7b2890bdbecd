<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import centerContent from './regulatoryDatabaseMode/center.vue'
import systemApi from '@/api/complianceApi/one/systemManagement'

const router = useRouter()

// 搜索参数
const searchParams = ref({
  title: null,
  isTime: null,
})

// 刷新触发器
const refreshTrigger = ref(0)

// 智能推荐状态
const isIntelligentMode = ref(false)

// 防抖处理：防止快速连续点击
let debounceTimer: any = null

// 加载状态和总数
const loading = ref(false)
const total = ref(0)

// center组件引用
const centerRef = ref<any>(null)

// 法规转化按钮加载状态
const transformLoading = ref(false)

// 组织架构数据（示例）
const _groupList = ref([
  {
    value: '1',
    label: '银保监会',
    children: [
      { value: '1-1', label: '银行监管部' },
      { value: '1-2', label: '保险监管部' },
    ],
  },
  {
    value: '2',
    label: '商务部',
    children: [
      { value: '2-1', label: '市场建设司' },
      { value: '2-2', label: '外贸司' },
    ],
  },
  {
    value: '3',
    label: '证监会',
    children: [
      { value: '3-1', label: '市场监管部' },
      { value: '3-2', label: '发行监管部' },
    ],
  },
])

const _props1 = {
  expandTrigger: 'hover' as const,
  value: 'value',
  label: 'label',
  children: 'children',
}

// 搜索功能
function getList() {
  // 点击查询按钮时，通过refreshTrigger触发搜索
  // 这样即使searchParams没有变化也能重新搜索
  refreshTrigger.value++
}

// 重置搜索
function resetSearch() {
  // 重置搜索参数，子组件会通过watch自动响应
  searchParams.value = {
    title: null,
    isTime: null,
  }
  getList()
  // 不需要再增加refreshTrigger，因为searchParams的变化已经会触发重新加载
}

// 新增法规
function addRegulation() {
  router.push({
    path: '/database/laws/addEdit',
  })
}

// 批量导出
function _batchExport() {
  ElMessage.info('批量导出功能开发中...')
}

// 法规转化
async function regulatoryTransform() {
  if (!centerRef.value || !centerRef.value.regulationIds || centerRef.value.regulationIds.length === 0) {
    ElMessage.warning('请先选择要转化的法规')
    return
  }

  try {
    transformLoading.value = true
    const params = {
      regulationIds: centerRef.value.regulationIds,
    }

    await systemApi.regulatoryConversion(params, 'create')
    ElMessage.success('法规转化成功')
  }
  catch (error) {
    ElMessage.error('法规转化失败，请稍后重试')
  }
  finally {
    transformLoading.value = false
  }
}

// 智能推荐切换
function aiReview() {
  // 清除之前的防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  // 防止加载状态下的重复点击
  if (loading.value) {
    ElMessage.warning('正在加载中，请稍候...')
    return
  }

  // 设置防抖，300ms内只能点击一次
  debounceTimer = setTimeout(() => {
    isIntelligentMode.value = !isIntelligentMode.value
    // 触发刷新以切换接口
    refreshTrigger.value++
    debounceTimer = null
  }, 300)
}

// 组织架构变化
function _changegroup(_value: any) {
  // 处理组织架构变化
}

// 处理子组件事件
function handleLoadingUpdate(loadingState: boolean) {
  loading.value = loadingState
}

function handleTotalUpdate(totalCount: number) {
  total.value = totalCount
}

function handleDataLoaded(_data: any[]) {
  // 处理数据加载完成事件
}

// 组件挂载后自动加载数据
onMounted(() => {
  // 初始加载数据
  getList()
})
</script>

<template>
  <div class="absolute-container">
    <PageHeader>
      <template #content>
        <div class="card flex justify-between p-16">
          <div class="ml-32">
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item label="名称:">
                <el-input v-model="searchParams.title" style="width: 200px;" clearable placeholder="搜索法规名称..." @clear="searchParams.title = null" />
              </el-form-item>
              <!-- <el-form-item label="发布机构:">
                <el-cascader
                  v-model="searchParams.group_id" :props="props1" clearable class="w-full" :options="groupList"
                  @change="changegroup" @clear="searchParams.group_id = undefined"
                />
              </el-form-item> -->
              <el-form-item label="全部状态:" style="width: 200px;">
                <el-select v-model="searchParams.isTime" clearable placeholder="请选择状态" @clear="searchParams.isTime = null">
                  <el-option label="现行有效" value="现行有效" />
                  <el-option label="失效" value="失效" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button v-auth="'regulatoryDatabase/search'" type="primary" :loading="loading" @click="getList">
                  <template #icon>
                    <svg-icon name="ep:search" />
                  </template>
                  查询
                </el-button>
                <el-button v-auth="'regulatoryDatabase/reset'" @click="resetSearch">
                  重置
                </el-button>
                <el-button
                  v-auth="'regulatoryDatabase/recommend'"
                  :type="isIntelligentMode ? 'primary' : 'default'"
                  :loading="loading"
                  :disabled="loading"
                  @click="aiReview"
                >
                  <template v-if="!loading">
                    {{ isIntelligentMode ? '取消推荐' : '智能推荐' }}
                  </template>
                  <template v-else>
                    {{ isIntelligentMode ? '切换中...' : '加载中...' }}
                  </template>
                </el-button>
                <!-- v-auth="'regulatoryDatabase/transform'" -->
                <el-button
                  type="success"
                  :loading="transformLoading"
                  :disabled="transformLoading"
                  @click="regulatoryTransform"
                >
                  <template v-if="!transformLoading">
                    法规转化
                  </template>
                  <template v-else>
                    转化中...
                  </template>
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="flex">
            <div v-auth="'regulatoryDatabase/addRegulation'">
              <el-button type="primary" @click="addRegulation">
                <svg-icon name="ep:plus" />
                <span class="ml-4">新增法规</span>
              </el-button>
            </div>
            <!-- <div v-auth="'regulatoryDatabase/batchExport'" class="ml-14">
              <el-button type="primary" plain @click="batchExport">
                <svg-icon name="ep:download" />
                <span class="ml-4">批量导出</span>
              </el-button>
            </div> -->
          </div>
        </div>
      </template>
    </PageHeader>
    <PageMain style="background-color: transparent;">
      <div class="conBox mt-20 pr-20">
        <!-- <template #leftSide>
            <div class="aic jcsb flex">
              <div class="f-16 f-500">
                筛选条件
              </div>
              <div>
                <svg-icon name="ep:setting" />
              </div>
            </div>
            <div class="mt-16">
              <el-tree :data="data" :props="defaultProps" :default-expand-all="true" @node-click="handleNodeClick" />
            </div>
          </template> -->
        <!-- <template #rightSide>
            <rightContent />
          </template> -->
        <div>
          <centerContent
            ref="centerRef"
            :search-params="searchParams"
            :refresh-trigger="refreshTrigger"
            :is-intelligent-mode="isIntelligentMode"
            @update:loading="handleLoadingUpdate"
            @update:total="handleTotalUpdate"
            @data-loaded="handleDataLoaded"
          />
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }

  .conBox {
    :deep(.main) {
      background-color: transparent;

      .main-container {
        padding: 0 !important;
      }

      .el-slider__button-wrapper {
        display: none;
      }

      .el-slider__runway.is-disabled .el-slider__bar {
        height: 8px;
        background: #4caf50;
        border-radius: 9999px;
      }
    }

    :deep(.flex-container) {
      padding-right: 40px !important;
    }
  }
</style>
