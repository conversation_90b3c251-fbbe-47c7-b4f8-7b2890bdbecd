import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/training',
  component: Layout,
  // redirect: '/one/1',
  name: 'training',
  meta: {
    title: '合规培训体系',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/training/curriculum',
      name: 'training/curriculum',
      component: () => import('@/views/hegui/prevention/training/curriculum.vue'),
      meta: {
        title: '培训课程库',
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/training/curriculum/edit',
          name: '/training/curriculum/edit',
          component: () => import('@/views/hegui/prevention/training/curriculum_edit.vue'),
          meta: {
            title: '新增课程',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/training/curriculume/detail',
          name: '/training/curriculum/detail',
          component: () => import('@/views/hegui/prevention/training/curriculum_detail.vue'),
          meta: {
            title: '培训课程库详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/training/plan',
      name: 'training/plan',
      component: () => import('@/views/hegui/prevention/training/plan.vue'),
      meta: {
        title: '培训计划',
      },
      children: [
        {
          path: '/training/plan/edit',
          name: '/training/plan/edit',
          component: () => import('@/views/hegui/prevention/training/plan_edit.vue'),
          meta: {
            title: '新增培训计划',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/training/plan/detail',
          name: '/training/plan/detail',
          component: () => import('@/views/hegui/prevention/training/plan_detail.vue'),
          meta: {
            title: '培训计划详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/training/assessment',
      name: 'training/assessment',
      // redirect: '/one/3',
      component: () => import('@/views/hegui/prevention/training/assessment.vue'),
      meta: {
        title: '考核认证',
      },
      children: [
        {
          path: '/training/assessment/detail',
          name: '/training/assessment/detail',
          component: () => import('@/views/hegui/prevention/training/assessment_detail.vue'),
          meta: {
            title: '考核认证详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/training/assessment/edit',
          name: '/training/assessment/edit',
          component: () => import('@/views/hegui/prevention/training/assessment_edit.vue'),
          meta: {
            title: '考核认证编辑',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/training/report',
      name: 'training/report',
      // redirect: '/one/3',
      component: () => import('@/views/hegui/prevention/training/report.vue'),
      meta: {
        title: '培训报告',
      },
      children: [
        {
          path: '/training/report/detail',
          name: '/training/report/detail',
          component: () => import('@/views/hegui/prevention/training/report_detail.vue'),
          meta: {
            title: '培训报告详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        // {
        //   path: '/training/report/edit',
        //   name: '/training/report/edit',
        //   component: () => import('@/views/hegui/prevention/training/report_edit.vue'),
        //   meta: {
        //     title: '培训报告编辑',
        //     sidebar: false,
        //     breadcrumb: false,
        //   },
        // },
      ],
    },
    {
      path: '/training/learningCenter',
      name: '/training/learningCenter',
      // redirect: '/one/3',
      component: () => import('@/views/hegui/prevention/training/learningCenter.vue'),
      meta: {
        title: '学习中心',
      },
      children: [
        {
          path: '/training/learningCenter/detail',
          name: '/training/learningCenter/detail',
          component: () => import('@/views/hegui/prevention/training/learningCenter_detail.vue'),
          meta: {
            title: '学习详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/training/learningCenter/certificateInfo',
          name: '/training/learningCenter/certificateInfo',
          component: () => import('@/views/hegui/prevention/training/certificateInfo.vue'),
          meta: {
            title: '相关证书',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/training/learningCenter/test',
          name: '/training/learningCenter/test',
          component: () => import('@/views/hegui/prevention/training/learningCenter_test.vue'),
          meta: {
            title: '课后测试',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      // 考试
      path: '/training/examination',
      name: '/training/examination',
      component: () => import('@/views/hegui/prevention/training/examination/index.vue'),
      meta: {
        title: '考试',
      },
      children: [
        {
          path: '/training/examination/detail',
          name: '/training/examination/detail',
          component: () => import('@/views/hegui/prevention/training/examination/detail.vue'),
          meta: {
            title: '考试详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/training/examination/center',
          name: '/training/examination/center',
          component: () => import('@/views/hegui/prevention/training/examination/center.vue'),
          meta: {
            title: '在线考试',
            sidebar: false,
            breadcrumb: false,
          },
        },
        // 错题解析
        {
          path: '/training/examination/error',
          name: '/training/examination/error',
          component: () => import('@/views/hegui/prevention/training/examination/error.vue'),
          meta: {
            title: '错题解析',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/training/examination/addEdit',
          name: '/training/examination/addEdit',
          component: () => import('@/views/hegui/prevention/training/examination/addEdit.vue'),
          meta: {
            title: '考试新增',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/training/consultingNews',
      name: 'training/consultingNews',
      component: () => import('@/views/hegui/prevention/training/consultingNews/index.vue'),
      meta: {
        title: '咨讯动态',
      },
      children: [
        {
          path: '/training/consultingNews/detail',
          name: '/training/consultingNews/detail',
          component: () => import('@/views/hegui/prevention/training/consultingNews/detail.vue'),
          meta: {
            title: '咨讯动态详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/training/consultingNews/addEdit',
          name: '/training/consultingNews/addEdit',
          component: () => import('@/views/hegui/prevention/training/consultingNews/addEdit.vue'),
          meta: {
            title: '咨讯动态编辑',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },

  ],
}

export default routes
