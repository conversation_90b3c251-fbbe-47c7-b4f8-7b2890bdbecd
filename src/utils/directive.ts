import type { App } from 'vue'
import mediumZoom from 'medium-zoom'

export default function directive(app: App) {
  // 注册 v-auth 和 v-auth-all 指令
  app.directive('auth', {
    mounted: (el, binding) => {
      if (!useAuth().auth(binding.value)) {
        el.remove()
      }
    },
  })
  app.directive('auth-all', {
    mounted: (el, binding) => {
      if (!useAuth().authAll(binding.value)) {
        el.remove()
      }
    },
  })
  app.directive('timeago', {
    mounted: (el, binding) => {
      el.textContent = useTimeago().format(binding.value)
      if (binding.modifiers.interval) {
        el.__timeagoSetInterval__ = setInterval(() => {
          el.textContent = useTimeago().format(binding.value)
        }, 1000)
      }
    },
    beforeUnmount: (el, binding) => {
      if (binding.modifiers.interval) {
        clearInterval(el.__timeagoSetInterval__)
      }
    },
  })
  // 注册防抖指令
  app.directive('debounce', {
    mounted(el, binding) {
      // 参数解析（只需延迟时间）
      const delay = typeof binding.value === 'number' ? binding.value : 1000
      let timer = null

      // 保存原始状态和事件处理器
      const originalDisabled = el.disabled
      const originalOnclick = el.onclick

      // 覆盖点击事件
      el.onclick = function (...args) {
        // 如果已经在禁用状态，直接返回
        if (el.disabled) { return }

        // 立即禁用按钮
        el.disabled = true

        // 执行原始点击逻辑
        if (originalOnclick) {
          originalOnclick.apply(this, args)
        }

        // 设置恢复定时器
        timer = setTimeout(() => {
          el.disabled = originalDisabled
          timer = null
        }, delay)
      }

      // 存储引用用于卸载
      el._debounce = { timer, originalDisabled }
    },
    beforeUnmount(el) {
      if (el._debounce) {
        clearTimeout(el._debounce.timer)
        el.disabled = el._debounce.originalDisabled // 还原原始状态
        delete el._debounce
      }
    },
  })
  // app.directive('debounce', {
  //   mounted: (el, binding) => {
  //     let timer: NodeJS.Timeout
  //     const delay = binding.value || 1000

  //     const debounceHandler = (event: Event) => {
  //       clearTimeout(timer)
  //       timer = setTimeout(() => {
  //         if (binding.arg === 'input') {
  //           // 处理输入事件
  //           const inputEvent = new Event('input', { bubbles: true })
  //           el.dispatchEvent(inputEvent)
  //         } else {
  //           // 处理点击事件
  //           const originalHandler = el.__originalHandler__
  //           if (originalHandler) {
  //             originalHandler(event)
  //           }
  //         }
  //       }, delay)
  //     }

  //     if (binding.arg === 'input') {
  //       el.addEventListener('input', debounceHandler)
  //     } else {
  //       // 保存原始事件处理器
  //       el.__originalHandler__ = el.onclick
  //       el.onclick = debounceHandler
  //     }

  //     el.__debounceHandler__ = debounceHandler
  //   },
  //   beforeUnmount: (el, binding) => {
  //     if (binding.arg === 'input') {
  //       el.removeEventListener('input', el.__debounceHandler__)
  //     }
  //     delete el.__originalHandler__
  //     delete el.__debounceHandler__
  //   },
  // })

  // 注册 medium-zoom 指令
  app.directive('zoomable', {
    mounted: (el) => {
      mediumZoom(el, {
        background: 'var(--g-bg)',
      })
    },
  })
}
