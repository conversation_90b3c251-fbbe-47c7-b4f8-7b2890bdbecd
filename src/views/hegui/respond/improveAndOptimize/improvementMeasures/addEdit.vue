<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Bell as ElIconBell,
  Bottom as ElIconBottom,
  Check as ElIconCheck,
  Close as ElIconClose,
  DataLine as ElIconDataLine,
  Delete as ElIconDelete,
  MagicStick as ElIconMagicStick,
  Menu as ElIconMenu,
  Plus as ElIconPlus,
  Search as ElIconSearch,
  Top as ElIconTop,
  Upload as ElIconUpload,
  View as ElIconView,
  Warning as ElIconWarning,
} from '@element-plus/icons-vue'
import measureApi from '@/api/problemTask/measure'
import dictApi from '@/api/modules/system/dict'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import Depart<PERSON>erson from '@/components/departPerson/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import HandlingMeasuresSelector from '@/components/HandlingMeasuresSelector/index.vue'
import { disablePastDates } from '@/utils/dateUtils'

const route = useRoute()

// 接口数据类型定义
interface DictOption {
  label: string
  value: string
}

interface AttachmentItem {
  id?: number
  relatedId?: number
  relatedType?: number
  fileName: string
  filePath: string
  fileType: string
  fileSize?: string
  fileDesc?: string
  metadata?: string
  version?: number
  createdBy?: string
  createdAt?: any
  updatedBy?: string
  updatedAt?: any
  isDeleted?: boolean
}

interface ImproveForm {
  id?: number
  improveName: string
  improveCode: string
  improveType: string
  improveSource: string
  sourceCode: string
  sourceName: string
  level: string
  dutyEmployeeId: number | null
  dutyEmployeeOrgId: number | null
  collaborationEmployeeId: number | null
  startDate: string
  finishDate: string
  status: null
  improveBackground: string
  problemAnalysis: string
  improveTarget: string
  relatedRegion: string[]
  schemeSummary: string
  resourceRequirement?: string
  expectedEffect?: string
  emergencyPlan?: string
  supervisionMethod?: null | string[]
  supervisorEmployeeId?: string
  supervisionFrequency?: string
  evaluationIndicators?: string
  evaluationTime?: string
  metadata?: string
  version?: number
  createdBy?: string
  createdAt?: any
  updatedBy?: string
  updatedAt?: any
  isDeleted?: boolean
  attachmentList: AttachmentItem[]
}

const _route = useRoute()
const _router = useRouter()
const _loading = ref(false)
const _isEdit = ref(false)

// 表单引用
const _formRef = ref()

// 字典选项
const _improveTypeOptions = ref<DictOption[]>([])
const _improveSourceOptions = ref<DictOption[]>([])

// 表单校验规则
const _formRules = {
  improveName: [
    { required: true, message: '请输入措施名称', trigger: 'blur' },
  ],
  improveType: [
    { required: true, message: '请选择措施类型', trigger: 'change' },
  ],
  level: [
    { required: true, message: '请选择优先级', trigger: 'change' },
  ],
  improveSource: [
    { required: true, message: '请选择来源', trigger: 'change' },
  ],
  dutyEmployeeOrgId: [
    { required: true, message: '请选择责任部门', trigger: 'change' },
  ],
  dutyEmployeeId: [
    { required: true, message: '请选择负责人', trigger: 'change' },
  ],
  collaborationEmployeeId: [
    { required: true, message: '请选择协作人', trigger: 'change' },
  ],
  finishDate: [
    { required: true, message: '请选择计划完成日期', trigger: 'change' },
  ],
  improveBackground: [
    { required: true, message: '请输入改进背景', trigger: 'blur' },
  ],
  improveTarget: [
    { required: true, message: '请输入改进目标', trigger: 'blur' },
  ],
  sourceCode: [
    { required: true, message: '请选择关联的处理措施', trigger: 'change' },
  ],
}

// 表单数据
const _formData = reactive<ImproveForm>({
  improveName: '',
  improveCode: '',
  improveType: '',
  improveSource: '',
  sourceCode: '',
  sourceName: '',
  level: '',
  dutyEmployeeId: null,
  dutyEmployeeOrgId: null,
  collaborationEmployeeId: null,
  startDate: '',
  finishDate: '',
  status: null,
  improveBackground: '',
  problemAnalysis: '',
  improveTarget: '',
  relatedRegion: [],
  schemeSummary: '',
  resourceRequirement: '',
  expectedEffect: '',
  emergencyPlan: '',
  supervisionMethod: null,
  supervisionFrequency: '',
  evaluationIndicators: '',
  evaluationTime: '',
  attachmentList: [],
})

// 获取详情数据
async function fetchDetailData() {
  const id = route.query.id
  if (id) {
    _isEdit.value = true
    _loading.value = true
    try {
      const response = await measureApi.getImproveDetail(id)
      if (response) {
        const data = response
        // 处理复选框组数据，将逗号分隔的字符串转换为数组
        if (data.relatedRegion && typeof data.relatedRegion === 'string') {
          data.relatedRegion = data.relatedRegion.split(',')
        }
        Object.assign(_formData, data)
      }
    }
    catch (error) {
      console.error('获取详情失败:', error)
      ElMessage.error('获取详情失败')
    }
    finally {
      _loading.value = false
    }
  }
}

// 附件上传成功处理
function handleAttachmentUploadSuccess(_fileInfo: any) {
  // 附件上传成功，循环数据添加attachmentType字段
  _formData.attachmentList.forEach((item) => {
    if (!item.relatedType) {
      item.relatedType = 2
    }
  })
}

// 处理来源选择变化
function handleSourceChange(value: any, row: any) {
  if (row) {
    // 设置来源编号和名称
    _formData.sourceCode = value
    _formData.sourceName = row.title
  }
  else {
    // 清空时同时清空两个字段
    _formData.sourceCode = ''
    _formData.sourceName = ''
  }
}

// 保存数据
async function handleSave() {
  // 表单校验
  if (!_formRef.value) {
    return
  }
  const valid = await _formRef.value.validate().catch(() => false)
  if (!valid) {
    ElMessage.warning('请完善必填信息')
    return
  }

  _loading.value = true
  try {
    // 处理复选框组数据，转换为逗号分隔的字符串
    const submitData = {
      ..._formData,
      relatedRegion: Array.isArray(_formData.relatedRegion) ? _formData.relatedRegion.join(',') : _formData.relatedRegion,
    }

    if (_isEdit.value) {
      await measureApi.updateImprove(_formData.id!.toString(), submitData)
      ElMessage.success('更新成功')
    }
    else {
      await measureApi.createImprove(submitData)
      ElMessage.success('创建成功')
    }
    _router.back()
  }
  catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
  finally {
    _loading.value = false
  }
}

// 保存并开始实施
async function _handleSaveAndStart() {
  _formData.status = 'PROGRESSING' as any
  await handleSave()
}

// 取消操作
function _handleCancel() {
  _router.back()
}

// 预览功能
function _handlePreview() {
  // 预览功能实现
  ElMessage.info('预览功能待实现')
}

// 获取字典数据
async function fetchDictOptions() {
  try {
    // 获取措施类型选项 (35)
    const typeResponse = await dictApi.dictAll(31)
    if (typeResponse) {
      _improveTypeOptions.value = typeResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }

    // 获取来源选项 (3)
    const sourceResponse = await dictApi.dictAll(30)
    if (sourceResponse) {
      _improveSourceOptions.value = sourceResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
    ElMessage.error('获取字典数据失败')
  }
}

// 生成措施编号
async function _generateCode() {
  try {
    const response = await dictApi.getCode('CONTINUOUS_IMPROVE')
    if (response) {
      _formData.improveCode = response
    }
  }
  catch (error) {
    console.error('获取措施编号失败:', error)
    ElMessage.error('获取措施编号失败')
  }
}

// 页面初始化
onMounted(() => {
  fetchDictOptions()
  if (route.query.id) {
    // 处理详情接口
    fetchDetailData()
  }
  else {
    _generateCode()
  }
})

const isReminderEnabled = ref(false)
const viewPermission = ref('全公司')
const editPermission = ref('仅负责人')
const progressPermission = ref('仅负责人')
const evaluatePermission = ref('监督人')

const templates = ref([
  { name: '流程优化标准模板', scenario: '适用于业务流程改进' },
  { name: '制度完善模板', scenario: '适用于规章制度修订' },
  { name: '系统功能改进模板', scenario: '适用于IT系统功能优化' },
  { name: '岗位职责优化模板', scenario: '适用于岗位职责调整' },
])

const relatedCases = ref([
  { title: '生产流程优化项目', type: '流程改进', status: '已完成', similarity: 85 },
  { title: '质量管理制度修订', type: '制度改进', status: '进行中', similarity: 76 },
  { title: 'CRM系统功能增强', type: '系统改进', status: '已完成', similarity: 62 },
])

const references = ref([
  { title: '企业持续改进指南', link: '#' },
  { title: '流程优化最佳实践', link: '#' },
  { title: '风险管理方法论', link: '#' },
  { title: '改进措施评估标准', link: '#' },
])

const aiSuggestion = ref('')

function generateSolution() {
  aiSuggestion.value = 'AI建议：根据您的问题描述，建议采用分阶段实施方案，第一阶段进行现状调研，第二阶段设计优化方案，第三阶段试点运行，第四阶段全面推广。'
}

function identifyRisks() {
  aiSuggestion.value = 'AI识别风险：1. 部门协作不畅可能导致进度延迟 2. 资源不足可能影响实施效果 3. 员工抵触情绪可能阻碍执行'
}

function suggestMetrics() {
  aiSuggestion.value = 'AI建议评估指标：1. 流程效率提升百分比 2. 错误率降低幅度 3. 员工满意度变化 4. 成本节约金额'
}
</script>

<template>
  <div class="h-screen flex bg-gray-100">
    <!-- 主内容区 -->
    <div class="flex flex-1 flex-col overflow-hidden">
      <!-- 页面内容 -->
      <main class="flex-1 overflow-auto bg-gray-50 p-6">
        <div class="rounded-lg bg-white p-6 shadow-sm">
          <!-- 页面标题和操作按钮 -->
          <div class="mb-6 flex items-center justify-between">
            <h1 class="text-xl font-bold">
              {{ _isEdit ? '编辑改进措施' : '新增改进措施' }}
            </h1>
            <div class="flex space-x-3">
              <el-button type="primary" class="!rounded-button whitespace-nowrap" :loading="_loading" @click="handleSave">
                <el-icon><ElIconCheck /></el-icon>
                <span>保存</span>
              </el-button>
              <!-- <el-button class="!rounded-button whitespace-nowrap" :loading="_loading" @click="_handleSaveAndStart">
                <el-icon><ElIconUpload /></el-icon>
                <span>保存并开始实施</span>
              </el-button> -->
              <el-button class="!rounded-button whitespace-nowrap" @click="_handleCancel">
                <el-icon><ElIconClose /></el-icon>
                <span>取消</span>
              </el-button>
            </div>
          </div>
          <!-- 表单内容 -->
          <el-form ref="_formRef" :model="_formData" :rules="_formRules" label-width="120px">
            <div class="space-y-8">
              <!-- 基本信息 -->
              <div class="border-b pb-6">
                <h2 class="mb-4 text-lg font-bold">
                  基本信息
                </h2>
                <div class="grid grid-cols-2 gap-6">
                  <el-form-item label="措施名称" prop="improveName" required>
                    <el-input v-model="_formData.improveName" placeholder="请输入措施名称" />
                  </el-form-item>
                  <el-form-item label="措施编号">
                    <div class="flex space-x-2">
                      <el-input v-model="_formData.improveCode" placeholder="自动生成" readonly />
                    </div>
                  </el-form-item>
                  <el-form-item label="改进类型" prop="improveType" required>
                    <el-select v-model="_formData.improveType" placeholder="请选择措施类型" class="w-full">
                      <el-option
                        v-for="option in _improveTypeOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="优先级" prop="level" required>
                    <el-radio-group v-model="_formData.level">
                      <el-radio-button label="HIGH" class="!text-red-500">
                        高
                      </el-radio-button>
                      <el-radio-button label="MIDDLE" class="!text-orange-500">
                        中
                      </el-radio-button>
                      <el-radio-button label="LOW" class="!text-green-500">
                        低
                      </el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="来源" prop="improveSource" required>
                    <el-select v-model="_formData.improveSource" placeholder="请选择来源" class="w-full">
                      <el-option
                        v-for="option in _improveSourceOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="来源编号" prop="sourceCode" required>
                    <HandlingMeasuresSelector
                      v-model="_formData.sourceCode"
                      :display-value="_formData.sourceName"
                      value-key="dealCode"
                      display-key="title"
                      placeholder="请选择关联的处理措施"
                      @change="handleSourceChange"
                    />
                  </el-form-item>
                  <el-form-item label="责任部门" prop="dutyEmployeeOrgId" required>
                    <DepartmentTreeSelect v-model="_formData.dutyEmployeeOrgId as any" placeholder="请选择责任部门" />
                  </el-form-item>
                  <el-form-item label="负责人" prop="dutyEmployeeId" required>
                    <DepartPerson v-model="_formData.dutyEmployeeId as any" placeholder="请选择负责人" />
                  </el-form-item>
                  <el-form-item label="协作人" prop="collaborationEmployeeId" required>
                    <DepartPerson v-model="_formData.collaborationEmployeeId as any" placeholder="请选择协作人" />
                  </el-form-item>
                  <el-form-item label="开始日期">
                    <el-date-picker v-model="_formData.startDate" type="date" placeholder="选择日期" :disabled-date="disablePastDates" class="w-full" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                  </el-form-item>
                  <el-form-item label="计划完成日期" prop="finishDate" required>
                    <el-date-picker v-model="_formData.finishDate" :disabled-date="disablePastDates" type="date" placeholder="选择日期" class="w-full" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                  </el-form-item>
                </div>
              </div>
              <!-- 改进内容 -->
              <div class="border-b pb-6">
                <h2 class="mb-4 text-lg font-bold">
                  改进内容
                </h2>
                <el-form-item label="改进背景" prop="improveBackground" required>
                  <el-input v-model="_formData.improveBackground" type="textarea" :rows="4" placeholder="描述改进的背景和原因" />
                </el-form-item>
                <el-form-item label="问题分析">
                  <el-input v-model="_formData.problemAnalysis" type="textarea" :rows="4" placeholder="描述问题的根本原因和影响" />
                </el-form-item>
                <el-form-item label="改进目标" prop="improveTarget" required>
                  <el-input v-model="_formData.improveTarget" type="textarea" :rows="4" placeholder="描述改进预期达到的具体目标" />
                </el-form-item>
                <el-form-item label="关联领域">
                  <el-checkbox-group v-model="_formData.relatedRegion">
                    <el-checkbox label="流程" />
                    <el-checkbox label="制度" />
                    <el-checkbox label="岗位职责" />
                    <el-checkbox label="系统" />
                    <el-checkbox label="培训" />
                    <el-checkbox label="其他" />
                  </el-checkbox-group>
                </el-form-item>
              </div>
              <!-- 实施方案 -->
              <div v-if="false" class="border-b pb-6">
                <h2 class="mb-4 text-lg font-bold">
                  实施方案
                </h2>
                <el-form-item label="方案概述" required>
                  <el-input v-model="_formData.schemeSummary" type="textarea" :rows="4" placeholder="描述实施方案的总体思路和框架" />
                </el-form-item>
                <el-form-item label="实施步骤">
                  <div class="w-full">
                    <div class="mb-2 flex items-center justify-between">
                      <span class="text-sm text-gray-500">实施步骤列表</span>
                      <el-button size="small" class="!rounded-button whitespace-nowrap">
                        <el-icon><ElIconPlus /></el-icon>
                        <span>添加步骤</span>
                      </el-button>
                    </div>
                    <el-table :data="[]" border class="w-full">
                      <el-table-column prop="step" label="步骤序号" width="100" />
                      <el-table-column prop="name" label="步骤名称" />
                      <el-table-column prop="startDate" label="开始日期" width="120" />
                      <el-table-column prop="endDate" label="结束日期" width="120" />
                      <el-table-column prop="owner" label="负责人" width="120" />
                      <el-table-column prop="desc" label="步骤描述" />
                      <el-table-column label="操作" width="120">
                        <template #default>
                          <el-button size="small" text>
                            <el-icon><ElIconTop /></el-icon>
                          </el-button>
                          <el-button size="small" text>
                            <el-icon><ElIconBottom /></el-icon>
                          </el-button>
                          <el-button size="small" text>
                            <el-icon><ElIconDelete /></el-icon>
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
                <el-form-item label="资源需求">
                  <el-input v-model="_formData.resourceRequirement" type="textarea" :rows="4" placeholder="描述实施需要的人力、物力和财力资源" />
                </el-form-item>
                <el-form-item label="预期效果">
                  <el-input v-model="_formData.expectedEffect" type="textarea" :rows="4" placeholder="描述改进措施预期达到的效果和成果" />
                </el-form-item>
              </div>
              <!-- 风险管理 -->
              <div v-if="false" class="border-b pb-6">
                <h2 class="mb-4 text-lg font-bold">
                  风险管理
                </h2>
                <el-form-item label="实施风险点">
                  <div class="w-full">
                    <div class="mb-2 flex items-center justify-between">
                      <span class="text-sm text-gray-500">风险点列表</span>
                      <el-button size="small" class="!rounded-button whitespace-nowrap">
                        <el-icon><ElIconPlus /></el-icon>
                        <span>添加风险点</span>
                      </el-button>
                    </div>
                    <el-table :data="[]" border class="w-full">
                      <el-table-column prop="risk" label="风险描述" />
                      <el-table-column prop="level" label="风险等级" width="120">
                        <template #default>
                          <el-tag type="danger">
                            高
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="scope" label="影响范围" />
                      <el-table-column prop="solution" label="应对措施" />
                      <el-table-column label="操作" width="80">
                        <template #default>
                          <el-button size="small" text>
                            <el-icon><ElIconDelete /></el-icon>
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
                <el-form-item label="应急预案">
                  <el-input v-model="_formData.emergencyPlan" type="textarea" :rows="4" placeholder="描述实施过程中可能遇到的问题和应对方案" />
                </el-form-item>
              </div>
              <!-- 监督与评估 -->
              <div v-if="false" class="border-b pb-6">
                <h2 class="mb-4 text-lg font-bold">
                  监督与评估
                </h2>
                <el-form-item label="监督方式">
                  <el-checkbox-group v-model="_formData.supervisionMethod as any">
                    <el-checkbox label="定期汇报" />
                    <el-checkbox label="现场检查" />
                    <el-checkbox label="数据监控" />
                    <el-checkbox label="第三方评估" />
                    <el-checkbox label="其他" />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="监督人">
                  <DepartPerson v-model="_formData.supervisorEmployeeId" placeholder="请选择监督人" />
                </el-form-item>
                <el-form-item label="监督频率">
                  <el-select v-model="_formData.supervisionFrequency" placeholder="请选择监督频率" class="w-full">
                    <el-option label="每日" value="1" />
                    <el-option label="每周" value="2" />
                    <el-option label="每月" value="3" />
                    <el-option label="每季度" value="4" />
                    <el-option label="半年度" value="5" />
                    <el-option label="年度" value="6" />
                    <el-option label="其他" value="7" />
                  </el-select>
                </el-form-item>
                <el-form-item label="评估指标">
                  <div class="w-full">
                    <div class="mb-2 flex items-center justify-between">
                      <span class="text-sm text-gray-500">评估指标列表</span>
                      <el-button size="small" class="!rounded-button whitespace-nowrap">
                        <el-icon><ElIconPlus /></el-icon>
                        <span>添加指标</span>
                      </el-button>
                    </div>
                    <el-table :data="[]" border class="w-full">
                      <el-table-column prop="name" label="指标名称" />
                      <el-table-column prop="type" label="指标类型" width="120">
                        <template #default>
                          <el-tag>定量</el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="target" label="目标值" width="120" />
                      <el-table-column prop="method" label="评估方法" />
                      <el-table-column label="操作" width="80">
                        <template #default>
                          <el-button size="small" text>
                            <el-icon><ElIconDelete /></el-icon>
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
                <el-form-item label="评估时间">
                  <el-date-picker v-model="_formData.evaluationTime" type="date" placeholder="选择评估时间" class="w-full" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                </el-form-item>
              </div>
              <!-- 附件与相关资料 -->
              <div>
                <h2 class="mb-4 text-lg font-bold">
                  附件与相关资料
                </h2>
                <el-form-item label="附件上传">
                  <UploadMbb
                    v-model="_formData.attachmentList"
                    :max="10"
                    :size="20"
                    service-name="whiskerguardregulatoryservice"
                    category-name="improvement-measures"
                    :use-file-path="true"
                    tip-text="支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 20MB"
                    @upload-success="handleAttachmentUploadSuccess"
                  />
                </el-form-item>
              </div>
              <!-- 通知设置 -->
              <div v-if="false" class="border-b pb-6">
                <h2 class="mb-4 text-lg font-bold">
                  通知设置
                </h2>
                <el-form-item label="通知对象">
                  <el-select multiple placeholder="请选择接收通知的人员" class="w-full">
                    <el-option label="张明远" value="1" />
                    <el-option label="李思雨" value="2" />
                    <el-option label="王建国" value="3" />
                    <el-option label="赵晓芳" value="4" />
                    <el-option label="陈志强" value="5" />
                  </el-select>
                </el-form-item>
                <el-form-item label="通知方式">
                  <el-checkbox-group>
                    <el-checkbox label="系统消息" />
                    <el-checkbox label="电子邮件" />
                    <el-checkbox label="短信" />
                    <el-checkbox label="其他" />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="通知事件">
                  <el-checkbox-group>
                    <el-checkbox label="开始实施" />
                    <el-checkbox label="进度更新" />
                    <el-checkbox label="完成实施" />
                    <el-checkbox label="状态变更" />
                    <el-checkbox label="评估结果" />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="进度提醒">
                  <div class="flex items-center space-x-4">
                    <el-switch />
                    <span>开启提醒</span>
                    <el-select placeholder="提醒频率" class="w-32" :disabled="!isReminderEnabled">
                      <el-option label="每周" value="1" />
                      <el-option label="每两周" value="2" />
                      <el-option label="每月" value="3" />
                    </el-select>
                    <el-checkbox-group class="ml-4">
                      <el-checkbox label="系统消息" />
                      <el-checkbox label="电子邮件" />
                      <el-checkbox label="短信" />
                    </el-checkbox-group>
                  </div>
                </el-form-item>
              </div>
              <!-- 权限设置 -->
              <div v-if="false">
                <h2 class="mb-4 text-lg font-bold">
                  权限设置
                </h2>
                <el-form-item label="查看权限">
                  <el-radio-group>
                    <el-radio label="全公司" />
                    <el-radio label="责任部门" />
                    <el-radio label="改进团队" />
                    <el-radio label="指定人员" />
                  </el-radio-group>
                  <el-select v-if="viewPermission === '指定人员'" multiple placeholder="请选择可查看人员" class="mt-2 w-full">
                    <el-option label="张明远" value="1" />
                    <el-option label="李思雨" value="2" />
                    <el-option label="王建国" value="3" />
                    <el-option label="赵晓芳" value="4" />
                    <el-option label="陈志强" value="5" />
                  </el-select>
                </el-form-item>
                <el-form-item label="编辑权限">
                  <el-radio-group>
                    <el-radio label="仅负责人" />
                    <el-radio label="改进团队" />
                    <el-radio label="指定人员" />
                  </el-radio-group>
                  <el-select v-if="editPermission === '指定人员'" multiple placeholder="请选择可编辑人员" class="mt-2 w-full">
                    <el-option label="张明远" value="1" />
                    <el-option label="李思雨" value="2" />
                    <el-option label="王建国" value="3" />
                    <el-option label="赵晓芳" value="4" />
                    <el-option label="陈志强" value="5" />
                  </el-select>
                </el-form-item>
                <el-form-item label="进度更新权限">
                  <el-radio-group>
                    <el-radio label="仅负责人" />
                    <el-radio label="改进团队" />
                    <el-radio label="协作人" />
                    <el-radio label="指定人员" />
                  </el-radio-group>
                  <el-select v-if="progressPermission === '指定人员'" multiple placeholder="请选择可更新进度人员" class="mt-2 w-full">
                    <el-option label="张明远" value="1" />
                    <el-option label="李思雨" value="2" />
                    <el-option label="王建国" value="3" />
                    <el-option label="赵晓芳" value="4" />
                    <el-option label="陈志强" value="5" />
                  </el-select>
                </el-form-item>
                <el-form-item label="评估权限">
                  <el-radio-group>
                    <el-radio label="监督人" />
                    <el-radio label="部门主管" />
                    <el-radio label="指定人员" />
                  </el-radio-group>
                  <el-select v-if="evaluatePermission === '指定人员'" multiple placeholder="请选择可评估人员" class="mt-2 w-full">
                    <el-option label="张明远" value="1" />
                    <el-option label="李思雨" value="2" />
                    <el-option label="王建国" value="3" />
                    <el-option label="赵晓芳" value="4" />
                    <el-option label="陈志强" value="5" />
                  </el-select>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </div>
        <div v-if="false" class="w-1/4">
          <div class="sticky top-6 space-y-6">
            <!-- 改进措施模板 -->
            <div class="rounded-lg bg-white p-6 shadow-sm">
              <h2 class="mb-4 text-lg font-bold">
                措施模板
              </h2>
              <div class="space-y-3">
                <div
                  v-for="(template, index) in templates"
                  :key="index"
                  class="cursor-pointer border rounded p-3 hover:bg-gray-50"
                >
                  <div class="font-medium">
                    {{ template.name }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ template.scenario }}
                  </div>
                </div>
              </div>
              <el-button class="!rounded-button mt-4 w-full whitespace-nowrap">
                <el-icon><ElIconPlus /></el-icon>
                <span>保存为模板</span>
              </el-button>
            </div>

            <!-- 相关案例 -->
            <div class="rounded-lg bg-white p-6 shadow-sm">
              <h2 class="mb-4 text-lg font-bold">
                相关案例
              </h2>
              <div class="space-y-3">
                <div
                  v-for="(caseItem, index) in relatedCases"
                  :key="index"
                  class="cursor-pointer border rounded p-3 hover:bg-gray-50"
                >
                  <div class="flex items-start justify-between">
                    <div class="font-medium">
                      {{ caseItem.title }}
                    </div>
                    <el-tag size="small" :type="caseItem.status === '已完成' ? 'success' : 'warning'">
                      {{ caseItem.status }}
                    </el-tag>
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ caseItem.type }}
                  </div>
                  <div class="text-xs text-blue-500">
                    相似度: {{ caseItem.similarity }}%
                  </div>
                </div>
              </div>
            </div>

            <!-- AI辅助 -->
            <div class="rounded-lg bg-white p-6 shadow-sm">
              <h2 class="mb-4 text-lg font-bold">
                AI辅助
              </h2>
              <div class="space-y-3">
                <el-button
                  class="!rounded-button w-full whitespace-nowrap"
                  @click="generateSolution"
                >
                  <el-icon><ElIconMagicStick /></el-icon>
                  <span>AI生成实施方案</span>
                </el-button>
                <el-button
                  class="!rounded-button w-full whitespace-nowrap"
                  @click="identifyRisks"
                >
                  <el-icon><ElIconWarning /></el-icon>
                  <span>AI风险识别</span>
                </el-button>
                <el-button
                  class="!rounded-button w-full whitespace-nowrap"
                  @click="suggestMetrics"
                >
                  <el-icon><ElIconDataLine /></el-icon>
                  <span>AI评估指标建议</span>
                </el-button>
                <div
                  v-if="aiSuggestion"
                  class="mt-3 rounded bg-blue-50 p-3 text-sm"
                >
                  {{ aiSuggestion }}
                </div>
              </div>
            </div>

            <!-- 参考资料 -->
            <div class="rounded-lg bg-white p-6 shadow-sm">
              <h2 class="mb-4 text-lg font-bold">
                参考资料
              </h2>
              <div class="space-y-2">
                <a
                  v-for="(ref, index) in references"
                  :key="index"
                  :href="ref.link"
                  target="_blank"
                  class="block text-sm text-blue-500 hover:text-blue-700"
                >
                  {{ ref.title }}
                </a>
                <a href="#" class="mt-2 block text-sm text-blue-500 hover:text-blue-700">查看更多</a>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
:deep(.el-form-item__label) {
  text-align: right;
  color: #666;
  font-size: 14px;
}
:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  height: 32px;
  border-radius: 4px;
}
:deep(.el-textarea__inner) {
  min-height: 100px !important;
}
:deep(.el-form-item) {
  margin-bottom: 24px;
}
:deep(.el-table) {
  margin-top: 8px;
}
</style>
