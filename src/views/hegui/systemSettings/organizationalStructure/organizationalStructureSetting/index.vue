<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Search } from '@element-plus/icons-vue'
import organizationalApi from '@/api/organizational/index'

// 数据状态
const loading = ref(false)
const treeData = ref([])
const expandedKeys = ref([])
const searchKeyword = ref('')
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const formRef = ref()

// 表单数据
const formData = ref({
  id: null,
  name: '',
  code: '',
  type: 'DEPARTMENT',
  level: 1,
  status: 1,
  sortOrder: 0,
  description: '',
  metadata: '',
  parentId: null,
  version: 0
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入组织单位名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入唯一编码', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择组织单元类型', trigger: 'change' }
  ]
}

// 组织单元类型选项
const typeOptions = [
  { label: '公司', value: 'COMPANY' },
  { label: '子公司', value: 'SUBSIDIARY' },
  { label: '事业群', value: 'BUSINESS_GROUP' },
  { label: '部门', value: 'DEPARTMENT' },
  { label: '团队', value: 'TEAM' }
]

// 获取组织架构树形数据
async function fetchOrgTree() {
  try {
    loading.value = true
    const response = await organizationalApi.organizationalUnitTreeApi()
    treeData.value = response.data || []
    // 默认展开第一层
    if (treeData.value.length > 0) {
      expandedKeys.value = [treeData.value[0].id]
    }
  }
  catch (error) {
    console.error('获取组织架构失败:', error)
    ElMessage.error('获取组织架构失败')
  }
  finally {
    loading.value = false
  }
}

// 重置表单
function resetForm() {
  formData.value = {
    id: null,
    name: '',
    code: '',
    type: 'DEPARTMENT',
    level: 1,
    status: 1,
    sortOrder: 0,
    description: '',
    metadata: '',
    parentId: null,
    version: 0
  }
  formRef.value?.clearValidate()
}

// 新增组织单元
function addOrgUnit(parentNode?: any) {
  resetForm()
  if (parentNode) {
    formData.value.parentId = parentNode.id
    formData.value.level = parentNode.level + 1
  }
  dialogTitle.value = '新增组织单元'
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑组织单元
function editOrgUnit(node: any) {
  resetForm()
  formData.value = {
    id: node.id,
    name: node.name,
    code: node.code,
    type: node.type,
    level: node.level,
    status: node.status,
    sortOrder: node.sortOrder || 0,
    description: node.description || '',
    metadata: node.metadata || '',
    parentId: node.parentId,
    version: node.version
  }
  dialogTitle.value = '编辑组织单元'
  isEdit.value = true
  dialogVisible.value = true
}

// 保存组织单元
async function saveOrgUnit() {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      // 更新组织单元
      await organizationalApi.organizationalUnitApi({ id: formData.value.id }, formData.value, 'update')
      ElMessage.success('更新组织单元成功')
    }
    else {
      // 创建组织单元
      await organizationalApi.organizationalUnitApi({}, formData.value, 'create')
      ElMessage.success('创建组织单元成功')
    }

    dialogVisible.value = false
    await fetchOrgTree()
  }
  catch (error) {
    console.error('保存组织单元失败:', error)
    ElMessage.error('保存组织单元失败')
  }
  finally {
    loading.value = false
  }
}

// 删除组织单元
async function deleteOrgUnit(node: any) {
  try {
    await ElMessageBox.confirm('确定要删除这个组织单元吗？删除后不可恢复！', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await organizationalApi.organizationalUnitApi({ id: node.id }, {}, 'delete')
    ElMessage.success('删除成功')
    await fetchOrgTree()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除组织单元失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 获取类型名称
function getTypeName(type: string) {
  const option = typeOptions.find(item => item.value === type)
  return option?.label || type
}

// 获取状态标签
function getStatusTag(status: number) {
  return status === 1 ? 'success' : 'danger'
}

// 获取状态名称
function getStatusName(status: number) {
  return status === 1 ? '启用' : '禁用'
}

// 格式化时间
function formatTime(timeObj: any) {
  if (!timeObj || !timeObj.seconds) return '-'
  const date = new Date(timeObj.seconds * 1000)
  return date.toLocaleDateString('zh-CN')
}

// 搜索过滤
function filterNode(value: string, data: any) {
  if (!value) return true
  return data.name.includes(value) || data.code.includes(value)
}

// 监听搜索关键词变化
function handleSearch() {
  // Element Plus Tree 组件会自动处理过滤
}

onMounted(() => {
  fetchOrgTree()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              组织架构设置
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'organizationalStructureSetting/index/insert'" type="primary" :icon="Plus" @click="addOrgUnit()">
              新增根节点
            </el-button>
          </div>
        </div>
      </template>
    </page-header>

    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-card shadow="hover">
              <template #header>
                <div class="flex justify-between items-center">
                  <div class="f-16 fw-600">组织架构树</div>
                  <el-input
                    v-model="searchKeyword"
                    placeholder="搜索组织单元名称或编码"
                    :prefix-icon="Search"
                    style="width: 300px"
                    clearable
                    @input="handleSearch"
                  />
                </div>
              </template>

              <div v-loading="loading" class="tree-container">
                <el-tree
                  :data="treeData"
                  :props="{ children: 'children', label: 'name' }"
                  :expand-on-click-node="false"
                  :default-expanded-keys="expandedKeys"
                  :filter-node-method="filterNode"
                  ref="treeRef"
                  node-key="id"
                  class="org-tree"
                >
                  <template #default="{ node, data }">
                    <div class="tree-node">
                      <div class="node-content">
                        <div class="node-info">
                          <span class="node-name">{{ data.name }}</span>
                          <el-tag :type="getStatusTag(data.status)" size="small" class="ml-2">
                            {{ getStatusName(data.status) }}
                          </el-tag>
                          <el-tag type="info" size="small" class="ml-1">
                            {{ getTypeName(data.type) }}
                          </el-tag>
                        </div>
                        <div class="node-meta">
                          <span class="node-code">编码: {{ data.code }}</span>
                          <span class="node-level">层级: {{ data.level }}</span>
                          <span class="node-time">创建: {{ formatTime(data.createdAt) }}</span>
                        </div>
                      </div>
                      <div class="node-actions">
                        <el-button type="primary" link size="small" @click="addOrgUnit(data)">
                          <el-icon><Plus /></el-icon>
                          新增子节点
                        </el-button>
                        <el-button type="primary" link size="small" @click="editOrgUnit(data)">
                          <el-icon><Edit /></el-icon>
                          编辑
                        </el-button>
                        <el-button type="danger" link size="small" @click="deleteOrgUnit(data)">
                          <el-icon><Delete /></el-icon>
                          删除
                        </el-button>
                      </div>
                    </div>
                  </template>
                </el-tree>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>

    <!-- 新增/编辑组织单元弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="组织单位名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入组织单位名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="唯一编码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入唯一编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="组织单元类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择组织单元类型" style="width: 100%">
                <el-option
                  v-for="option in typeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="formData.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="层级深度">
              <el-input-number v-model="formData.level" :min="1" :max="10" style="width: 100%" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序序号">
              <el-input-number v-model="formData.sortOrder" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述信息">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入描述信息" />
        </el-form-item>

        <el-form-item label="扩展元数据">
          <el-input v-model="formData.metadata" type="textarea" :rows="2" placeholder="请输入扩展元数据（JSON格式）" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="saveOrgUnit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/toolsCss";

.tree-container {
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
}

.org-tree {
  :deep(.el-tree-node__content) {
    height: auto;
    padding: 8px 0;
  }
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }
}

.node-content {
  flex: 1;
  min-width: 0;
}

.node-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.node-name {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
}

.node-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.node-code,
.node-level,
.node-time {
  white-space: nowrap;
}

.node-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.cssTable {
  :deep(.el-table__header-wrapper .el-table__cell) {
    height: 55px;
    background: #f5f7fa;
  }
}
</style>

<style scoped>
.el-tree {
  --el-tree-node-hover-bg-color: transparent;
}

.el-tag {
  --el-tag-border-radius: 4px;
}

.el-button--text {
  min-height: auto;
  padding: 0;
}

.el-input {
  --el-input-height: 36px;
}

.el-select {
  --el-select-height: 36px;
}
</style>
