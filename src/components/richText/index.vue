<script setup lang="ts">
import {
  ref,
} from 'vue'
import tinymce from 'tinymce/tinymce'
import TinymceEditor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver/theme'
import 'tinymce/icons/default/icons'
import 'tinymce/models/dom'
import 'tinymce/plugins/autolink'
import 'tinymce/plugins/autoresize'
import 'tinymce/plugins/image'
import 'tinymce/plugins/insertdatetime'
import 'tinymce/plugins/link'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/media'
import 'tinymce/plugins/preview'
import 'tinymce/plugins/table'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/code'
import 'tinymce/plugins/searchreplace'

import useSettingsStore from '@/store/modules/settings'

const props = withDefaults(
  defineProps<{
    content?: any
  }>(),
  {
    content: '',
  },
)
const emits = defineEmits(['change'])
const settingsStore = useSettingsStore()

const defaultSetting = ref({
  language_url: 'tinymce/langs/zh-Hans.js',
  language: 'zh-Hans',
  skin_url: settingsStore.settings.app.colorScheme === 'light'
    ? 'tinymce/skins/ui/oxide'
    : 'tinymce/skins/ui/oxide-dark',
  content_css: settingsStore.settings.app.colorScheme === 'light'
    ? 'tinymce/skins/content/default/content.min.css'
    : 'tinymce/skins/content/dark/content.min.css',
  min_height: 250,
  max_height: 600,
  selector: 'textarea',
  plugins: 'autolink autoresize image insertdatetime link lists media preview table wordcount code searchreplace',
  toolbar: 'undo redo | blocks | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | forecolor backcolor removeformat | link image media table insertdatetime searchreplace | preview code',
  branding: false,
  menubar: false,
  statusbar: false, // 是否显示底部状态栏
  toolbar_mode: 'sliding',
  insertdatetime_formats: [
    '%Y年%m月%d日',
    '%H点%M分%S秒',
    '%Y-%m-%d',
    '%H:%M:%S',
  ],
  images_upload_handler: (blobInfo: any) => new Promise((resolve) => {
    const img = `data:image/jpeg;base64,${blobInfo.base64()}`
    resolve(img)
  }),
  // images_upload_handler: (blobInfo: any) => new Promise((resolve) => {
  //   upload(blobInfo, (e: any) => {
  //     resolve(e)
  //   })
  // }),
})
// 上传接口
// function upload(blobInfo: any, fn: any) {
//   const formData = new FormData()
//   // formData.append('dirname', pictureType('agreement'))// 传参数
//   formData.append('file', blobInfo.blob())
//   // 这里为自己的上传接口调用方法
//   userApi.upload(formData).then((res: any) => {
//     if (res.code === 0) {
//       const baseurl: any = import.meta.env.VITE_APP_API_BASEURLIMG
//       fn && fn(baseurl + res.data)
//     }
//     else {
//       ElMessage.error('图片上传失败')
//       fn && fn('')
//     }
//   })
// }
const content: any = ref('')
onMounted(() => {
  tinymce.init({})
})
watch(() => content.value, (val: any) => {
  emits('change', val) // 告诉父组件
}, {
  immediate: true,
  deep: true,
})
watch(() => props.content, (val: any) => {
  content.value = val
}, {
  immediate: true,
  deep: true,
})
</script>

<template>
  <div>
    <TinymceEditor v-model="content" :init="defaultSetting" />
  </div>
</template>
