<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import assessApi from '@/api/complianceApi/prevention/assess'

// 定义组件名称，解决 keep-alive 缓存问题
defineOptions({
  name: 'ExaminationList',
})

// 路由实例
const router = useRouter()

// 接口类型定义
interface Examination {
  examId: string
  examName: string
  examDuration: number
  questionCount: number
  passScore: number
  score?: number
  examStatus: 'PENDING' | 'ONGOING' | 'COMPLETED'
  isPassed?: boolean
  startTime?: string
  endTime?: string
}

interface ExamFilters {
  examStatus: string
  searchTerm: string
}

// 响应式数据
const loading = ref(false)
const examPage = ref(1)
const examPageSize = ref(10)
const examTotal = ref(0)
const examFilters = ref<ExamFilters>({
  examStatus: '',
  searchTerm: '',
})
function goAddEdit(item: any) {
  if (item?.examId) {
    // 编辑考核
    router.push({
      name: '/training/examination/addEdit',
      query: { examId: item.examId },
    })
  }
  else {
    // 新增考核
    router.push({
      name: '/training/examination/addEdit',
    })
  }
}
// 考试列表数据
const examList = ref<Examination[]>([])

// Tab分类
const tabs = ref([
  { label: '全部', value: '' },
  { label: '待考核', value: 'PENDING' },
  { label: '已完成', value: 'COMPLETED' },
])
const activeTab = ref(0)

// 工具函数
function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    PENDING: '待考核',
    ONGOING: '进行中',
    COMPLETED: '已完成',
  }
  return statusMap[status] || '待考核'
}

function getStatusType(status: string): 'info' | 'warning' | 'success' | 'primary' | 'danger' {
  const typeMap: Record<string, 'info' | 'warning' | 'success' | 'primary' | 'danger'> = {
    PENDING: 'info',
    ONGOING: 'warning',
    COMPLETED: 'success',
  }
  return typeMap[status] || 'info'
}

// 事件处理函数

// 查询考试列表
async function queryExamList() {
  loading.value = true
  try {
    const params = {
      page: examPage.value - 1, // 接口页码从0开始
      size: examPageSize.value,
      examStatus: examFilters.value.examStatus || null,
      searchTerm: examFilters.value.searchTerm || null,
    }

    const response = await assessApi.getExamList(params)

    if (response && response.content) {
      examList.value = response.content
      examTotal.value = response.totalElements || 0
    }
    else {
      examList.value = []
      examTotal.value = 0
    }
  }
  catch (error) {
    console.error('获取考试列表失败:', error)
    ElMessage.error('获取考试列表失败')
    examList.value = []
    examTotal.value = 0
  }
  finally {
    loading.value = false
  }
}

// Tab切换处理
function handleTabChange(index: number) {
  activeTab.value = index
  examFilters.value.examStatus = tabs.value[index].value
  examPage.value = 1
  queryExamList()
}

// 搜索处理
function handleSearch() {
  examPage.value = 1
  queryExamList()
}

// 重置搜索
function resetFilters() {
  examFilters.value = {
    examStatus: '',
    searchTerm: '',
  }
  activeTab.value = 0
  examPage.value = 1
  queryExamList()
}

// 查看考试详情
function goToDetail(exam: Examination) {
  router.push({
    name: '/training/examination/detail',
    query: { examId: exam.examId },
  })
}

// 查看错题解析
function goToErrorAnalysis(exam: Examination) {
  // 这里需要考试记录ID，暂时使用examId，实际应该从接口获取
  router.push({
    path: '/training/examination/error',
    query: { examRecordId: exam.examId },
  })
}

// 开始考试
async function startExam(exam: Examination) {
  try {
    await ElMessageBox.confirm(
      `确定要开始考试"${exam.examName}"吗？`,
      '确认开始考试',
      {
        confirmButtonText: '开始考试',
        cancelButtonText: '取消',
        type: 'info',
      },
    )

    const response = await assessApi.startExam(exam.examId)
    if (response) {
      ElMessage.success('考试已开始')
      // 跳转到考试页面
      router.push({
        path: '/training/examination/center',
        query: {
          examId: exam.examId,
          examDuration: exam.examDuration,
        },
      })
    }
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('开始考试失败:', error)
      ElMessage.error('开始考试失败')
    }
  }
}

// 分页处理
function handlePageChange() {
  queryExamList()
}

// 监听过滤条件变化
watch(
  () => [examPage.value, examPageSize.value],
  () => {
    queryExamList()
  },
)

// 组件挂载时的初始化
onMounted(() => {
  queryExamList()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-1">
    <page-header>
      <template #content>
        <div class="aic jcsb flex">
          <div class="f-20 c-['#000000']">
            <h2 class="text-xl text-gray-800 font-semibold">
              考试管理
            </h2>
          </div>
          <div>
            <el-button
              v-auth="['examination/index/addauth']"
              type="primary" @click="goAddEdit(null)"
            >
              <svg-icon name="ep:plus" />
              <span class="ml-4">新增考核认证</span>
            </el-button>
          </div>
        </div>
      </template>
    </page-header>

    <!-- 筛选区域 -->
    <div class="mb-4 rounded-lg bg-white p-4 shadow-sm">
      <!-- Tab分类 -->
      <div class="mb-4">
        <div class="flex space-x-6">
          <div
            v-for="(tab, index) in tabs"
            :key="index"
            class="cursor-pointer pb-2 text-sm font-medium transition-colors"
            :class="[
              activeTab === index
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-600 hover:text-gray-800',
            ]"
            @click="handleTabChange(index)"
          >
            {{ tab.label }}
          </div>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="flex items-center gap-4">
        <el-input
          v-model="examFilters.searchTerm"
          placeholder="搜索考试名称"
          class="w-64"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <svg-icon name="ep:search" />
          </template>
        </el-input>

        <el-button type="primary" @click="handleSearch">
          查询
        </el-button>

        <el-button @click="resetFilters">
          重置
        </el-button>
      </div>
    </div>

    <!-- 考试列表 -->
    <div class="overflow-hidden rounded-lg bg-white shadow-sm">
      <el-table
        v-loading="loading"
        :data="examList"
        style="width: 100%"
        border
        element-loading-text="加载中..."
      >
        <el-table-column prop="examName" label="考试名称" show-overflow-tooltip />

        <el-table-column prop="examDuration" label="考试时长" align="center">
          <template #default="{ row }">
            <el-tag size="small" effect="plain">
              {{ row.examDuration }}分钟
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="questionCount" label="题目数量" align="center">
          <template #default="{ row }">
            <el-tag size="small" effect="plain">
              {{ row.questionCount }}题
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="passScore" label="及格分" align="center">
          <template #default="{ row }">
            <span class="font-medium">{{ row.passScore }}分</span>
          </template>
        </el-table-column>

        <el-table-column prop="score" label="考试成绩" align="center">
          <template #default="{ row }">
            <div v-if="row.score !== null && row.score !== undefined">
              <span class="font-medium">{{ row.score }}分</span>
              <el-tag
                v-if="row.examStatus === 'COMPLETED'"
                :type="row.isPassed ? 'success' : 'danger'"
                size="small"
                class="ml-2"
              >
                {{ row.isPassed ? '合格' : '不合格' }}
              </el-tag>
            </div>
            <span v-else class="text-gray-400">暂无成绩</span>
          </template>
        </el-table-column>

        <el-table-column prop="examStatus" label="状态" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.examStatus)"
              effect="light"
              size="small"
            >
              {{ getStatusText(row.examStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="120">
          <template #default="{ row }">
            <el-button
              v-auth="['examination/index/detailauth']"
              type="primary" plain
              size="small"
              @click="goToDetail(row)"
            >
              查看详情
            </el-button>
            <!-- <el-button
              v-if="row.examStatus === 'PENDING'"
              type="success"
              size="small"
              @click="startExam(row)"
            >
              开始考试
            </el-button> -->
            <!-- <el-button
              v-if="row.examStatus === 'COMPLETED' && !row.isPassed"
              type="warning"
              size="small"
              @click="startExam(row)"
            >
              重新考试
            </el-button> -->
            <!-- <el-button
              v-if="row.examStatus === 'COMPLETED'"
              type="info"
              size="small"
              @click="goToErrorAnalysis(row)"
            >
              错题解析
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex items-center justify-between p-4">
        <span class="text-sm text-gray-600">
          共 {{ examTotal }} 条记录
        </span>
        <el-pagination
          v-model:current-page="examPage"
          v-model:page-size="examPageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="examTotal"
          layout="total, sizes, prev, pager, next, jumper"
          class="justify-end"
          @current-change="handlePageChange"
          @size-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
.cursor-pointer {
  cursor: pointer;
}
</style>
