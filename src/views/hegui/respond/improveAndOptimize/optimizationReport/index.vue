<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Collection as ElIconCollection,
  DataAnalysis as ElIconDataAnalysis,
  Document as ElIconDocument,
  Download as ElIconDownload,
  Files as ElIconFiles,
  Grid as ElIconGrid,
  MagicStick as ElIconMagicStick,
  Menu as ElIconMenu,
  <PERSON><PERSON><PERSON> as ElIconPieChart,
  Plus as ElIconPlus,
  Search as ElIconSearch,
  Star as ElIconStar,
  TrendCharts as ElIconTrendCharts,
  // TrendingUp as ElIconTrendingUp,
  Upload as ElIconUpload,
  View as ElIconView,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import continuousApi from '@/api/problemTask/continuous'
import dictApi from '@/api/modules/system/dict'

const router = useRouter()

function handleAdd() {
  router.push({
    path: '/respond/improveAndOptimize/optimizationReport/addEdit',
  })
}

// 查询参数
const searchParams = ref<{
  title: string | null
  reportCode: string | null
  reportType: string | null
  employeeId: string | null
  orgId: string | null
  establishDate: string | null
  level: string | null
  createdAtStart: string | null
  createdAtEnd: string | null
}>({
  title: null,
  reportCode: null,
  reportType: null,
  employeeId: null,
  orgId: null,
  establishDate: null,
  level: null,
  createdAtStart: null,
  createdAtEnd: null,
})

// 分页参数
const pagination = ref({
  page: 0,
  size: 10,
  total: 0,
})

// 日期范围
const dateRange = ref<[string, string] | undefined>(undefined)

// 关键字搜索
const keyword = ref('')

// 报告数据
const reportData = ref([])

// 加载状态
const loading = ref(false)

// 获取报告列表
async function getReportList() {
  try {
    loading.value = true

    // 处理日期范围
    if (dateRange.value && Array.isArray(dateRange.value) && dateRange.value.length === 2) {
      searchParams.value.createdAtStart = dateRange.value[0]
      searchParams.value.createdAtEnd = dateRange.value[1]
    }
    else {
      searchParams.value.createdAtStart = null
      searchParams.value.createdAtEnd = null
    }

    // 处理关键字搜索
    if (keyword.value) {
      searchParams.value.title = keyword.value
    }
    else {
      searchParams.value.title = null
    }

    const params = {
      page: pagination.value.page,
      size: pagination.value.size,
      ...searchParams.value,
    }

    const response = await continuousApi.searchReports(params)

    if (response && response.content) {
      reportData.value = response.content
      pagination.value.total = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('获取报告列表失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 分页改变
function handlePageChange(page: number) {
  pagination.value.page = page - 1
  getReportList()
}

// 页大小改变
function handleSizeChange(size: number) {
  pagination.value.size = size
  pagination.value.page = 0
  getReportList()
}

// 搜索
function handleSearch() {
  pagination.value.page = 0
  getReportList()
}

// 重置搜索
function handleReset() {
  searchParams.value = {
    title: null,
    reportCode: null,
    reportType: null,
    employeeId: null,
    orgId: null,
    establishDate: null,
    level: null,
    createdAtStart: null,
    createdAtEnd: null,
  }
  dateRange.value = undefined
  keyword.value = ''
  pagination.value.page = 0
  getReportList()
}

// 字典选项
const reportTypeOptions = ref<Array<{ label: string, value: string }>>([])
const levelOptions = ref<Array<{ label: string, value: string }>>([])

// 获取字典数据
async function fetchDictOptions() {
  try {
    // 获取报告类型字典 (字典类型 92)
    const reportTypeResponse = await dictApi.dictAll(92)
    if (reportTypeResponse) {
      reportTypeOptions.value = reportTypeResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }

    // 获取保密级别字典 (字典类型 41)
    const levelResponse = await dictApi.dictAll(41)
    if (levelResponse) {
      levelOptions.value = levelResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 格式化报告类型
function formatReportType(type: string) {
  const option = reportTypeOptions.value.find(item => item.value === type)
  return option ? option.label : type
}

// 格式化保密级别
function formatLevel(level: string) {
  const option = levelOptions.value.find(item => item.value === level)
  return option ? option.label : level
}

// 格式化日期
function formatDate(dateObj: any) {
  if (!dateObj) {
    return ''
  }
  if (typeof dateObj === 'string') {
    return dateObj
  }
  if (dateObj.seconds) {
    return new Date(dateObj.seconds * 1000).toLocaleDateString()
  }
  return ''
}

const topReports = ref([
  { rank: 1, title: '2024年第一季度合规运营全面分析', type: '季度报告', views: 456, createDate: '2024-04-05', author: '张明远' },
  { rank: 2, title: '数据隐私合规年度评估报告', type: '年度报告', views: 389, createDate: '2024-03-15', author: '李静怡' },
  { rank: 3, title: '反洗钱合规专项检查报告', type: '专项报告', views: 367, createDate: '2024-05-20', author: '王伟' },
  { rank: 4, title: '2024年1月合规风险监测报告', type: '月度报告', views: 342, createDate: '2024-02-01', author: '陈思' },
  { rank: 5, title: '信息安全合规整改效果评估', type: '专项报告', views: 321, createDate: '2024-04-18', author: '赵强' },
])
const recentViewed = ref([
  { id: 'RP202406001', title: '2024年6月合规风险分析报告', type: '月度报告', viewTime: '10分钟前', lastView: '2024-06-28 14:30' },
  { id: 'RP2024SP001', title: '数据隐私合规专项检查报告', type: '专项报告', viewTime: '1小时前', lastView: '2024-06-28 13:45' },
  { id: 'RP2024Q2001', title: '2024年第二季度合规运营总结', type: '季度报告', viewTime: '3小时前', lastView: '2024-06-28 11:20' },
  { id: 'RP2024SP002', title: '信息安全合规整改报告', type: '专项报告', viewTime: '昨天', lastView: '2024-06-27 16:10' },
  { id: 'RP202406002', title: '反洗钱合规月度监测报告', type: '月度报告', viewTime: '昨天', lastView: '2024-06-27 09:30' },
  { id: 'RP2024Q2002', title: '2024年第二季度合规培训效果评估', type: '季度报告', viewTime: '2天前', lastView: '2024-06-26 14:15' },
])

const _myReports = ref({
  created: [
    { id: 'RP202406001', title: '2024年6月合规风险分析报告', status: '已发布', date: '06-28' },
    { id: 'RP202406003', title: '员工行为规范月度评估', status: '草稿', date: '06-12' },
    { id: 'RP2024SP003', title: '合同管理合规性专项审计报告', status: '已发布', date: '06-08' },
  ],
  pending: [
    { id: 'RP2024Q2001', title: '2024年第二季度合规运营总结', date: '06-25' },
    { id: 'RP202406004', title: '2024年6月合规政策更新报告', date: '06-05' },
  ],
  published: [
    { id: 'RP2024SP001', title: '数据隐私合规专项检查报告', date: '06-20' },
    { id: 'RP202406002', title: '反洗钱合规月度监测报告', date: '06-18' },
    { id: 'RP2024Q2002', title: '2024年第二季度合规培训效果评估', date: '06-10' },
  ],
})

const _hotReports = ref([
  { id: 'RP2024SP001', title: '数据隐私合规专项检查报告', views: 367, date: '06-20' },
  { id: 'RP202406001', title: '2024年6月合规风险分析报告', views: 156, date: '06-28' },
  { id: 'RP202406002', title: '反洗钱合规月度监测报告', views: 187, date: '06-18' },
])

const _hotReportPeriod = ref('week')
const trendChart = ref<HTMLElement>()
const pieChart = ref<HTMLElement>()
function getTypeClass(type: string) {
  const classes: Record<string, string> = {
    月度报告: 'text-blue-500',
    季度报告: 'text-green-500',
    年度报告: 'text-purple-500',
    专项报告: 'text-orange-500',
    系统升级: 'text-blue-500',
    培训教育: 'text-green-500',
    流程优化: 'text-purple-500',
    政策更新: 'text-orange-500',
  }
  return classes[type] || 'text-gray-500'
}
function _getStatusClass(status: string) {
  const classes: Record<string, string> = {
    草稿: 'text-gray-500',
    审核中: 'text-blue-500',
    已发布: 'text-green-500',
    已归档: 'text-indigo-500',
  }
  return classes[status] || 'text-gray-500'
}

// 查看报告详情
function handleView(row: any) {
  router.push({
    path: '/respond/improveAndOptimize/optimizationReport/detail',
    query: { id: row.id },
  })
}

// 编辑报告
function handleEdit(row: any) {
  router.push({
    path: '/respond/improveAndOptimize/optimizationReport/addEdit',
    query: { id: row.id, mode: 'edit' },
  })
}

// 删除报告
function handleDelete(row: any) {
  ElMessageBox.confirm(
    `确定要删除报告"${row.title}"吗？此操作不可撤销。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(async () => {
    try {
      await continuousApi.deleteReport(row.id)
      ElMessage.success('删除成功')
      // 重新加载列表
      getReportList()
    }
    catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

onMounted(() => {
  // 初始化加载数据
  getReportList()
  fetchDictOptions()

  nextTick(() => {
    if (trendChart.value) {
      const chart = echarts.init(trendChart.value)
      chart.setOption({
        animation: false,
        title: { text: '报告数量趋势', left: 'center' },
        tooltip: { trigger: 'axis' },
        legend: { data: ['月度报告', '季度报告', '专项报告'], bottom: 10 },
        grid: { left: '3%', right: '4%', bottom: '15%', top: '15%', containLabel: true },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
          axisLine: { lineStyle: { color: '#ccc' } },
        },
        yAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#ccc' } },
          splitLine: { lineStyle: { type: 'dashed' } },
        },
        series: [
          { name: '月度报告', type: 'line', data: [12, 15, 18, 14, 16, 20], smooth: true, lineStyle: { width: 3 }, symbolSize: 8 },
          { name: '季度报告', type: 'line', data: [3, 2, 4, 5, 3, 4], smooth: true, lineStyle: { width: 3 }, symbolSize: 8 },
          { name: '专项报告', type: 'line', data: [5, 4, 6, 8, 7, 9], smooth: true, lineStyle: { width: 3 }, symbolSize: 8 },
        ],
      })
    }
    if (pieChart.value) {
      const chart = echarts.init(pieChart.value)
      chart.setOption({
        animation: false,
        title: { text: '报告类型分布', left: 'center' },
        tooltip: { trigger: 'item' },
        legend: { orient: 'vertical', right: 10, top: 'center' },
        series: [
          {
            name: '报告类型',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: { borderRadius: 10, borderColor: '#fff', borderWidth: 2 },
            label: { show: false, position: 'center' },
            emphasis: { label: { show: true, fontSize: '18', fontWeight: 'bold' } },
            labelLine: { show: false },
            data: [
              { value: 684, name: '月度报告' },
              { value: 240, name: '季度报告' },
              { value: 180, name: '年度报告' },
              { value: 180, name: '专项报告' },
            ],
          },
        ],
      })
    }
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              优化报告
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'optimizationReport/index/add'" type="primary" class="!rounded-button whitespace-nowrap" @click="handleAdd">
              <!-- <el-icon class="mr-1">
                <ElIconPlus />
              </el-icon> -->
              新增报告
            </el-button>
            <el-button v-auth="'optimizationReport/index/batchExport'" plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconDownload />
              </el-icon>
              批量导出
            </el-button>
            <el-button v-auth="'optimizationReport/index/statisticalAnalysis'" plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconDataAnalysis />
              </el-icon>
              统计分析
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="24">
            <el-row v-if="false" :gutter="20" class="">
              <el-col :span="6">
                <el-card shadow="hover" class="">
                  <!-- <template #header>
                    <div class="f-16 fw-600">基本信息</div>
                  </template> -->
                  <div class="flex justify-between">
                    <div>
                      <div class="text-sm text-gray-500">
                        报告总数
                      </div>
                      <div class="mt-1 text-2xl font-bold">
                        1,284
                      </div>
                      <div class="mt-1 flex items-center text-sm text-green-500">
                        <el-icon class="mr-1" />12.5%
                      </div>
                    </div>
                    <div class="h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                      <el-icon class="text-xl text-blue-500">
                        <ElIconDocument />
                      </el-icon>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="">
                  <div class="flex justify-between">
                    <div>
                      <div class="text-sm text-gray-500">
                        本月新增
                      </div>
                      <div class="mt-1 text-2xl font-bold">
                        86
                      </div>
                      <div class="mt-1 flex items-center text-sm text-green-500">
                        <el-icon class="mr-1" />8.2%
                      </div>
                    </div>
                    <div class="h-12 w-12 flex items-center justify-center rounded-full bg-green-100" />
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="">
                  <div class="flex justify-between">
                    <div>
                      <div class="text-sm text-gray-500">
                        平均阅读量
                      </div>
                      <div class="mt-1 text-2xl font-bold">
                        324
                      </div>
                      <div class="mt-1 flex items-center text-sm text-red-500" />
                    </div>
                    <div class="h-12 w-12 flex items-center justify-center rounded-full bg-purple-100">
                      <el-icon class="text-xl text-purple-500">
                        <ElIconView />
                      </el-icon>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="">
                  <div class="flex justify-between">
                    <div>
                      <div class="text-sm text-gray-500">
                        高引用率
                      </div>
                      <div class="mt-1 text-2xl font-bold">
                        68%
                      </div>
                      <div class="mt-1 flex items-center text-sm text-green-500">
                        <el-icon class="mr-1" />5.7%
                      </div>
                    </div>
                    <div class="h-12 w-12 flex items-center justify-center rounded-full bg-orange-100">
                      <el-icon class="text-xl text-orange-500">
                        <ElIconStar />
                      </el-icon>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
            <el-card shadow="hover" class="mt-20">
              <!-- 快速筛选区 -->
              <div class="rounded-lg bg-white p-4 shadow">
                <div class="flex overflow-x-auto pb-2 space-x-2">
                  <button
                    v-auth="'optimizationReport/index/filterAll'"
                    class="!rounded-button whitespace-nowrap bg-blue-500 px-4 py-1.5 text-sm text-white"
                  >
                    全部
                  </button>
                  <button
                    v-auth="'optimizationReport/index/filterMonthly'"
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-1.5 text-sm hover:bg-gray-50"
                  >
                    月度报告
                  </button>
                  <button
                    v-auth="'optimizationReport/index/filterQuarterly'"
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-1.5 text-sm hover:bg-gray-50"
                  >
                    季度报告
                  </button>
                  <button
                    v-auth="'optimizationReport/index/filterYearly'"
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-1.5 text-sm hover:bg-gray-50"
                  >
                    年度报告
                  </button>
                  <button
                    v-auth="'optimizationReport/index/filterSpecial'"
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-1.5 text-sm hover:bg-gray-50"
                  >
                    专项报告
                  </button>
                  <button
                    v-auth="'optimizationReport/index/filterHighViews'"
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-1.5 text-sm hover:bg-gray-50"
                  >
                    高阅读量
                  </button>
                </div>
              </div>
              <!-- 筛选区 -->
              <el-card shadow="hover" class="mb-6 !rounded-lg">
                <div class="mb-4 flex items-center space-x-4">
                  <el-select v-model="searchParams.reportType" placeholder="报告类型" class="w-48" clearable>
                    <el-option
                      v-for="option in reportTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  <el-select v-model="searchParams.level" placeholder="保密级别" class="w-48" clearable>
                    <el-option
                      v-for="option in levelOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  <!-- <el-input v-model="searchParams.reportCode" placeholder="报告编号" class="w-48" clearable /> -->
                  <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    class="w-64"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                  />
                  <el-input v-model="keyword" placeholder="关键字搜索" class="w-48" @keyup.enter="handleSearch">
                    <template #prefix>
                      <el-icon>
                        <ElIconSearch />
                      </el-icon>
                    </template>
                  </el-input>
                </div>
                <div class="flex justify-end">
                  <!-- v-auth="'optimizationReport/index/search'" -->
                  <el-button
                    type="primary"
                    class="!rounded-button whitespace-nowrap"
                    @click="handleSearch"
                  >
                    查询
                  </el-button>
                  <el-button
                    class="!rounded-button whitespace-nowrap"
                    @click="handleReset"
                  >
                    重置
                  </el-button>
                </div>
              </el-card>
              <!-- 报告列表区 -->
              <div class="rounded-lg bg-white shadow">
                <div class="flex items-center justify-between border-b border-gray-200 p-4">
                  <div class="text-gray-700">
                    共 {{ pagination.total }} 条报告
                  </div>
                  <div class="flex space-x-2">
                    <button
                      v-auth="'optimizationReport/index/tableView'"
                      class="!rounded-button whitespace-nowrap border border-gray-300 px-3 py-1 text-sm hover:bg-gray-50"
                    >
                      <el-icon class="mr-1">
                        <ElIconMenu />
                      </el-icon>表格视图
                    </button>
                    <button
                      v-auth="'optimizationReport/index/cardView'"
                      class="!rounded-button whitespace-nowrap border border-blue-500 px-3 py-1 text-sm text-blue-500 hover:bg-blue-50"
                    >
                      <el-icon class="mr-1">
                        <ElIconGrid />
                      </el-icon>卡片视图
                    </button>
                  </div>
                </div>
                <div class="p-4">
                  <el-table v-loading="loading" :data="reportData" style="width: 100%;">
                    <el-table-column type="selection" width="50" />
                    <el-table-column prop="title" label="报告标题" min-width="200" />
                    <el-table-column prop="reportType" label="报告类型" width="120">
                      <template #default="{ row }">
                        <span :class="getTypeClass(formatReportType(row.reportType))">{{ formatReportType(row.reportType) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="reportCycle" label="报告周期" width="120" />
                    <el-table-column label="覆盖时间" width="180">
                      <template #default="{ row }">
                        {{ formatDate(row.startDate) }} 至 {{ formatDate(row.endDate) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="createdBy" label="编制人" width="120" />
                    <el-table-column prop="establishDate" label="编制日期" width="120" />
                    <el-table-column prop="level" label="保密级别" width="120">
                      <template #default="{ row }">
                        <span>{{ formatLevel(row.level) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="210">
                      <template #default="{ row }">
                        <!-- v-auth="'optimizationReport/index/view'" -->
                        <el-button
                          v-auth="'optimizationReport/index/view'"
                          type="primary" plain
                          size="small"
                          @click="handleView(row)"
                        >
                          查看
                        </el-button>
                        <el-button
                          v-auth="'optimizationReport/index/edit'"
                          type="warning" plain
                          size="small"
                          @click="handleEdit(row)"
                        >
                          编辑
                        </el-button>
                        <el-button
                          v-auth="'optimizationReport/index/delete'"
                          type="danger" plain
                          size="small"
                          @click="handleDelete(row)"
                        >
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <div class="flex items-center justify-between border-t border-gray-200 p-4">
                  <div class="text-sm text-gray-500">
                    显示 {{ (pagination.page * pagination.size) + 1 }}-{{ Math.min((pagination.page + 1) * pagination.size, pagination.total) }} 条，共 {{ pagination.total }} 条
                  </div>
                  <el-pagination
                    :total="pagination.total"
                    :page-size="pagination.size"
                    :current-page="pagination.page + 1"
                    layout="prev, pager, next, jumper, sizes"
                    :page-sizes="[10, 20, 50, 100]"
                    background
                    @current-change="handlePageChange"
                    @size-change="handleSizeChange"
                  />
                </div>
              </div>
            </el-card>
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  报告统计
                </div>
              </template>
              <div class="grid grid-cols-2 gap-4">
                <div class="h-80">
                  <div ref="trendChart" class="h-full w-full" />
                </div>
                <div class="h-80">
                  <div ref="pieChart" class="h-full w-full" />
                </div>
              </div>
            </el-card>
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  生成报告
                </div>
              </template>
              <div class="mb-6 flex items-center justify-between">
                <div>
                  <p class="mb-2 text-gray-600">
                    使用向导快速生成标准化的合规报告
                  </p>
                  <p class="text-sm text-gray-500">
                    支持多种报告类型和自定义模板
                  </p>
                </div>
                <button v-auth="'optimizationReport/index/startGenerate'" class="!rounded-button whitespace-nowrap bg-blue-500 px-4 py-2 text-white hover:bg-blue-600">
                  开始生成
                </button>
              </div>
              <div class="grid grid-cols-4 gap-4">
                <div class="cursor-pointer border border-gray-200 rounded-lg p-4 hover:shadow-md">
                  <div class="mb-3 h-32 flex items-center justify-center rounded-md bg-blue-50">
                    <el-icon class="text-4xl text-blue-500">
                      <ElIconDocument />
                    </el-icon>
                  </div>
                  <h3 class="mb-1 font-medium">
                    月度总结模板
                  </h3>
                  <p class="text-sm text-gray-500">
                    适用于月度合规情况汇总
                  </p>
                  <div v-auth="'optimizationReport/index/useMonthlyTemplate'" class="mt-3 text-sm text-blue-500">
                    使用模板
                  </div>
                </div>
                <div class="cursor-pointer border border-gray-200 rounded-lg p-4 hover:shadow-md">
                  <div class="mb-3 h-32 flex items-center justify-center rounded-md bg-green-50">
                    <el-icon class="text-4xl text-green-500">
                      <ElIconDataAnalysis />
                    </el-icon>
                  </div>
                  <h3 class="mb-1 font-medium">
                    季度分析模板
                  </h3>
                  <p class="text-sm text-gray-500">
                    适用于季度合规数据分析
                  </p>
                  <div v-auth="'optimizationReport/index/useQuarterlyTemplate'" class="mt-3 text-sm text-blue-500">
                    使用模板
                  </div>
                </div>
                <div class="cursor-pointer border border-gray-200 rounded-lg p-4 hover:shadow-md">
                  <div class="mb-3 h-32 flex items-center justify-center rounded-md bg-purple-50">
                    <el-icon class="text-4xl text-purple-500">
                      <ElIconPieChart />
                    </el-icon>
                  </div>
                  <h3 class="mb-1 font-medium">
                    年度回顾模板
                  </h3>
                  <p class="text-sm text-gray-500">
                    适用于年度合规工作总结
                  </p>
                  <div v-auth="'optimizationReport/index/useYearlyTemplate'" class="mt-3 text-sm text-blue-500">
                    使用模板
                  </div>
                </div>
                <div class="cursor-pointer border border-gray-200 rounded-lg p-4 hover:shadow-md">
                  <div class="mb-3 h-32 flex items-center justify-center rounded-md bg-orange-50">
                    <el-icon class="text-4xl text-orange-500">
                      <ElIconFiles />
                    </el-icon>
                  </div>
                  <h3 class="mb-1 font-medium">
                    专项分析模板
                  </h3>
                  <p class="text-sm text-gray-500">
                    适用于特定合规问题分析
                  </p>
                  <div v-auth="'optimizationReport/index/useSpecialTemplate'" class="mt-3 text-sm text-blue-500">
                    使用模板
                  </div>
                </div>
              </div>
            </el-card>
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  最近查看
                </div>
              </template>
              <div class="flex overflow-x-auto pb-4 space-x-4">
                <div
                  v-for="item in recentViewed" :key="item.id"
                  class="min-w-[240px] cursor-pointer border border-gray-200 rounded-lg p-4 hover:shadow-md"
                >
                  <h3 class="mb-1 truncate font-medium">
                    {{ item.title }}
                  </h3>
                  <div class="mb-2 flex justify-between text-sm text-gray-500">
                    <span>{{ item.type }}</span>
                    <span>{{ item.viewTime }}</span>
                  </div>
                  <div class="text-xs text-gray-400">
                    上次查看: {{ item.lastView }}
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  :deep(.el-table .cell) {
    padding-right: 10px;
    padding-left: 10px;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f8fafc;
  }

  :deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
    background-color: #1e88e5;
  }

  :deep(.el-pagination.is-background .el-pager li:not(.is-disabled):hover) {
    color: #1e88e5;
  }

  :deep(.el-select .el-input__inner) {
    height: 32px;
  }

  :deep(.el-date-editor .el-range-input) {
    height: 30px;
  }
</style>
