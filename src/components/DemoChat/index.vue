<template>
  <div class="demo-chat-container">
    <div class="demo-header">
      <h3>AI智能问答演示</h3>
      <p>这是一个演示版本，展示AI问答的基本功能</p>
    </div>
    
    <div class="demo-messages" ref="messagesContainer">
      <div v-for="(message, index) in messages" :key="index" class="demo-message">
        <div class="message-avatar">
          <el-avatar :src="message.isUser ? userAvatar : aiAvatar" :size="32" />
        </div>
        <div class="message-content">
          <div class="message-name">{{ message.name }}</div>
          <div class="message-text">
            <MarkdownRenderer v-if="!message.isUser" :content="message.content" />
            <span v-else>{{ message.content }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="demo-input">
      <el-input
        v-model="inputText"
        placeholder="输入您的问题..."
        @keyup.enter="sendMessage"
        :disabled="isThinking"
      >
        <template #append>
          <el-button 
            type="primary" 
            @click="sendMessage"
            :loading="isThinking"
            :disabled="!inputText.trim()"
          >
            发送
          </el-button>
        </template>
      </el-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import MarkdownRenderer from '@/components/MarkdownRenderer/index.vue'

const inputText = ref('')
const isThinking = ref(false)
const messagesContainer = ref()

const userAvatar = '/src/assets/images/user-avatar.png'
const aiAvatar = '/src/assets/images/ai-avatar.png'

const messages = ref([
  {
    isUser: false,
    name: 'AI助手',
    content: '您好！我是AI智能问答助手。我可以帮助您：\n\n- 回答各种问题\n- 解释代码和技术概念\n- 提供建议和指导\n- 支持**Markdown**格式显示\n\n请随时向我提问！'
  }
])

// 预设的演示回答
const demoResponses = [
  '这是一个很好的问题！让我来为您详细解答：\n\n## 主要要点\n\n1. **第一点**：这里是详细说明\n2. **第二点**：包含代码示例\n\n```javascript\nconst example = "Hello World";\nconsole.log(example);\n```\n\n希望这个回答对您有帮助！',
  
  '根据您的问题，我建议采用以下方法：\n\n> 💡 **提示**：这是一个最佳实践\n\n### 解决方案\n\n- 使用现代化的技术栈\n- 遵循代码规范\n- 注重用户体验\n\n如果您需要更多信息，请告诉我！',
  
  '让我为您分析一下这个问题：\n\n| 方面 | 优点 | 缺点 |\n|------|------|------|\n| 性能 | 快速响应 | 内存占用 |\n| 维护 | 易于更新 | 复杂度高 |\n| 扩展 | 灵活配置 | 学习成本 |\n\n**总结**：综合考虑各个因素，建议采用渐进式的方案。',
  
  '这是一个技术相关的问题，让我提供一些代码示例：\n\n```typescript\ninterface ApiResponse {\n  success: boolean;\n  data: any;\n  message?: string;\n}\n\nconst handleApiCall = async (): Promise<ApiResponse> => {\n  try {\n    const response = await fetch("/api/data");\n    return await response.json();\n  } catch (error) {\n    return { success: false, data: null, message: "请求失败" };\n  }\n};\n```\n\n这样的实现方式具有良好的类型安全性和错误处理机制。'
]

const sendMessage = async () => {
  if (!inputText.value.trim() || isThinking.value) return
  
  // 添加用户消息
  messages.value.push({
    isUser: true,
    name: '用户',
    content: inputText.value
  })
  
  const userQuestion = inputText.value
  inputText.value = ''
  
  // 滚动到底部
  await nextTick()
  scrollToBottom()
  
  // 模拟AI思考
  isThinking.value = true
  
  // 添加思考中的消息
  messages.value.push({
    isUser: false,
    name: 'AI助手',
    content: '正在思考中...'
  })
  
  await nextTick()
  scrollToBottom()
  
  // 模拟延迟
  setTimeout(() => {
    // 随机选择一个演示回答
    const randomResponse = demoResponses[Math.floor(Math.random() * demoResponses.length)]
    
    // 更新最后一条消息
    messages.value[messages.value.length - 1].content = randomResponse
    
    isThinking.value = false
    
    nextTick(() => {
      scrollToBottom()
    })
  }, 1500)
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}
</script>

<style scoped lang="scss">
.demo-chat-container {
  height: 600px;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
}

.demo-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px 8px 0 0;
  
  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
  }
}

.demo-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
}

.demo-message {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
  gap: 12px;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  max-width: calc(100% - 60px);
}

.message-name {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 500;
}

.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  line-height: 1.6;
  
  .demo-message:first-child & {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }
}

.demo-input {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  background: white;
  border-radius: 0 0 8px 8px;
}
</style>