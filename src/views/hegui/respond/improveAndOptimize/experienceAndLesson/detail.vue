<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  ArrowRight,
  Document,
  Download,
  Edit,
  Link,
  More,
  Printer,
  Share,
  Star,
  View,
} from '@element-plus/icons-vue'
import experienceApi from '@/api/monitor/exprience'
import dictApi from '@/api/modules/system/dict'
import UploadMbb from '@/components/uploadMbb/index.vue'

const route = useRoute()
const router = useRouter()

const activeTab = ref('relatedAnalysis')
const loading = ref(false)

// 详情数据
const detailData = ref({
  id: null,
  tenantId: null,
  title: '', // 标题
  experienceCode: '', // 编号
  experienceType: '', // 类型
  experienceSource: '', // 来源
  level: '', // 优先级
  sourceCode: '', // 来源编号
  dutyEmployeeOrgId: null, // 责任部门id
  label: '', // 标签
  status: '', // 状态
  problemBackground: '', // 问题背景
  eventCourse: '', // 事件经过
  problemAnalysis: '', // 问题分析
  reasonCategory: '', // 原因分类
  experienceLearned: '', // 经验教训
  improvementSuggestion: '', // 改进建议
  metadata: '', // 补充字段
  attachmentList: [], // 附件列表
  createTime: '',
  createBy: '',
  updateTime: '',
  updateBy: '',
})

// 选项数据
const typeOptions = ref<any[]>([])
const sourceOptions = ref<any[]>([])
const levelOptions = ref([
  { label: '低', value: 'LOW' },
  { label: '中', value: 'MIDDLE' },
  { label: '高', value: 'HIGH' },
])

// 计算属性
const typeLabel = computed(() => {
  const option = typeOptions.value.find((item: any) => item.value === detailData.value.experienceType)
  return option?.name || detailData.value.experienceType
})

const sourceLabel = computed(() => {
  const option = sourceOptions.value.find((item: any) => item.value === detailData.value.experienceSource)
  return option?.name || detailData.value.experienceSource
})

const levelLabel = computed(() => {
  const option = levelOptions.value.find(item => item.value === detailData.value.level)
  return option?.label || detailData.value.level
})

const statusLabel = computed(() => {
  const statusMap: Record<string, string> = {
    DRAFT: '草稿',
    PUBLISHED: '已发布',
    SAVE: '已保存',
  }
  return statusMap[detailData.value.status] || detailData.value.status
})

const statusType = computed(() => {
  const typeMap: Record<string, 'info' | 'success' | 'warning' | 'primary' | 'danger'> = {
    DRAFT: 'info',
    PUBLISHED: 'success',
    SAVE: 'warning',
  }
  return typeMap[detailData.value.status] || 'info'
})

const levelType = computed(() => {
  const typeMap: Record<string, 'info' | 'success' | 'warning' | 'primary' | 'danger'> = {
    LOW: 'success',
    MIDDLE: 'warning',
    HIGH: 'danger',
  }
  return typeMap[detailData.value.level] || 'info'
})

// 标签数组
const tags = computed(() => {
  return detailData.value.label ? detailData.value.label.split(',') : []
})

// 原因分类数组
const reasonCategories = computed(() => {
  return detailData.value.reasonCategory ? detailData.value.reasonCategory.split(',') : []
})

// 获取字典数据
async function fetchDictOptions() {
  try {
    const typeResponse = await dictApi.dictAll(27)
    const sourceResponse = await dictApi.dictAll(28)
    if (typeResponse) {
      sourceOptions.value = typeResponse
    }
    if (sourceResponse) {
      typeOptions.value = sourceResponse
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 获取详情数据
async function fetchDetailData() {
  try {
    loading.value = true
    const response = await experienceApi.getExperienceDetail(route.query.id as string)
    if (response) {
      Object.assign(detailData.value, response)
    }
  }
  catch (error) {
    ElMessage.error('获取详情失败')
  }
  finally {
    loading.value = false
  }
}

// 编辑
function handleEdit() {
  router.push({
    path: '/respond/improveAndOptimize/experienceAndLesson/addEdit',
    query: {
      id: detailData.value.id,
      mode: 'edit',
    },
  })
}

// 返回列表
function handleBack() {
  router.back()
}

// 格式化文件大小
function formatFileSize(size: number) {
  if (!size) {
    return '0 B'
  }
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(1)} ${units[index]}`
}

// 下载文件
function downloadFile(file: any) {
  if (file.filePath) {
    window.open(file.filePath, '_blank')
  }
  else {
    ElMessage.warning('文件路径不存在')
  }
}

// 初始化
async function init() {
  await fetchDictOptions()
  await fetchDetailData()
}

// 页面初始化
onMounted(() => {
  init()
})

const relatedMaterials = ref([
  {
    name: '2023Q1项目复盘报告',
    type: 'PDF文档',
    uploadTime: '2023-04-20',
    uploader: '张明远',
    description: '包含3个延期项目的详细分析',
  },
  {
    name: '客户A项目延期分析',
    type: 'PPT演示',
    uploadTime: '2023-04-18',
    uploader: '李思雨',
    description: '客户A项目时间线及关键节点分析',
  },
  {
    name: '供应商评估报告',
    type: 'Excel表格',
    uploadTime: '2023-04-15',
    uploader: '王建国',
    description: '核心供应商交付能力评估数据',
  },
])

const relatedRisks = ref([
  {
    name: '供应商交付风险',
    type: '供应链风险',
    level: '高',
    description: '直接导致项目延期的关键因素',
  },
  {
    name: '需求变更风险',
    type: '项目风险',
    level: '中',
    description: '间接影响项目进度的重要因素',
  },
])

const relatedPolicies = ref([
  {
    name: '项目管理办法',
    type: '管理制度',
    version: 'V2.3',
    description: '风险管理章节需要修订',
  },
  {
    name: '供应商管理办法',
    type: '管理制度',
    version: 'V1.5',
    description: '供应商评估标准需要完善',
  },
])

const relatedPositions = ref([
  {
    name: '项目经理',
    department: '项目管理办公室',
    description: '风险管理第一责任人',
  },
  {
    name: '采购专员',
    department: '采购部',
    description: '供应商风险管理责任人',
  },
])

// 附件列表类型定义
interface AttachmentFile {
  fileName?: string
  name?: string
  fileSize?: number
  size?: number
  filePath?: string
}

const relatedProcesses = ref([
  {
    name: '项目风险管理流程',
    type: '管理流程',
    version: 'V1.2',
    description: '需要优化风险预警机制',
  },
  {
    name: '供应商评估流程',
    type: '业务流程',
    version: 'V2.1',
    description: '需要增加交付能力评估指标',
  },
])
</script>

<template>
  <div class="bg-gray-50">
    <div class="mx-auto flex">
      <!-- 左侧主内容区 -->
      <div class="w-full p-6">
        <!-- 顶部信息区 -->
        <div class="py-4">
          <div class="mb-4 flex items-center text-sm text-gray-500">
            <span>应对之翼</span>
            <el-icon class="mx-2 text-xs">
              <ArrowRight />
            </el-icon>
            <span>持续改进优化</span>
            <el-icon class="mx-2 text-xs">
              <ArrowRight />
            </el-icon>
            <span>经验教训库</span>
            <el-icon class="mx-2 text-xs">
              <ArrowRight />
            </el-icon>
            <span class="text-gray-700">经验教训详情</span>
          </div>
          <div class="mb-6 flex items-center justify-between">
            <h1 class="text-2xl font-bold">
              {{ detailData.title || '经验教训详情' }}
            </h1>
            <div class="flex items-center space-x-2">
              <el-tag :type="statusType" size="small">
                {{ statusLabel }}
              </el-tag>
              <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleEdit">
                <el-icon class="mr-2">
                  <Edit />
                </el-icon>编辑
              </el-button>
              <el-button plain class="!rounded-button whitespace-nowrap" @click="handleBack">
                <el-icon class="mr-2">
                  <ArrowLeft />
                </el-icon>返回
              </el-button>
            </div>
          </div>
        </div>

        <!-- 基本信息区 -->
        <el-card shadow="hover" class="mb-6">
          <template #header>
            <h2 class="text-lg font-semibold">
              基本信息
            </h2>
          </template>
          <div class="grid grid-cols-2 gap-4">
            <div class="flex">
              <span class="w-24 text-gray-500">编号：</span>
              <span>{{ detailData.experienceCode }}</span>
            </div>
            <div class="flex">
              <span class="w-24 text-gray-500">标题：</span>
              <span>{{ detailData.title }}</span>
            </div>
            <div class="flex">
              <span class="w-24 text-gray-500">类型：</span>
              <span>{{ typeLabel }}</span>
            </div>
            <div class="flex">
              <span class="w-24 text-gray-500">来源：</span>
              <span v-if="!detailData.sourceCode">{{ sourceLabel }}</span>
              <el-link v-else type="primary" :underline="false">
                {{ detailData.sourceName }}
              </el-link>
            </div>
            <div class="flex">
              <span class="w-24 text-gray-500">优先级：</span>
              <el-tag :type="levelType" size="small">
                {{ levelLabel }}
              </el-tag>
            </div>
            <div class="flex">
              <span class="w-24 text-gray-500">责任部门：</span>
              <span>{{ detailData.dutyEmployeeOrgName || '-' }}</span>
            </div>
            <div class="flex">
              <span class="w-24 text-gray-500">创建人：</span>
              <span>{{ detailData.createdBy || '-' }}</span>
            </div>
            <div class="flex">
              <span class="w-24 text-gray-500">创建日期：</span>
              <span>{{ detailData.createdAt || '-' }}</span>
            </div>
            <div class="flex">
              <span class="w-24 text-gray-500">状态：</span>
              <span>{{ statusLabel }}</span>
            </div>
            <div v-if="tags.length > 0" class="flex">
              <span class="w-24 text-gray-500">标签：</span>
              <div class="flex flex-wrap gap-1">
                <el-tag v-for="tag in tags" :key="tag" size="small" type="info">
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 内容展示区 -->
        <el-card shadow="hover" class="mb-6">
          <template #header>
            <h2 class="text-lg font-semibold">
              内容详情
            </h2>
          </template>

          <div v-if="detailData.problemBackground" class="mb-6">
            <h3 class="mb-2 font-semibold">
              问题背景
            </h3>
            <div class="whitespace-pre-wrap text-gray-700">
              {{ detailData.problemBackground }}
            </div>
          </div>

          <div v-if="detailData.eventCourse" class="mb-6">
            <h3 class="mb-2 font-semibold">
              事件经过
            </h3>
            <div class="whitespace-pre-wrap text-gray-700">
              {{ detailData.eventCourse }}
            </div>
          </div>

          <div v-if="detailData.problemAnalysis" class="mb-6">
            <h3 class="mb-2 font-semibold">
              问题分析
            </h3>
            <div class="whitespace-pre-wrap text-gray-700">
              {{ detailData.problemAnalysis }}
            </div>
            <div v-if="reasonCategories.length > 0" class="mt-3">
              <h4 class="mb-2 text-sm font-medium">
                原因分类
              </h4>
              <div class="flex flex-wrap gap-2">
                <el-tag v-for="category in reasonCategories" :key="category" size="small" type="warning">
                  {{ category }}
                </el-tag>
              </div>
            </div>
          </div>

          <div v-if="detailData.experienceLearned" class="mb-6">
            <h3 class="mb-2 font-semibold">
              经验教训
            </h3>
            <div class="whitespace-pre-wrap text-gray-700">
              {{ detailData.experienceLearned }}
            </div>
          </div>

          <div v-if="detailData.improvementSuggestion" class="mb-6">
            <h3 class="mb-2 font-semibold">
              改进建议
            </h3>
            <div class="whitespace-pre-wrap text-gray-700">
              {{ detailData.improvementSuggestion }}
            </div>
          </div>

          <!-- 相关资料 -->
          <div v-if="detailData.attachmentList && detailData.attachmentList.length > 0" class="mb-6">
            <h3 class="mb-2 font-semibold">
              相关资料
            </h3>
            <UploadMbb
              :model-value="detailData.attachmentList"
              :readonly="true"
              :auto-upload="false"
              :max="10"
              tip-text="附件仅支持查看和下载"
            />
          </div>
        </el-card>

        <!-- 相关资料区 -->
        <el-card v-if="false" shadow="hover" class="mb-6">
          <template #header>
            <h2 class="text-lg font-semibold">
              相关资料
            </h2>
          </template>
          <el-table :data="relatedMaterials" style="width: 100%">
            <el-table-column prop="name" label="资料名称" />
            <el-table-column prop="type" label="资料类型" />
            <el-table-column prop="uploadTime" label="上传时间" />
            <el-table-column prop="uploader" label="上传人" />
            <el-table-column prop="description" label="描述" />
            <el-table-column label="操作">
              <template #default>
                <el-link type="primary" :underline="false" class="mr-3">
                  查看
                </el-link>
                <el-link type="primary" :underline="false">
                  下载
                </el-link>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 内容标签页区 -->
        <el-card v-if="false" shadow="hover" class="mb-6">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="关联分析" name="relatedAnalysis">
              <div class="mb-8">
                <h3 class="mb-4 text-lg font-semibold">
                  关联风险
                </h3>
                <el-table :data="relatedRisks" style="width: 100%">
                  <el-table-column prop="name" label="风险名称" />
                  <el-table-column prop="type" label="风险类型" />
                  <el-table-column prop="level" label="风险等级">
                    <template #default="{ row }">
                      <el-tag :type="row.level === '高' ? 'danger' : 'warning'" size="small">
                        {{ row.level }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="关联性说明" />
                  <el-table-column label="操作">
                    <template #default>
                      <el-link type="primary" :underline="false">
                        查看详情
                      </el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div class="mb-8">
                <h3 class="mb-4 text-lg font-semibold">
                  关联制度
                </h3>
                <el-table :data="relatedPolicies" style="width: 100%">
                  <el-table-column prop="name" label="制度名称" />
                  <el-table-column prop="type" label="制度类型" />
                  <el-table-column prop="version" label="版本号" />
                  <el-table-column prop="description" label="关联性说明" />
                  <el-table-column label="操作">
                    <template #default>
                      <el-link type="primary" :underline="false">
                        查看详情
                      </el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div class="mb-8">
                <h3 class="mb-4 text-lg font-semibold">
                  关联岗位
                </h3>
                <el-table :data="relatedPositions" style="width: 100%">
                  <el-table-column prop="name" label="岗位名称" />
                  <el-table-column prop="department" label="所属部门" />
                  <el-table-column prop="description" label="关联性说明" />
                  <el-table-column label="操作">
                    <template #default>
                      <el-link type="primary" :underline="false">
                        查看详情
                      </el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div>
                <h3 class="mb-4 text-lg font-semibold">
                  关联流程
                </h3>
                <el-table :data="relatedProcesses" style="width: 100%">
                  <el-table-column prop="name" label="流程名称" />
                  <el-table-column prop="type" label="流程类型" />
                  <el-table-column prop="version" label="版本号" />
                  <el-table-column prop="description" label="关联性说明" />
                  <el-table-column label="操作">
                    <template #default>
                      <el-link type="primary" :underline="false">
                        查看详情
                      </el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
            <el-tab-pane label="改进进展" name="improvementProgress" />
            <el-tab-pane label="讨论与反馈" name="discussion" />
            <el-tab-pane label="生命周期" name="lifecycle" />
          </el-tabs>
        </el-card>
      </div>

      <!-- 右侧相关信息区 -->
      <div v-if="false" class="w-1-4">
        <div class="sticky top-4 space-y-4">
          <!-- 关联事件模块 -->
          <el-card shadow="hover">
            <template #header>
              <h2 class="text-lg font-semibold">
                关联事件
              </h2>
            </template>
            <div class="space-y-3">
              <div>
                <el-link type="primary" :underline="false">
                  客户A项目延期处理
                </el-link>
                <div class="mt-1 flex items-center">
                  <el-tag type="info" size="small">
                    项目事件
                  </el-tag>
                  <span class="ml-2 text-xs text-gray-500">2023-03-15</span>
                </div>
              </div>
              <div>
                <el-link type="primary" :underline="false">
                  核心供应商违约调查
                </el-link>
                <div class="mt-1 flex items-center">
                  <el-tag type="info" size="small">
                    供应商事件
                  </el-tag>
                  <span class="ml-2 text-xs text-gray-500">2023-03-22</span>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 相似案例模块 -->
          <el-card shadow="hover">
            <template #header>
              <h2 class="text-lg font-semibold">
                相似案例
              </h2>
            </template>
            <div class="space-y-3">
              <div>
                <el-link type="primary" :underline="false">
                  B项目资源调配不足导致延期
                </el-link>
                <div class="mt-1 flex items-center">
                  <el-progress :percentage="85" :show-text="false" class="mr-2" />
                  <span class="text-xs text-gray-500">85%</span>
                </div>
              </div>
              <div>
                <el-link type="primary" :underline="false">
                  供应商风险管理不足案例
                </el-link>
                <div class="mt-1 flex items-center">
                  <el-progress :percentage="78" :show-text="false" class="mr-2" />
                  <span class="text-xs text-gray-500">78%</span>
                </div>
              </div>
              <div>
                <el-link type="primary" :underline="false">
                  跨部门协作不畅导致项目延期
                </el-link>
                <div class="mt-1 flex items-center">
                  <el-progress :percentage="72" :show-text="false" class="mr-2" />
                  <span class="text-xs text-gray-500">72%</span>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 推荐阅读模块 -->
          <el-card shadow="hover">
            <template #header>
              <h2 class="text-lg font-semibold">
                推荐阅读
              </h2>
            </template>
            <div class="space-y-3">
              <div>
                <el-link type="primary" :underline="false">
                  项目风险管理最佳实践
                </el-link>
                <div class="mt-1 flex items-center">
                  <el-tag type="success" size="small">
                    指南
                  </el-tag>
                  <div class="ml-2 flex items-center text-xs text-gray-500">
                    <el-icon class="mr-1">
                      <View />
                    </el-icon>
                    <span>1,024</span>
                  </div>
                </div>
              </div>
              <div>
                <el-link type="primary" :underline="false">
                  供应商管理中的风险控制
                </el-link>
                <div class="mt-1 flex items-center">
                  <el-tag type="warning" size="small">
                    案例
                  </el-tag>
                  <div class="ml-2 flex items-center text-xs text-gray-500">
                    <el-icon class="mr-1">
                      <View />
                    </el-icon>
                    <span>876</span>
                  </div>
                </div>
              </div>
              <div>
                <el-link type="primary" :underline="false">
                  敏捷项目管理中的风险应对
                </el-link>
                <div class="mt-1 flex items-center">
                  <el-tag type="info" size="small">
                    白皮书
                  </el-tag>
                  <div class="ml-2 flex items-center text-xs text-gray-500">
                    <el-icon class="mr-1">
                      <View />
                    </el-icon>
                    <span>1,532</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 快捷操作模块 -->
          <el-card shadow="hover">
            <template #header>
              <h2 class="text-lg font-semibold">
                快捷操作
              </h2>
            </template>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.min-h-[1024px] {
  min-height: 1024px;
}
.max-w-[1440px] {
  max-width: 1440px;
}
.w-3-4 {
  width: 75%;
}
.w-1-4 {
  width: 25%;
}
.pr-6 {
  padding-right: 1.5rem;
}
.bg-gray-50 {
  background-color: #f9fafb;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.flex {
  display: flex;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-gray-500 {
  color: #6b7280;
}
.mb-4 {
  margin-bottom: 1rem;
}
.items-center {
  align-items: center;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.text-gray-700 {
  color: #374151;
}
.justify-between {
  justify-content: space-between;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.font-bold {
  font-weight: 700;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.space-x-2 > * + * {
  margin-left: 0.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.font-semibold {
  font-weight: 600;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.space-y-3 > * + * {
  margin-top: 0.75rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.text-gray-900 {
  color: #111827;
}
.block {
  display: block;
}
.mt-3 {
  margin-top: 0.75rem;
}
.underline {
  text-decoration-line: underline;
}
.grid {
  display: grid;
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.gap-4 {
  gap: 1rem;
}
.w-24 {
  width: 6rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.list-disc {
  list-style-type: disc;
}
.pl-5 {
  padding-left: 1.25rem;
}
.space-y-2 > * + * {
  margin-top: 0.5rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.sticky {
  position: sticky;
}
.top-4 {
  top: 1rem;
}
.space-y-4 > * + * {
  margin-top: 1rem;
}
.gap-2 {
  gap: 0.5rem;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.flex-1 {
  flex: 1 1 0%;
}
.border {
  border-width: 1px;
}
.border-gray-200 {
  border-color: #e5e7eb;
}
.rounded {
  border-radius: 0.25rem;
}
.p-3 {
  padding: 0.75rem;
}
.text-blue-500 {
  color: #3b82f6;
}
.font-medium {
  font-weight: 500;
}
</style>
