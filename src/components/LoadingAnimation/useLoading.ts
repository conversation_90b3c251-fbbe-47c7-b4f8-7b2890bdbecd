import { ref, reactive } from 'vue'

export interface LoadingOptions {
  text?: string
  size?: 'small' | 'medium' | 'large'
  fullscreen?: boolean
  showProgress?: boolean
  duration?: number // 自动关闭时间（毫秒）
}

export interface LoadingState {
  visible: boolean
  text: string
  size: 'small' | 'medium' | 'large'
  fullscreen: boolean
  showProgress: boolean
  progress: number
}

/**
 * 加载动画组合式函数
 * 提供便捷的加载状态管理
 */
export function useLoading(defaultOptions: LoadingOptions = {}) {
  // 加载状态
  const state = reactive<LoadingState>({
    visible: false,
    text: defaultOptions.text || '加载中...',
    size: defaultOptions.size || 'medium',
    fullscreen: defaultOptions.fullscreen !== false,
    showProgress: defaultOptions.showProgress || false,
    progress: 0,
  })

  // 定时器引用
  let timer: NodeJS.Timeout | null = null
  let progressTimer: NodeJS.Timeout | null = null

  /**
   * 显示加载动画
   */
  function show(options: LoadingOptions = {}) {
    // 清除之前的定时器
    if (timer) {
      clearTimeout(timer)
      timer = null
    }

    // 更新状态
    Object.assign(state, {
      visible: true,
      text: options.text || defaultOptions.text || '加载中...',
      size: options.size || defaultOptions.size || 'medium',
      fullscreen: options.fullscreen !== false,
      showProgress: options.showProgress || false,
      progress: 0,
    })

    // 自动关闭
    if (options.duration && options.duration > 0) {
      timer = setTimeout(() => {
        hide()
      }, options.duration)
    }
  }

  /**
   * 隐藏加载动画
   */
  function hide() {
    state.visible = false
    state.progress = 0

    // 清除定时器
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
    if (progressTimer) {
      clearInterval(progressTimer)
      progressTimer = null
    }
  }

  /**
   * 更新进度
   */
  function updateProgress(progress: number) {
    if (state.showProgress) {
      state.progress = Math.max(0, Math.min(100, progress))
      
      // 进度达到100%时自动关闭
      if (state.progress >= 100) {
        setTimeout(() => {
          hide()
        }, 500)
      }
    }
  }

  /**
   * 模拟进度更新
   */
  function simulateProgress(duration: number = 3000, steps: number = 10) {
    if (!state.showProgress) return

    state.progress = 0
    const stepDuration = duration / steps
    const stepProgress = 100 / steps

    progressTimer = setInterval(() => {
      state.progress += stepProgress
      
      if (state.progress >= 100) {
        if (progressTimer) {
          clearInterval(progressTimer)
          progressTimer = null
        }
        setTimeout(() => {
          hide()
        }, 500)
      }
    }, stepDuration)
  }

  /**
   * 更新加载文字
   */
  function updateText(text: string) {
    state.text = text
  }

  /**
   * 切换显示状态
   */
  function toggle(options?: LoadingOptions) {
    if (state.visible) {
      hide()
    } else {
      show(options)
    }
  }

  return {
    state,
    show,
    hide,
    updateProgress,
    simulateProgress,
    updateText,
    toggle,
  }
}

/**
 * 全局加载实例
 * 用于在应用中统一管理加载状态
 */
export const globalLoading = useLoading({
  text: '加载中...',
  size: 'medium',
  fullscreen: true,
})

/**
 * 异步操作包装器
 * 自动显示和隐藏加载动画
 */
export async function withLoading<T>(
  asyncFn: () => Promise<T>,
  options: LoadingOptions = {}
): Promise<T> {
  const loading = useLoading(options)
  
  try {
    loading.show(options)
    const result = await asyncFn()
    return result
  } finally {
    loading.hide()
  }
}

/**
 * 带进度的异步操作包装器
 */
export async function withProgressLoading<T>(
  asyncFn: (updateProgress: (progress: number) => void) => Promise<T>,
  options: LoadingOptions = {}
): Promise<T> {
  const loading = useLoading({
    ...options,
    showProgress: true,
  })
  
  try {
    loading.show({
      ...options,
      showProgress: true,
    })
    
    const result = await asyncFn((progress) => {
      loading.updateProgress(progress)
    })
    
    return result
  } finally {
    // 确保进度达到100%后再关闭
    if (loading.state.progress < 100) {
      loading.updateProgress(100)
    }
  }
}

/**
 * 创建局部加载实例
 */
export function createLocalLoading(options: LoadingOptions = {}) {
  return useLoading({
    ...options,
    fullscreen: false,
  })
}

/**
 * 预设配置
 */
export const LoadingPresets = {
  // 页面加载
  page: {
    text: '页面加载中...',
    size: 'large' as const,
    fullscreen: true,
  },
  
  // 数据加载
  data: {
    text: '数据加载中...',
    size: 'medium' as const,
    fullscreen: true,
  },
  
  // 文件上传
  upload: {
    text: '文件上传中...',
    size: 'medium' as const,
    fullscreen: true,
    showProgress: true,
  },
  
  // 表单提交
  submit: {
    text: '提交中...',
    size: 'medium' as const,
    fullscreen: true,
  },
  
  // 局部加载
  local: {
    text: '加载中...',
    size: 'small' as const,
    fullscreen: false,
  },
  
  // 搜索
  search: {
    text: '搜索中...',
    size: 'small' as const,
    fullscreen: false,
  },
} as const

export default useLoading
