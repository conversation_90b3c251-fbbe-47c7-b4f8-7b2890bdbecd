# 导入组件使用说明

现在导入组件已经拆分为两个独立的版本，分别处理不同的后端返回格式。

## 组件说明

### ImportV1.vue
适用于返回格式：
```typescript
{
  successCount?: number    // 新增成功数
  updatedCount?: number    // 更新成功数
  failedCount?: number     // 失败数
  failedRecords?: Array<{
    data: any
    error: string
  }>
}
```

### ImportV2.vue
适用于返回格式：
```typescript
{
  success: boolean
  totalRows?: number       // 总行数
  successRows?: number     // 成功行数
  failedRows?: number      // 失败行数
  skippedRows?: number     // 跳过行数
  message?: string         // 消息
  successIds?: any[]       // 成功的ID列表
  errorDetails?: Array<{
    rowNumber: number
    fieldName: string
    fieldValue: string
    errorMessage: string
    errorType: string
  }>
}
```

## 使用方法

### 方法1：使用默认组件（ImportV1）
```vue
<script setup>
import ImportComponent from '@/components/import/index.vue'

// 使用 ImportV1 格式的 API
function handleImportData(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return userApi.importEmployees(formData)
}
</script>

<template>
  <ImportComponent
    v-model:visible="importDialogVisible"
    title="导入用户"
    :download-template-api="userApi.downloadEmployeeTemplate"
    :import-data-api="handleImportData"
    template-file-name="企业员工信息模板.xlsx"
    accept-file-types=".xlsx,.xls"
    :max-file-size="10"
    :show-download-template="true"
    @success="handleImportSuccess"
    @error="handleImportError"
  />
</template>
```

### 方法2：直接使用 ImportV1
```vue
<script setup>
import ImportV1 from '@/components/import/ImportV1.vue'
</script>

<template>
  <ImportV1
    v-model:visible="importDialogVisible"
    title="导入数据"
    :import-data-api="yourApiFunction"
    @success="handleSuccess"
    @error="handleError"
  />
</template>
```

### 方法3：直接使用 ImportV2
```vue
<script setup>
import ImportV2 from '@/components/import/ImportV2.vue'
</script>

<template>
  <ImportV2
    v-model:visible="importDialogVisible"
    title="导入数据"
    :import-data-api="yourApiFunction"
    @success="handleSuccess"
    @error="handleError"
  />
</template>
```

## Props 说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | boolean | false | 弹窗显示状态 |
| title | string | '数据导入' | 弹窗标题 |
| downloadTemplateApi | Function | - | 下载模板的API函数 |
| importDataApi | Function | - | 导入数据的API函数 |
| templateFileName | string | '导入模板.xlsx' | 模板文件名 |
| acceptFileTypes | string | '.xlsx,.xls' | 支持的文件类型 |
| maxFileSize | number | 10 | 最大文件大小(MB) |
| showDownloadTemplate | boolean | true | 是否显示下载模板按钮 |

## Events 说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | boolean | 弹窗显示状态变化 |
| success | result | 导入成功回调 |
| error | error | 导入失败回调 |

## 显示效果对比

### ImportV1 显示效果
- 成功：`导入完成！新增 2 条，更新 3 条`
- 部分失败：`导入完成！新增 2 条，更新 3 条，失败 1 条`
- 完全失败：显示错误详情对话框

### ImportV2 显示效果
- 成功：`导入成功！共处理 5 条数据，成功 5 条`
- 失败：显示错误详情对话框，包含行号和字段信息

## 迁移指南

如果你当前使用的是旧的导入组件，建议：

1. **确认后端返回格式**：查看你的API返回的数据结构
2. **选择对应组件**：根据返回格式选择 ImportV1 或 ImportV2
3. **更新导入语句**：修改组件导入路径
4. **测试功能**：确保导入和错误显示正常工作

## 注意事项

1. **API适配**：如果你的API期望 `FormData` 但组件传递 `File`，需要创建适配函数
2. **错误处理**：两个组件的错误显示格式不同，请根据需要选择
3. **向后兼容**：默认的 `index.vue` 使用 ImportV1，保持向后兼容性
