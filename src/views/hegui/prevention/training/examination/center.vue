<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  ArrowRight,
  Clock,
  Document,
  Grid,
  Upload,
} from '@element-plus/icons-vue'
import assessApi from '@/api/complianceApi/prevention/assess'
import PageHeader from '@/components/PageHeader/index.vue'
import PageMain from '@/components/PageMain/index.vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 考试数据
interface Question {
  id: string
  type: 'single' | 'multi' | 'judge'
  text: string
  image?: string
  options: Array<{ text: string, image?: string }>
  answer: number[]
  marked: boolean
}

const questions = ref<Question[]>([])
const currentIndex = ref(0)
const showQuestionCard = ref(false)
const countdown = ref(3600) // 默认60分钟
const timer = ref<NodeJS.Timeout | null>(null)
const loading = ref(false)
const examRecordId = ref<string>('')
const isExamEnded = ref(false)
const isSubmit = ref(false)
const isPageLeaving = ref(false)
const hasShownExamWarning = ref(false)
const autoSubmitInProgress = ref(false)

// 计算属性
const currentQuestion = computed(() => questions.value[currentIndex.value])
const answeredCount = computed(() => questions.value.filter(q => q.answer.length > 0).length)
const totalQuestions = computed(() => questions.value.length)

// 格式化时间显示
function formatTime(seconds: number): string {
  const h = Math.floor(seconds / 3600)
  const m = Math.floor((seconds % 3600) / 60)
  const s = seconds % 60
  return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`
}

// 获取题型文字
function getQuestionType(type: string): string {
  switch (type) {
    case 'single': return '单选题'
    case 'multi': return '多选题'
    case 'judge': return '判断题'
    default: return ''
  }
}

// 判断选项是否被选中
function isSelected(index: number): boolean {
  return currentQuestion.value?.answer.includes(index) || false
}

// 处理选项选择
function handleSelectOption(index: number) {
  if (isExamEnded.value) {
    ElMessage.warning('考试时间已结束，无法答题')
    return
  }

  if (!currentQuestion.value) { return }

  if (currentQuestion.value.type === 'single' || currentQuestion.value.type === 'judge') {
    questions.value[currentIndex.value].answer = [index]
  }
  else if (currentQuestion.value.type === 'multi') {
    const answerIndex = currentQuestion.value.answer.indexOf(index)
    if (answerIndex === -1) {
      questions.value[currentIndex.value].answer.push(index)
    }
    else {
      questions.value[currentIndex.value].answer.splice(answerIndex, 1)
    }
  }
}

// 上一题
function handlePrevQuestion() {
  if (currentIndex.value > 0) {
    currentIndex.value--
  }
  else {
    ElMessage.info('已经是第一题了')
  }
}

// 下一题
function handleNextQuestion() {
  if (currentIndex.value < questions.value.length - 1) {
    currentIndex.value++
  }
  else {
    ElMessage.info('已经是最后一题了')
  }
}

// 跳转到指定题目
function handleJumpQuestion(index: number) {
  currentIndex.value = index
  showQuestionCard.value = false
}

// 返回处理
function handleBack() {
  if (isSubmit.value) {
    router.back()
    return
  }

  ElMessageBox.confirm(
    '您还没有提交考试，确定要离开吗？离开将自动提交当前答案。',
    '提示',
    {
      confirmButtonText: '确定离开',
      cancelButtonText: '继续考试',
      type: 'warning',
    },
  ).then(async () => {
    if (timer.value) {
      clearInterval(timer.value)
    }
    await submitExam(true, true)
  }).catch(() => {
    // 用户取消
  })
}

// 提交考试
function handleSubmit() {
  if (isExamEnded.value) {
    ElMessage.warning('考试时间已结束')
    return
  }

  const totalQuestions = questions.value.length
  const answeredQuestions = answeredCount.value

  if (answeredQuestions < totalQuestions) {
    ElMessageBox.confirm(
      `您还有 ${totalQuestions - answeredQuestions} 道题未作答，是否确认提交？`,
      '提示',
      {
        confirmButtonText: '确认提交',
        cancelButtonText: '继续答题',
        type: 'warning',
      },
    ).then(() => {
      submitExam()
    }).catch(() => {
      // 用户取消
    })
  }
  else {
    ElMessageBox.confirm(
      '您已完成所有题目，确认提交考试吗？',
      '提示',
      {
        confirmButtonText: '确认提交',
        cancelButtonText: '继续答题',
        type: 'warning',
      },
    ).then(() => {
      submitExam()
    }).catch(() => {
      // 用户取消
    })
  }
}

// 执行提交考试
async function submitExam(isDefaultSubmit = false, isAutoSubmit = false) {
  if (autoSubmitInProgress.value) {
    return // 防止重复提交
  }

  autoSubmitInProgress.value = true
  isSubmit.value = true

  if (!examRecordId.value) {
    if (!isAutoSubmit) {
      ElMessage.error('缺少考试记录ID')
    }
    autoSubmitInProgress.value = false
    return
  }

  try {
    let loading: any = null
    if (!isAutoSubmit) {
      loading = ElMessage({
        message: '提交中...',
        type: 'info',
        duration: 0,
      })
    }

    // 构造提交数据
    const submitData: Record<string, string> = {}
    questions.value.forEach((question: Question) => {
      const answerLetters = question.answer.map((index: number) => String.fromCharCode(65 + index))
      const userAnswer = question.type === 'multi' ? answerLetters.join(',') : (answerLetters[0] || '')
      submitData[question.id] = userAnswer
    })

    // 记录提交原因
    const submitReason = isAutoSubmit ? 'auto_submit' : 'manual_submit'
    console.log(`考试提交 - 原因: ${submitReason}, 时间: ${new Date().toISOString()}`)

    const result = await assessApi.submitExam(examRecordId.value, submitData)

    if (loading) {
      loading.close()
    }

    if (result) {
      if (!isAutoSubmit) {
        ElMessage.success('提交成功')
      }

      if (timer.value) {
        clearInterval(timer.value)
      }

      // 移除页面离开监听
      removePageLeaveListeners()

      if (isDefaultSubmit || isAutoSubmit) {
        router.back()
      }
      else {
        setTimeout(() => {
          router.back()
        }, 1500)
      }
    }
    else {
      if (!isAutoSubmit) {
        ElMessage.error('提交失败，请重试')
      }
      autoSubmitInProgress.value = false
    }
  }
  catch (error) {
    console.error('提交考试失败:', error)
    if (!isAutoSubmit) {
      ElMessage.error('提交失败，请重试')
    }
    autoSubmitInProgress.value = false

    // 如果是自动提交失败，尝试保存到本地存储
    if (isAutoSubmit) {
      saveExamStateToLocal()
    }
  }
}

// 倒计时
function startCountdown() {
  timer.value = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--

      // 时间警告提醒
      if (countdown.value === 300 && !isExamEnded.value) { // 5分钟警告
        ElMessage.warning('考试时间还剩5分钟，请抓紧时间答题！')
      }
      else if (countdown.value === 60 && !isExamEnded.value) { // 1分钟警告
        ElMessage.error('考试时间还剩1分钟，即将自动提交！')
      }
    }
    else {
      if (timer.value) {
        clearInterval(timer.value)
      }
      isExamEnded.value = true

      // 时间到达，立即自动提交
      console.log('考试时间到达，执行自动提交')
      ElMessage.error('考试时间已结束，正在自动提交...')

      // 立即自动提交，不显示确认对话框
      setTimeout(() => {
        submitExam(true, true)
      }, 500)
    }
  }, 1000)
}

// 预览图片
function previewImage(url: string) {
  // 这里可以使用Element Plus的图片预览组件
  window.open(url, '_blank')
}

// 保存考试状态到本地存储
function saveExamStateToLocal() {
  try {
    const examState = {
      examRecordId: examRecordId.value,
      questions: questions.value,
      currentIndex: currentIndex.value,
      countdown: countdown.value,
      timestamp: Date.now(),
      reason: 'page_leave_auto_submit_failed',
    }
    localStorage.setItem('exam_state_backup', JSON.stringify(examState))
    console.log('考试状态已保存到本地存储')
  }
  catch (error) {
    console.error('保存考试状态失败:', error)
  }
}

// 从本地存储恢复考试状态
function restoreExamStateFromLocal() {
  try {
    const savedState = localStorage.getItem('exam_state_backup')
    if (savedState) {
      const examState = JSON.parse(savedState)
      // 检查是否是同一个考试记录
      if (examState.examRecordId === examRecordId.value) {
        questions.value = examState.questions || []
        currentIndex.value = examState.currentIndex || 0
        countdown.value = examState.countdown || 3600
        console.log('已从本地存储恢复考试状态')

        // 清除本地存储
        localStorage.removeItem('exam_state_backup')
        return true
      }
    }
  }
  catch (error) {
    console.error('恢复考试状态失败:', error)
  }
  return false
}

// 页面离开前的处理
function handleBeforeUnload(event: BeforeUnloadEvent) {
  if (isSubmit.value || autoSubmitInProgress.value) {
    return // 已经提交或正在提交，允许离开
  }

  // 阻止页面离开并自动提交
  event.preventDefault()

  // 立即触发自动提交
  setTimeout(() => {
    if (!isSubmit.value && !autoSubmitInProgress.value) {
      console.log('检测到页面离开，执行自动提交')
      submitExam(true, true)
    }
  }, 100)

  // 现代浏览器只需要调用 preventDefault()，不需要设置 returnValue
  // 浏览器会自动显示确认对话框
}

// 页面可见性变化处理
function handleVisibilityChange() {
  if (document.hidden && !isSubmit.value && !autoSubmitInProgress.value) {
    console.log('页面变为不可见，可能用户切换了标签页')
    // 可以在这里添加警告或其他处理逻辑
    // 暂时不自动提交，只记录日志
  }
}

// 路由离开守卫
function handleRouteLeave() {
  if (isSubmit.value || autoSubmitInProgress.value) {
    return true // 已经提交，允许离开
  }

  // 自动提交考试
  submitExam(true, true)
  return false // 阻止立即离开，等待提交完成
}

// 添加页面离开监听器
function addPageLeaveListeners() {
  window.addEventListener('beforeunload', handleBeforeUnload)
  document.addEventListener('visibilitychange', handleVisibilityChange)
}

// 移除页面离开监听器
function removePageLeaveListeners() {
  window.removeEventListener('beforeunload', handleBeforeUnload)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
}

// 显示考试开始前的警告提示
async function showExamWarning() {
  if (hasShownExamWarning.value) {
    return true
  }

  try {
    await ElMessageBox.confirm(
      `
      <div style="text-align: left; line-height: 1.6;">
        <h3 style="color: #e74c3c; margin-bottom: 16px;">⚠️ 考试重要提醒</h3>
        <p style="margin-bottom: 12px;"><strong>考试期间请注意：</strong></p>
        <ul style="margin: 0; padding-left: 20px;">
          <li style="margin-bottom: 8px;">考试开始后不能离开此页面</li>
          <li style="margin-bottom: 8px;">不能刷新页面或关闭浏览器标签</li>
          <li style="margin-bottom: 8px;">不能使用浏览器前进/后退按钮</li>
          <li style="margin-bottom: 8px;">考试时间到达或检测到页面离开将自动提交</li>
          <li style="margin-bottom: 8px;">请确保网络连接稳定</li>
        </ul>
        <p style="margin-top: 16px; color: #f56c6c;"><strong>违反以上规则将导致考试自动提交！</strong></p>
      </div>
      `,
      '考试须知',
      {
        confirmButtonText: '我已了解，开始考试',
        cancelButtonText: '取消考试',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        showClose: false,
      },
    )
    hasShownExamWarning.value = true
    return true
  }
  catch {
    // 用户取消考试
    router.back()
    return false
  }
}

// 页面初始化
onMounted(async () => {
  try {
    loading.value = true

    const examId = route.query.examId as string
    const examDuration = route.query.examDuration as string

    if (!examId) {
      ElMessage.error('缺少考试ID参数')
      return
    }

    // 显示考试警告
    const canStartExam = await showExamWarning()
    if (!canStartExam) {
      return
    }

    if (examDuration && !Number.isNaN(examDuration)) {
      countdown.value = Number.parseInt(examDuration) * 60
    }

    // 调用开始考试接口
    const startExamResult = await assessApi.startExam(examId)
    if (startExamResult?.id) {
      examRecordId.value = startExamResult.id

      // 尝试从本地存储恢复状态
      const restored = restoreExamStateFromLocal()

      if (!restored) {
        // 获取题目
        const questionsResult = await assessApi.getExamQuestion(startExamResult.id)
        if (questionsResult && Array.isArray(questionsResult)) {
          questions.value = questionsResult.map((item: any) => {
            const options: Array<{ text: string, image?: string }> = []
            if (item.optionA) { options.push({ text: item.optionA }) }
            if (item.optionB) { options.push({ text: item.optionB }) }
            if (item.optionC) { options.push({ text: item.optionC }) }
            if (item.optionD) { options.push({ text: item.optionD }) }

            let type: 'single' | 'multi' | 'judge' = 'single'
            if (item.questionType === '多选题') {
              type = 'multi'
            }
            else if (item.questionType === '判断题') {
              type = 'judge'
            }

            let userAnswerArray: number[] = []
            if (item.userAnswer && item.isAnswered) {
              if (type === 'multi') {
                userAnswerArray = item.userAnswer.split(',').map((ans: string) => {
                  return ans.charCodeAt(0) - 65
                }).filter((index: number) => index >= 0 && index < options.length)
              }
              else {
                const answerIndex = item.userAnswer.charCodeAt(0) - 65
                if (answerIndex >= 0 && answerIndex < options.length) {
                  userAnswerArray = [answerIndex]
                }
              }
            }

            return {
              id: item.questionId,
              type,
              text: item.questionContent,
              image: item.questionImage,
              options,
              answer: userAnswerArray,
              marked: false,
            }
          })

          if (!examDuration && startExamResult.examInfo?.examDuration) {
            countdown.value = startExamResult.examInfo.examDuration * 60
          }
        }
      }

      // 启用页面离开监听
      addPageLeaveListeners()
      console.log('考试页面离开监听已启用')
    }
    else {
      ElMessage.error('开始考试失败')
    }
  }
  catch (error) {
    console.error('加载考试数据失败:', error)
    setTimeout(() => {
      isSubmit.value = true
      handleBack()
    }, 1000)
  }
  finally {
    loading.value = false
  }

  startCountdown()
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }

  // 移除页面离开监听器
  removePageLeaveListeners()
})
</script>

<template>
  <div class="absolute-container">
    <!-- 页面头部 -->
    <PageHeader>
      <template #content>
        <div class="w-full flex items-center justify-between">
          <div class="flex items-center gap-4">
            <el-button :icon="ArrowLeft" circle @click="handleBack" />
            <h1 class="text-xl text-gray-800 font-bold">
              在线考试
            </h1>
            <span class="question-progress text-lg text-blue-600 font-medium">
              第 {{ currentIndex + 1 }} / {{ totalQuestions }} 题
            </span>
          </div>
          <div class="flex items-center gap-4">
            <div class="exam-status">
              <el-tag v-if="!isSubmit && !autoSubmitInProgress" type="success" size="small">
                <el-icon><Document /></el-icon>
                考试进行中
              </el-tag>
              <el-tag v-else-if="autoSubmitInProgress" type="warning" size="small">
                <el-icon><Upload /></el-icon>
                正在提交...
              </el-tag>
              <el-tag v-else type="info" size="small">
                <el-icon><Document /></el-icon>
                已提交
              </el-tag>
            </div>
            <div class="timer flex items-center gap-2 text-lg text-red-600 font-semibold">
              <el-icon><Clock /></el-icon>
              <span>{{ formatTime(countdown) }}</span>
            </div>
          </div>
        </div>
      </template>
    </PageHeader>

    <!-- 主要内容区域 -->
    <PageMain>
      <div class="h-[calc(100vh-160px)] min-w-0 flex overflow-hidden">
        <!-- 考试内容区域 -->
        <div class="min-w-0 flex flex-1 flex-col overflow-hidden border border-gray-200 rounded-xl bg-white shadow-lg">
          <!-- 加载状态 -->
          <div v-if="loading" class="flex flex-1 items-center justify-center">
            <div class="flex flex-col items-center space-y-4">
              <el-loading-spinner />
              <span class="text-gray-600">正在加载考试题目...</span>
            </div>
          </div>

          <!-- 题目内容 - 可滚动区域 -->
          <div v-else-if="questions.length > 0" class="flex-1 overflow-y-auto p-6">
            <el-card class="question-card" shadow="never">
              <!-- 题型标签 -->
              <div class="question-type">
                <el-tag type="primary" size="large">
                  {{ getQuestionType(currentQuestion?.type || '') }}
                </el-tag>
              </div>

              <!-- 题目内容 -->
              <div class="question-content">
                <div class="question-text">
                  {{ currentQuestion?.text }}
                </div>
                <div v-if="currentQuestion?.image" class="question-image">
                  <el-image
                    :src="currentQuestion.image"
                    fit="contain"
                    style="max-width: 100%; max-height: 300px;"
                    @click="previewImage(currentQuestion.image || '')"
                  />
                </div>
              </div>

              <!-- 选项区域 -->
              <div class="options-container">
                <div
                  v-for="(option, index) in currentQuestion?.options || []"
                  :key="index"
                  class="option-item"
                  :class="{ selected: isSelected(index) }"
                  @click="handleSelectOption(index)"
                >
                  <div class="option-prefix">
                    {{ String.fromCharCode(65 + index) }}.
                  </div>
                  <div class="option-content">
                    <span>{{ option.text }}</span>
                    <div v-if="option.image" class="option-image">
                      <el-image
                        :src="option.image"
                        fit="contain"
                        style="max-width: 200px; max-height: 150px;"
                        @click.stop="previewImage(option.image || '')"
                      />
                    </div>
                  </div>
                  <div class="option-selector">
                    <el-radio
                      v-if="currentQuestion?.type === 'single' || currentQuestion?.type === 'judge'"
                      :model-value="isSelected(index)"
                      :value="true"
                      @click.stop
                    />
                    <el-checkbox
                      v-else
                      :model-value="isSelected(index)"
                      @click.stop
                    />
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 无题目状态 -->
          <div v-else class="flex flex-1 items-center justify-center">
            <el-empty description="暂无考试题目" />
          </div>

          <!-- 底部操作栏 - 固定在底部 -->
          <div class="border-t border-gray-200 bg-gray-50 p-4">
            <div class="flex items-center justify-between">
              <el-button
                :icon="ArrowLeft"
                :disabled="currentIndex === 0"
                @click="handlePrevQuestion"
              >
                上一题
              </el-button>

              <div class="flex items-center gap-3">
                <el-button
                  :icon="Grid"
                  @click="showQuestionCard = true"
                >
                  题卡 ({{ answeredCount }}/{{ totalQuestions }})
                </el-button>

                <el-button
                  type="primary"
                  :icon="Upload"
                  @click="handleSubmit"
                >
                  提交考试
                </el-button>
              </div>

              <el-button
                :icon="ArrowRight"
                :disabled="currentIndex === totalQuestions - 1"
                @click="handleNextQuestion"
              >
                下一题
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </PageMain>

    <!-- 题卡弹窗 -->
    <el-dialog
      v-model="showQuestionCard"
      title="答题进度"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="question-card-content">
        <div class="progress-info">
          <span>已答题: {{ answeredCount }} / {{ totalQuestions }}</span>
          <span>完成率: {{ Math.round((answeredCount / totalQuestions) * 100) }}%</span>
        </div>

        <div class="question-grid">
          <div
            v-for="(item, index) in questions"
            :key="index"
            class="question-number"
            :class="{
              answered: item.answer.length > 0,
              marked: item.marked,
              current: index === currentIndex,
            }"
            @click="handleJumpQuestion(index)"
          >
            {{ index + 1 }}
          </div>
        </div>

        <div class="legend">
          <div class="legend-item">
            <div class="legend-color answered" />
            <span>已答题</span>
          </div>
          <div class="legend-item">
            <div class="legend-color current" />
            <span>当前题</span>
          </div>
          <div class="legend-item">
            <div class="legend-color unanswered" />
            <span>未答题</span>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showQuestionCard = false">
          关闭
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
@use "@/styles/toolsCss";

/* 题目卡片样式 */
.question-card {
  border: none;
  box-shadow: none;
}

.question-type {
  margin-bottom: 24px;
}

.question-content {
  margin-bottom: 32px;
}

.question-text {
  font-size: 18px;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
}

.question-image {
  text-align: center;
  cursor: pointer;
}

/* 选项样式 */
.options-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  background-color: var(--el-fill-color-lighter);
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.option-item:hover {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-color-primary-light-7);
}

.option-item.selected {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.option-prefix {
  flex: 0 0 auto;
  margin-right: 16px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.option-content {
  flex: 1;
  font-size: 16px;
  line-height: 1.5;
  color: var(--el-text-color-regular);
}

.option-image {
  margin-top: 12px;
  cursor: pointer;
}

.option-selector {
  flex: 0 0 auto;
  margin-left: 16px;
}

/* 题卡弹窗样式 */
.question-card-content {
  padding: 16px 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
}

.question-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.question-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 2px solid var(--el-border-color);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-regular);
}

.question-number:hover {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
}

.question-number.answered {
  background-color: var(--el-color-success);
  border-color: var(--el-color-success);
  color: white;
}

.question-number.current {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  color: white;
}

.question-number.marked {
  background-color: var(--el-color-warning);
  border-color: var(--el-color-warning);
  color: white;
}

.legend {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.legend-color.answered {
  background-color: var(--el-color-success);
}

.legend-color.current {
  background-color: var(--el-color-primary);
}

.legend-color.unanswered {
  background-color: var(--el-bg-color);
  border: 2px solid var(--el-border-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .exam-header {
    padding: 12px 16px;
  }

  .exam-content {
    padding: 16px;
  }

  .exam-footer {
    padding: 12px 16px;
  }

  .footer-actions {
    flex-wrap: wrap;
    gap: 12px;
  }

  .question-grid {
    grid-template-columns: repeat(8, 1fr);
  }

  .legend {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
