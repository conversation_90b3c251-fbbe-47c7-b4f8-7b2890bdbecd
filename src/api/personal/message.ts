import api from '@/api/index'

// 消息接口类型定义
interface MessageListParams {
  userId: string
  categoryName?: string
  keyword?: string
  page: number
  size: number
}

interface MessageItem {
  id: string
  title: string
  content: string
  type: string
  status: string
  priority: string
  createTime: string
  readTime?: string
}

interface MessageListResponse {
  content: MessageItem[]
  totalElements: number
  totalPages: number
  size: number
  number: number
}

interface MessageStats {
  total: number
  unread: number
  urgent: number
}

// 消息通知相关接口
export default {
  // 获取消息列表
  getMessageList(params: MessageListParams): Promise<MessageListResponse> {
    return api({
      url: `/whiskerguardgeneralservice/api/notification-center/records?page=${params.page}&size=${params.size}`,
      method: 'POST',
      data: {
        userId: params.userId,
        categoryName: params.categoryName,
        keyword: params.keyword,
      },
    })
  },

  // 获取分类消息列表
  getCategoryList(params: {
    userId: string
    categoryName: string
    keyword?: string
    page: number
    size: number
  }): Promise<MessageListResponse> {
    return api({
      url: `/whiskerguardgeneralservice/api/notification-center/records/category?page=${params.page}&size=${params.size}`,
      method: 'POST',
      data: {
        userId: params.userId,
        categoryName: params.categoryName,
        keyword: params.keyword,
      },
    })
  },

  // 批量标记已读
  batchMarkAsRead(userId: string): Promise<void> {
    return api({
      url: `/whiskerguardgeneralservice/api/notification-center/batch-read?userId=${userId}`,
      method: 'PUT',
    })
  },

  // 单个消息标记已读
  markAsRead(messageId: string): Promise<void> {
    return api({
      url: `/whiskerguardgeneralservice/api/notification-center/${messageId}/read`,
      method: 'PUT',
    })
  },

  // 删除消息
  deleteMessage(messageId: string): Promise<void> {
    return api({
      url: `/whiskerguardgeneralservice/api/notification-center/${messageId}`,
      method: 'DELETE',
    })
  },

  // 获取消息统计
  getMessageStats(userId: string): Promise<MessageStats> {
    return api({
      url: `/whiskerguardgeneralservice/api/notification-center/stats?userId=${userId}`,
      method: 'GET',
    })
  },
}

// 消息类型映射
export const MESSAGE_TYPES = {
  TASK: '任务提醒',
  USER: '用户通知',
  BUSINESS: '企业通知',
  SYSTEM: '系统通知',
} as const

// 消息状态映射
export const MESSAGE_STATUS = {
  SENT: '已发送',
  DELIVERED: '已送达',
  READ: '已读',
  FAILED: '发送失败',
} as const

// 消息优先级映射
export const MESSAGE_PRIORITY = {
  urgent: '紧急',
  normal: '普通',
} as const
