import api from '@/api/index'

// 责任追究整改数据类型定义
export interface ResponsibilityInvestigateCorrectionDTO {
  id?: number
  name: string // 整改项目名称
  correctionCode: string // 整改编号
  correctionType: 'COMPLIANCE_RISK' | 'OPERATIONAL_RISK' | 'SYSTEM_RISK' // 整改类型
  level: 'LOW' | 'MIDDLE' | 'HIGH' // 优先级
  investigateId?: number | null // 违规调查id
  dutyEmployeeId?: string | number | null // 责任人id
  dutyEmployeeOrgId?: number | null // 责任部门id
  collaborationEmployeeId?: string | number | null // 协作人id
  supervisionEmployeeId?: string | number | null // 监督人id
  startDate: string // 开始日期
  finishDate: string // 完成日期
  status: 'NO_START' | 'PROGRESSING' | 'FINISHED' | 'PAUSED' | 'CANCELED' // 状态
  correctionBackground: string // 整改背景
  correctionRequire: string // 整改要求
  correctionRange: string // 整改范围
  correctionScheme: string // 整改方案
  metadata?: string // 补充字段
  version?: number
  createdBy?: string
  createdAt?: any
  updatedBy?: string
  updatedAt?: any
  isDeleted?: boolean
  dutyEmployeeName?: string
  dutyEmployeeOrgName?: string
  collaborationEmployeeName?: string
  supervisionEmployeeName?: string
  investigateName?: string
}

export default {
  // 分页查询所有责任追究整改列表
  getCorrectionsList(params: any): Promise<any> {
    return api.post('/whiskerguardviolationservice/api/responsibility/investigate/corrections/search', params)
  },

  // 创建责任追究整改
  createCorrection(data: ResponsibilityInvestigateCorrectionDTO): Promise<any> {
    return api.post('/whiskerguardviolationservice/api/responsibility/investigate/corrections', data)
  },

  // 获取责任追究整改详情
  getCorrectionDetail(id: string): Promise<any> {
    return api.get(`/whiskerguardviolationservice/api/responsibility/investigate/corrections/${id}`)
  },

  // 更新责任追究整改
  updateCorrection(id: string, data: Partial<ResponsibilityInvestigateCorrectionDTO>): Promise<any> {
    return api.patch(`/whiskerguardviolationservice/api/responsibility/investigate/corrections/${id}`, data)
  },

  // 删除责任追究整改
  deleteCorrection(id: string): Promise<any> {
    return api.delete(`/whiskerguardviolationservice/api/responsibility/investigate/corrections/${id}`)
  },
}
