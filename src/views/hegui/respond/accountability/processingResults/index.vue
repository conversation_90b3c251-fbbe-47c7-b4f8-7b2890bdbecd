<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import {
  AlarmClock as ElIconAlarm<PERSON>lock,
  Check as ElIconCheck,
  CircleCheck as ElIconCircleCheck,
  Close as ElIconClose,
  Finished as ElIconFinished,
  RefreshLeft as ElIconRefreshLeft,
  Remove as ElIconRemove,
  Search as ElIconSearch,
  // Time as ElIconTime,
} from '@element-plus/icons-vue'
import correctionsApi from '@/api/problemTask/corrections'

const activeTab = ref('pending')
const confirmDialogVisible = ref(false)
const rejectDialogVisible = ref(false)
const exemptDialogVisible = ref(false)
const currentItem = ref<any>(null)

// 分页和搜索参数
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
})

const searchParams = reactive({
  tenantId: 0,
  name: '',
  correctionCode: '',
  correctionType: '',
  level: '',
  investigateId: 0,
  dutyEmployeeId: 0,
  dutyEmployeeOrgId: 0,
  collaborationEmployeeId: 0,
  supervisionEmployeeId: 0,
  startDate: '',
  finishDate: '',
  status: '',
  createdAtStart: '',
  createdAtEnd: '',
})

// 数据列表
const correctionsData = ref<any[]>([])
const loading = ref(false)
const confirmForm = ref({
  comment: '',
  score: 3,
  confirmer: '张明远',
  confirmDate: new Date().toISOString().split('T')[0],
})
const rejectForm = ref({
  reason: '',
  requirement: '',
  deadline: '',
  rejecter: '张明远',
  rejectDate: new Date().toISOString().split('T')[0],
})
const exemptForm = ref({
  reason: '',
  basis: '',
  approver: '',
  applicant: '张明远',
  applyDate: new Date().toISOString().split('T')[0],
})
function showConfirmDialog(row: any) {
  currentItem.value = row
  confirmDialogVisible.value = true
}
function showRejectDialog(row: any) {
  currentItem.value = row
  rejectDialogVisible.value = true
}
function showExemptDialog(row: any) {
  currentItem.value = row
  exemptDialogVisible.value = true
}
function handleConfirm() {
  // Handle confirm logic
  confirmDialogVisible.value = false
}
function handleReject() {
  // Handle reject logic
  rejectDialogVisible.value = false
}
function handleExempt() {
  // Handle exempt logic
  exemptDialogVisible.value = false
}

// 获取责任追究整改列表数据
async function getCorrectionsList() {
  try {
    loading.value = true
    const params = {
      ...searchParams,
      page: pagination.page,
      size: pagination.size,
    }
    const response = await correctionsApi.getCorrectionsList(params)
    correctionsData.value = response.data.content || []
    pagination.total = response.data.totalElements || 0
  }
  catch (error) {
    console.error('获取责任追究整改列表失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 根据状态过滤数据
const pendingData = computed(() => correctionsData.value.filter(item => item.status === 'NO_START'))
const confirmedData = computed(() => correctionsData.value.filter(item => item.status === 'CONFIRMED'))
const rejectedData = computed(() => correctionsData.value.filter(item => item.status === 'REJECTED'))
const exemptedData = computed(() => correctionsData.value.filter(item => item.status === 'EXEMPTED'))

// 根据标签页切换状态过滤
function handleTabChange(tab: any) {
  switch (tab) {
    case 'pending':
      searchParams.status = 'NO_START'
      break
    case 'confirmed':
      searchParams.status = 'CONFIRMED'
      break
    case 'rejected':
      searchParams.status = 'REJECTED'
      break
    case 'exempted':
      searchParams.status = 'EXEMPTED'
      break
    default:
      searchParams.status = ''
  }
  pagination.page = 0
  getCorrectionsList()
}

// 搜索功能
function handleSearch() {
  pagination.page = 0
  getCorrectionsList()
}

// 页面加载时获取数据
onMounted(() => {
  searchParams.status = 'NO_START'
})

onMounted(getCorrectionsList)
function getSeverityTagType(severity: string) {
  switch (severity) {
    case '严重': return 'danger'
    case '中等': return 'warning'
    case '轻微': return 'success'
    default: return 'info'
  }
}
function _getDaysClass(days: string) {
  const dayNum = Number.parseInt(days)
  if (dayNum > 3) { return 'text-red-500' }
  if (dayNum > 1) { return 'text-yellow-500' }
  return 'text-green-500'
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              处理结果确认
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'processingResults/index/batchConfirm'" type="primary" class="!rounded-button whitespace-nowrap">
              批量确认
            </el-button>
            <el-button v-auth="'processingResults/index/export'" class="!rounded-button whitespace-nowrap">
              导出
            </el-button>
            <el-button v-auth="'processingResults/index/statistics'" class="!rounded-button whitespace-nowrap">
              统计分析
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="6">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm text-gray-500">
                    待确认数
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    28
                  </div>
                  <div class="mt-1 text-xs text-green-500">
                    同比 +12%
                  </div>
                </div>
                <el-icon class="text-3xl text-blue-500">
                  <el-icon-time />
                </el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm text-gray-500">
                    已确认数
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    156
                  </div>
                  <div class="mt-1 text-xs text-green-500">
                    同比 +8%
                  </div>
                </div>
                <el-icon class="text-3xl text-green-500">
                  <ElIconCheck />
                </el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm text-gray-500">
                    已驳回数
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    12
                  </div>
                  <div class="mt-1 text-xs text-red-500">
                    同比 -5%
                  </div>
                </div>
                <el-icon class="text-3xl text-red-500">
                  <ElIconClose />
                </el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm text-gray-500">
                    已豁免数
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    5
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    同比 0%
                  </div>
                </div>
                <el-icon class="text-3xl text-purple-500">
                  <ElIconCircleCheck />
                </el-icon>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="12">
            <el-card shadow="hover" class="">
              <div class="flex items-start justify-between">
                <div>
                  <div class="mb-2 text-lg font-bold">
                    待我确认
                  </div>
                  <div class="mb-2 text-3xl font-bold">
                    8
                  </div>
                  <div class="mb-4 text-sm text-gray-500">
                    <span class="text-red-500">2 项超期</span>, <span class="text-yellow-500">3 项即将超期</span>
                  </div>
                  <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap">
                    立即处理
                  </el-button>
                </div>
                <el-icon class="text-4xl text-blue-500">
                  <ElIconAlarmClock />
                </el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover" class="">
              <div class="flex items-start justify-between">
                <div>
                  <div class="mb-2 text-lg font-bold">
                    我已确认
                  </div>
                  <div class="mb-2 text-3xl font-bold">
                    42
                  </div>
                  <div class="mb-4 text-sm text-gray-500">
                    本周 12 项, 本月 42 项
                  </div>
                  <el-button v-auth="'processingResults/index/immediateProcess'" size="small" class="!rounded-button whitespace-nowrap">
                    立即处理
                  </el-button>
                </div>
                <el-icon class="text-4xl text-green-500">
                  <ElIconFinished />
                </el-icon>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-20">
          <el-col :span="12">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-start justify-between">
                <div>
                  <div class="mb-2 text-lg font-bold">
                    我已驳回
                  </div>
                  <div class="mb-2 text-3xl font-bold">
                    6
                  </div>
                  <div class="mb-4 text-sm text-gray-500">
                    本周 2 项, 本月 6 项
                  </div>
                  <el-button v-auth="'processingResults/index/viewDetail'" size="small" class="!rounded-button whitespace-nowrap">
                    查看详情
                  </el-button>
                </div>
                <el-icon class="text-4xl text-red-500">
                  <ElIconRefreshLeft />
                </el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-start justify-between">
                <div>
                  <div class="mb-2 text-lg font-bold">
                    我已豁免
                  </div>
                  <div class="mb-2 text-3xl font-bold">
                    2
                  </div>
                  <div class="mb-4 text-sm text-gray-500">
                    本周 0 项, 本月 2 项
                  </div>
                  <el-button size="small" class="!rounded-button whitespace-nowrap">
                    查看详情
                  </el-button>
                </div>
                <el-icon class="text-4xl text-purple-500">
                  <ElIconRemove />
                </el-icon>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-card shadow="hover" class="mt-20">
          <!-- 筛选区 -->
          <el-card shadow="hover" class="mb-6 !rounded-lg">
            <div class="grid grid-cols-4 gap-4">
              <el-select placeholder="处理类型" size="small">
                <el-option label="全部" value="all" />
                <el-option label="警告" value="warning" />
                <el-option label="罚款" value="fine" />
                <el-option label="降级" value="demotion" />
                <el-option label="解雇" value="dismissal" />
              </el-select>
              <el-select placeholder="责任部门" size="small">
                <el-option label="全部" value="all" />
                <el-option label="财务部" value="finance" />
                <el-option label="人力资源部" value="hr" />
                <el-option label="市场部" value="marketing" />
                <el-option label="技术部" value="tech" />
              </el-select>
              <el-select placeholder="确认状态" size="small">
                <el-option label="全部" value="all" />
                <el-option label="待确认" value="pending" />
                <el-option label="已确认" value="confirmed" />
                <el-option label="已驳回" value="rejected" />
                <el-option label="已豁免" value="exempted" />
              </el-select>
              <el-date-picker
                type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                size="small"
              />
            </div>
            <div class="mt-4 flex justify-between">
              <el-input
                v-model="searchParams.name"
                placeholder="请输入关键字搜索"
                class="w-64"
                size="small"
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <el-icon class="el-input__icon">
                    <ElIconSearch />
                  </el-icon>
                </template>
              </el-input>
              <el-button type="primary" size="small" @click="handleSearch">
                搜索
              </el-button>
              <el-button  type="text" size="small">
                高级筛选
              </el-button>
            </div>
          </el-card>
          <!-- 表格区域 -->
          <el-card shadow="hover" class="mt-20">
            <el-tabs v-model="activeTab" @tab-change="handleTabChange">
              <el-tab-pane label="待确认" name="pending">
                <el-table :data="pendingData" v-loading="loading" style="width: 100%;">
                  <el-table-column type="selection" width="50" />
                  <el-table-column prop="correctionCode" label="整改编号" width="120" />
                  <el-table-column prop="name" label="整改名称" width="180" />
                  <el-table-column prop="correctionType" label="整改类型" width="120" />
                  <el-table-column prop="level" label="严重程度" width="120">
                    <template #default="{ row }">
                      <el-tag :type="getSeverityTagType(row.level)" size="small">
                        {{ row.level }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="dutyEmployeeId" label="责任人" width="150" />
                  <el-table-column prop="finishDate" label="完成日期" width="120" />
                  <el-table-column prop="supervisionEmployeeId" label="监督人" width="120" />
                  <el-table-column prop="startDate" label="开始日期" width="120" />
                  <el-table-column prop="status" label="状态" width="120">
                    <template #default="{ row }">
                      <el-tag :type="row.status === 'NO_START' ? 'warning' : 'success'" size="small">
                        {{ row.status === 'NO_START' ? '未开始' : row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                      <el-button v-auth="'processingResults/index/view'" type="primary" size="small" @click="showExemptDialog(row)">
                        查看
                      </el-button>
                      <el-button v-auth="'processingResults/index/confirm'" type="success" size="small" @click="showConfirmDialog(row)">
                        确认
                      </el-button>
                      <el-button v-auth="'processingResults/index/reject'" type="danger" size="small" @click="showRejectDialog(row)">
                        驳回
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="mt-4 flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    共 {{ pagination.total }} 条记录
                  </div>
                  <el-pagination
                    :current-page="pagination.page + 1"
                    :page-size="pagination.size"
                    :pager-count="5"
                    layout="prev, pager, next"
                    :total="pagination.total"
                    @current-change="(page) => { pagination.page = page - 1; getCorrectionsList() }"
                  />
                </div>
              </el-tab-pane>
              <el-tab-pane label="已确认" name="confirmed">
                <el-table :data="confirmedData" v-loading="loading" style="width: 100%;">
                  <el-table-column prop="correctionCode" label="整改编号" width="120" />
                  <el-table-column prop="name" label="整改名称" width="180" />
                  <el-table-column prop="correctionType" label="整改类型" width="120" />
                  <el-table-column prop="dutyEmployeeId" label="责任人" width="150" />
                  <el-table-column prop="finishDate" label="完成日期" width="120" />
                  <el-table-column prop="updatedAt" label="确认日期" width="120">
                    <template #default="{ row }">
                      {{ new Date(row.updatedAt?.seconds * 1000).toLocaleDateString() }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="updatedBy" label="确认人" width="120" />
                  <el-table-column prop="status" label="状态" width="120">
                    <template #default="{ row }">
                      <el-tag type="success" size="small">
                        已确认
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="correctionRequire" label="确认意见" />
                  <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                      <el-button v-auth="'processingResults/index/viewDetail'" type="primary" size="small" @click="showExemptDialog(row)">
                        查看详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="mt-4 flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    共 {{ confirmedData.length }} 条记录
                  </div>
                  <el-pagination
                    :current-page="pagination.page + 1"
                    :page-size="pagination.size"
                    :pager-count="5"
                    layout="prev, pager, next"
                    :total="pagination.total"
                    @current-change="(page) => { pagination.page = page - 1; getCorrectionsList() }"
                  />
                </div>
              </el-tab-pane>
              <el-tab-pane label="已驳回" name="rejected">
                <el-table :data="rejectedData" v-loading="loading" style="width: 100%;">
                  <el-table-column prop="correctionCode" label="整改编号" width="120" />
                  <el-table-column prop="name" label="整改名称" width="180" />
                  <el-table-column prop="correctionType" label="整改类型" width="120" />
                  <el-table-column prop="level" label="严重程度" width="120">
                    <template #default="{ row }">
                      <el-tag :type="getSeverityTagType(row.level)" size="small">
                        {{ row.level }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="dutyEmployeeId" label="责任人" width="150" />
                  <el-table-column prop="finishDate" label="完成日期" width="120" />
                  <el-table-column prop="updatedAt" label="驳回日期" width="120">
                    <template #default="{ row }">
                      {{ new Date(row.updatedAt?.seconds * 1000).toLocaleDateString() }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="updatedBy" label="驳回人" width="120" />
                  <el-table-column prop="correctionRequire" label="驳回原因" />
                  <el-table-column prop="startDate" label="整改期限" width="120" />
                  <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                      <el-button v-auth="'processingResults/index/viewDetail'" type="primary" size="small" @click="showExemptDialog(row)">
                        查看详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="mt-4 flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    共 {{ rejectedData.length }} 条记录
                  </div>
                  <el-pagination
                    :current-page="pagination.page + 1"
                    :page-size="pagination.size"
                    :pager-count="5"
                    layout="prev, pager, next"
                    :total="pagination.total"
                    @current-change="(page) => { pagination.page = page - 1; getCorrectionsList() }"
                  />
                </div>
              </el-tab-pane>
              <el-tab-pane label="已豁免" name="exempted">
                <el-table :data="exemptedData" v-loading="loading" style="width: 100%;">
                  <el-table-column prop="correctionCode" label="整改编号" width="120" />
                  <el-table-column prop="name" label="整改名称" width="180" />
                  <el-table-column prop="correctionType" label="整改类型" width="120" />
                  <el-table-column prop="level" label="严重程度" width="120">
                    <template #default="{ row }">
                      <el-tag :type="getSeverityTagType(row.level)" size="small">
                        {{ row.level }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="dutyEmployeeId" label="责任人" width="150" />
                  <el-table-column prop="createdAt" label="申请日期" width="120">
                    <template #default="{ row }">
                      {{ new Date(row.createdAt?.seconds * 1000).toLocaleDateString() }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="updatedAt" label="豁免日期" width="120">
                    <template #default="{ row }">
                      {{ new Date(row.updatedAt?.seconds * 1000).toLocaleDateString() }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="updatedBy" label="豁免人" width="120" />
                  <el-table-column prop="correctionRequire" label="豁免原因" />
                  <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                      <el-button type="primary" size="small" @click="showExemptDialog(row)">
                        查看详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="mt-4 flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    共 {{ exemptedData.length }} 条记录
                  </div>
                  <el-pagination
                    :current-page="pagination.page + 1"
                    :page-size="pagination.size"
                    :pager-count="5"
                    layout="prev, pager, next"
                    :total="pagination.total"
                    @current-change="(page) => { pagination.page = page - 1; getCorrectionsList() }"
                  />
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-card>
      </div>
    </PageMain>
    <!-- 确认弹窗 -->
    <el-dialog v-model="confirmDialogVisible" title="处理结果确认" width="800px">
      <div class="grid grid-cols-2 mb-4 gap-4">
        <div>
          <div class="text-sm text-gray-500">
            处理标题
          </div>
          <div class="font-medium">
            {{ currentItem?.title }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            处理类型
          </div>
          <div class="font-medium">
            {{ currentItem?.type }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            责任人/部门
          </div>
          <div class="font-medium">
            {{ currentItem?.department }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            完成日期
          </div>
          <div class="font-medium">
            {{ currentItem?.date }}
          </div>
        </div>
      </div>
      <div class="mb-6">
        <div class="mb-2 text-lg font-medium">
          处理结果内容
        </div>
        <el-card shadow="never" class="!rounded-lg">
          <div class="p-4">
            <div class="mb-4">
              <div class="mb-1 text-sm text-gray-500">
                处理结果概述
              </div>
              <div>已按照公司规定对违规行为进行处理，责任人已接受相应处罚</div>
            </div>
            <div class="mb-4">
              <div class="mb-1 text-sm text-gray-500">
                结果说明
              </div>
              <div>详细处理过程已记录在案，相关责任人已签字确认</div>
            </div>
            <div>
              <div class="mb-1 text-sm text-gray-500">
                附件
              </div>
              <el-button type="text" size="small">
                处理记录.pdf
              </el-button>
              <el-button type="text" size="small">
                确认书.docx
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
      <div class="mb-4">
        <div class="mb-2 text-lg font-medium">
          确认表单
        </div>
        <el-form :model="confirmForm" label-width="100px">
          <el-form-item label="确认意见">
            <el-input v-model="confirmForm.comment" type="textarea" :rows="3" placeholder="请输入确认意见" />
          </el-form-item>
          <el-form-item label="确认评分">
            <el-rate v-model="confirmForm.score" />
          </el-form-item>
          <el-form-item label="确认人">
            <el-input v-model="confirmForm.confirmer" disabled />
          </el-form-item>
          <el-form-item label="确认日期">
            <el-input v-model="confirmForm.confirmDate" disabled />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-between">
          <el-button v-auth="'processingResults/index/cancel'" @click="confirmDialogVisible = false">
            取消
          </el-button>
          <div class="space-x-3">
            <el-button v-auth="'processingResults/index/reject'" type="danger" @click="showRejectDialog(currentItem)">
              驳回
            </el-button>
            <el-button v-auth="'processingResults/index/confirm'" type="primary" @click="handleConfirm">
              确认
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
    <!-- 驳回弹窗 -->
    <el-dialog v-model="rejectDialogVisible" title="处理结果驳回" width="800px">
      <div class="grid grid-cols-2 mb-4 gap-4">
        <div>
          <div class="text-sm text-gray-500">
            处理标题
          </div>
          <div class="font-medium">
            {{ currentItem?.title }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            处理类型
          </div>
          <div class="font-medium">
            {{ currentItem?.type }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            责任人/部门
          </div>
          <div class="font-medium">
            {{ currentItem?.department }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            完成日期
          </div>
          <div class="font-medium">
            {{ currentItem?.date }}
          </div>
        </div>
      </div>
      <div class="mb-4">
        <el-form :model="rejectForm" label-width="100px">
          <el-form-item label="驳回原因" required>
            <el-input v-model="rejectForm.reason" type="textarea" :rows="3" placeholder="请输入驳回原因" />
          </el-form-item>
          <el-form-item label="整改要求">
            <el-input v-model="rejectForm.requirement" type="textarea" :rows="3" placeholder="请输入整改要求" />
          </el-form-item>
          <el-form-item label="整改期限">
            <el-date-picker v-model="rejectForm.deadline" type="date" placeholder="选择日期" />
          </el-form-item>
          <el-form-item label="驳回人">
            <el-input v-model="rejectForm.rejecter" disabled />
          </el-form-item>
          <el-form-item label="驳回日期">
            <el-input v-model="rejectForm.rejectDate" disabled />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-between">
          <el-button v-auth="'processingResults/index/cancel'" @click="rejectDialogVisible = false">
            取消
          </el-button>
          <el-button v-auth="'processingResults/index/reject'" type="danger" @click="handleReject">
            驳回
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 豁免弹窗 -->
    <el-dialog v-model="exemptDialogVisible" title="处理结果豁免" width="800px">
      <div class="grid grid-cols-2 mb-4 gap-4">
        <div>
          <div class="text-sm text-gray-500">
            处理标题
          </div>
          <div class="font-medium">
            {{ currentItem?.title }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            处理类型
          </div>
          <div class="font-medium">
            {{ currentItem?.type }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            责任人/部门
          </div>
          <div class="font-medium">
            {{ currentItem?.department }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            完成日期
          </div>
          <div class="font-medium">
            {{ currentItem?.date }}
          </div>
        </div>
      </div>
      <div class="mb-6">
        <div class="mb-2 text-lg font-medium">
          处理结果内容
        </div>
        <el-card shadow="never" class="!rounded-lg">
          <div class="p-4">
            <div class="mb-4">
              <div class="mb-1 text-sm text-gray-500">
                处理结果概述
              </div>
              <div>已按照公司规定对违规行为进行处理，责任人已接受相应处罚</div>
            </div>
            <div class="mb-4">
              <div class="mb-1 text-sm text-gray-500">
                结果说明
              </div>
              <div>详细处理过程已记录在案，相关责任人已签字确认</div>
            </div>
            <div>
              <div class="mb-1 text-sm text-gray-500">
                附件
              </div>
              <el-button type="text" size="small">
                处理记录.pdf
              </el-button>
              <el-button type="text" size="small">
                确认书.docx
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
      <div class="mb-4">
        <div class="mb-2 text-lg font-medium">
          豁免申请
        </div>
        <el-form :model="exemptForm" label-width="100px">
          <el-form-item label="豁免原因" required>
            <el-input v-model="exemptForm.reason" type="textarea" :rows="3" placeholder="请输入豁免原因" />
          </el-form-item>
          <el-form-item label="豁免依据">
            <el-input v-model="exemptForm.basis" type="textarea" :rows="3" placeholder="请输入豁免依据" />
          </el-form-item>
          <el-form-item label="审批人">
            <el-select v-model="exemptForm.approver" placeholder="请选择审批人">
              <el-option label="李总" value="李总" />
              <el-option label="王总监" value="王总监" />
              <el-option label="张经理" value="张经理" />
            </el-select>
          </el-form-item>
          <el-form-item label="申请人">
            <el-input v-model="exemptForm.applicant" disabled />
          </el-form-item>
          <el-form-item label="申请日期">
            <el-input v-model="exemptForm.applyDate" disabled />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-between">
          <el-button v-auth="'processingResults/index/cancel'" @click="exemptDialogVisible = false">
            取消
          </el-button>
          <el-button v-auth="'processingResults/index/submitExempt'" type="warning" @click="handleExempt">
            提交豁免
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-menu {
    border-right: none;
  }

  .el-menu-item.is-active {
    background-color: #1e88e5 !important;
  }

  .el-sub-menu .el-menu-item {
    padding-left: 48px !important;
  }

  .el-card {
    border: none;
  }

  .el-table {
    margin-top: 16px;
  }

  .el-tabs {
    margin-top: -16px;
  }

  .el-tabs__item {
    height: 48px;
    padding: 0 16px;
    line-height: 48px;
  }

  .el-tabs__active-bar {
    height: 3px;
  }
</style>
