<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { View } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import handlingMeasuresApi from '@/api/problemTask/handlingMeasures'
import dictApi from '@/api/modules/system/dict'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import DepartPerson from '@/components/departPerson/index.vue'

const route = useRoute()
const router = useRouter()

// 处理措施接口定义
interface Measure {
  id: number | null
  name: string
  dealId: number | null
  measureType: null | string
  startDate: string
  finishDate: string
  status: null | string
}

const similarCases = ref([

])

function viewCaseDetail(_id: number) {
  // 查看案例详情逻辑
  // TODO: 实现查看案例详情功能
}
// 表单数据 - 对应API接口字段
const formData = ref({
  id: undefined as string | undefined, // 处理措施ID
  title: '', // 处理标题
  dealCode: '', // 处理编号
  dealType: '', // 处理类型：FINANCIAL_VIOLATION, INFORMATION_SECURITY, DISCIPLINARY_ACTION
  level: 'LOW', // 优先级：LOW, MIDDLE, HIGH
  investigateId: undefined, // 违规调查id
  investigateName: '', // 违规调查名称（用于显示）
  dutyEmployeeId: undefined as string | number | undefined, // 责任人id - 支持字符串和数字
  dutyEmployeeOrgId: undefined, // 责任部门id
  dealEmployeeId: undefined as string | number | undefined, // 处理人id - 支持字符串和数字
  startDate: '', // 开始日期
  finishDate: '', // 完成日期
  status: 'NO_START', // 状态：NO_START, PROGRESSING, FINISHED, PAUSED, CANCELED
  violationDesc: '', // 违规描述
  violationImpact: '', // 违规影响
  responsibility: '', // 责任认定
  metadata: '', // 补充字段
  measureList: [] as Measure[], // 关联的措施信息
})

// 处理措施数据
const _measureData = ref({
  name: '', // 措施名称
  measureType: null, // 措施类型：DISCIPLINARY_ACTION, ECONOMIC_PENALTIES, SYSTEM_RECTIFICATION, TRAINING_EDUCATION
  startDate: '', // 开始日期
  finishDate: '', // 完成日期
  status: 'NO_START', // 状态
  metadata: '', // 补充字段
})

// 添加处理措施
function addMeasure() {
  const newMeasure: Measure = {
    id: null, // 新增时为null，编辑时才有值
    name: '', // 措施名称
    dealId: null, // 处理id
    measureType: null, // 措施类型：DISCIPLINARY_ACTION, ECONOMIC_PENALTIES, SYSTEM_RECTIFICATION, TRAINING_EDUCATION
    startDate: '', // 开始日期
    finishDate: '', // 完成日期
    status: 'NO_START', // 状态：NO_START, PROGRESSING, FINISHED, PAUSED, CANCELED
  }
  formData.value.measureList.push(newMeasure)
}

// 删除处理措施
// function _removeMeasure(index: number) {
//   formData.value.measureList.splice(index, 1)
// }

// // 初始化时添加一个默认措施
// if (formData.value.measureList.length === 0) {
//   addMeasure()
// }

// 处理违规调查选择变化
function handleInvestigateChange(value: any, row: any) {
  console.log('选中的违规调查:', value, row)
  if (row) {
    // 设置调查ID和名称
    formData.value.investigateId = value
    formData.value.investigateName = row.title
  }
  else {
    // 清空时同时清空两个字段
    formData.value.investigateId = undefined
    formData.value.investigateName = ''
  }
}

// 自动生成编号
const _autoGenerateCode = ref(true)

// 处理类型选项
const dealTypeOptions = ref<Array<{ name: string, value: string }>>([])
// 措施类型
const measureTypeOptions = ref([])

// 获取处理编号
async function fetchDealCode() {
  try {
    if (_autoGenerateCode.value) {
      const response = await dictApi.getCode('RESPONSIBILITY_DEAL')
      if (response) {
        formData.value.dealCode = response
      }
    }
  }
  catch (error) {
    console.error('获取处理编号失败:', error)
    ElMessage.error('获取处理编号失败')
  }
}

// 获取处理类型选项
async function fetchDealTypeOptions() {
  try {
    const response = await dictApi.dictAll(25)
    const response2 = await dictApi.dictAll(35)
    if (response) {
      dealTypeOptions.value = response
      measureTypeOptions.value = response2
    }
  }
  catch (error) {
    console.error('获取处理类型选项失败:', error)
    ElMessage.error('获取处理类型选项失败')
  }
}

// 获取处理措施详情
async function fetchHandlingMeasureDetail(id: any) {
  try {
    const response = await handlingMeasuresApi.getHandlingMeasureDetail(id)
    if (response) {
      // 将获取到的数据赋值给表单
      Object.assign(formData.value, response)

      // 如果后端没有返回 investigateName，但有 investigateId，可以在这里添加逻辑获取调查名称
      // 这里暂时假设后端会返回 investigateName 字段
    }
  }
  catch (error) {
    console.error('获取处理措施详情失败:', error)
    ElMessage.error('获取处理措施详情失败')
  }
}

// 保存处理措施
async function saveHandlingMeasure() {
  try {
    // 数据验证
    if (!formData.value.title) {
      ElMessage.error('请输入处理标题')
      return
    }
    if (!formData.value.dealType) {
      ElMessage.error('请选择处理类型')
      return
    }
    if (!formData.value.level) {
      ElMessage.error('请选择严重程度')
      return
    }
    if (!formData.value.dutyEmployeeId) {
      ElMessage.error('请选择责任人')
      return
    }
    if (!formData.value.dutyEmployeeOrgId) {
      ElMessage.error('请选择责任部门')
      return
    }
    if (!formData.value.dealEmployeeId) {
      ElMessage.error('请选择处理人')
      return
    }

    // 验证处理措施
    // for (let i = 0; i < formData.value.measureList.length; i++) {
    //   const measure = formData.value.measureList[i]
    //   if (!measure.measureDesc) {
    //     ElMessage.error(`请填写第${i + 1}个处理措施的描述`)
    //     return
    //   }
    //   if (!measure.measureType) {
    //     ElMessage.error(`请选择第${i + 1}个处理措施的类型`)
    //     return
    //   }
    // }

    // 格式化日期
    const submitData = {
      ...formData.value,
      startDate: formData.value.startDate,
      finishDate: formData.value.finishDate,
    }

    let result
    if (formData.value.id) {
      // 更新处理措施
      result = await handlingMeasuresApi.updateHandlingMeasure(submitData)
      ElMessage.success('更新成功')
    }
    else {
      // 新增处理措施
      result = await handlingMeasuresApi.createHandlingMeasure(submitData)
      ElMessage.success('保存成功')
    }

    // 可以在这里添加跳转逻辑，比如返回列表页
    // router.push('/hegui/respond/accountability/handlingMeasures')
    router.back()
    return result
  }
  catch (error) {
    ElMessage.error('保存失败，请检查网络连接或联系管理员')
    console.error('保存失败:', error)
    throw error
  }
}

// 保存并开始处理
async function _saveAndStart() {
  try {
    formData.value.status = 'PROGRESSING'
    await saveHandlingMeasure()
    ElMessage.success('保存并开始处理成功')
  }
  catch (error) {
    ElMessage.error('操作失败')
  }
}

// 取消操作
function _cancelOperation() {
  router.back()
  // 返回列表页面或关闭弹窗
}

// 监听自动生成编号变化
// watch(_autoGenerateCode, (newValue) => {
//   if (newValue) {
//     fetchDealCode()
//   }
//   else {
//     formData.value.dealCode = ''
//   }
// })

// 页面加载时获取编号和选项
onMounted(() => {
  if (route.query.id) {
    formData.value.id = route.query.id as string
    fetchHandlingMeasureDetail(route.query.id)
  }
  else {
    fetchDealCode()
  }
  fetchDealTypeOptions()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{ formData.id ? '编辑处理措施' : '新增处理措施' }}
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="saveHandlingMeasure">
              <i class="el-icon-check mr-1" />保存
            </el-button>
            <!-- <el-button plain class="!rounded-button whitespace-nowrap" @click="_saveAndStart">
              <i class="el-icon-check mr-1" />保存并开始处理
            </el-button> -->
            <el-button plain class="!rounded-button whitespace-nowrap" @click="_cancelOperation">
              <i class="el-icon-close mr-1" />取消
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col class="m-auto" :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  基本信息
                </div>
              </template>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="处理标题" required>
                  <el-input v-model="formData.title" placeholder="请输入处理标题" class="h-8" />
                </el-form-item>
                <el-form-item label="处理编号">
                  <div class="flex items-center space-x-3">
                    <el-input v-model="formData.dealCode" placeholder="系统自动生成" class="h-8" readonly />
                  </div>
                </el-form-item>
                <el-form-item label="处理类型" required>
                  <el-select v-model="formData.dealType" placeholder="请选择处理类型" class="w-full">
                    <el-option
                      v-for="option in dealTypeOptions"
                      :key="option.value"
                      :label="option.name"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="严重程度" required>
                  <el-radio-group v-model="formData.level">
                    <el-radio-button label="HIGH" class="text-red-500">
                      严重
                    </el-radio-button>
                    <el-radio-button label="MIDDLE" class="text-orange-500">
                      中等
                    </el-radio-button>
                    <el-radio-button label="LOW" class="text-yellow-500">
                      轻微
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="关联调查" required>
                  <ViolationInvestigationSelector
                    v-model="formData.investigateId"
                    :display-value="formData.investigateName"
                    display-key="title"
                    placeholder="请选择关联的违规问题调查"
                    @change="handleInvestigateChange"
                  />
                </el-form-item>
                <el-form-item label="责任人" required>
                  <DepartPerson v-model="formData.dutyEmployeeId" placeholder="请选择责任人" />
                </el-form-item>
                <el-form-item label="责任部门" required>
                  <DepartmentTreeSelect v-model="formData.dutyEmployeeOrgId" placeholder="请选择责任部门" />
                </el-form-item>
                <el-form-item label="处理人" required>
                  <DepartPerson v-model="formData.dealEmployeeId" placeholder="请选择处理人" />
                </el-form-item>
                <el-form-item label="开始日期">
                  <el-date-picker v-model="formData.startDate" type="date" placeholder="选择日期" class="w-full" />
                </el-form-item>
                <el-form-item label="计划完成日期">
                  <el-date-picker v-model="formData.finishDate" type="date" placeholder="选择日期" class="w-full" />
                </el-form-item>
                <el-form-item label="处理状态">
                  <el-radio-group v-model="formData.status">
                    <el-radio-button label="NO_START">
                      未开始
                    </el-radio-button>
                    <el-radio-button label="PROGRESSING">
                      进行中
                    </el-radio-button>
                    <el-radio-button label="FINISHED">
                      已完成
                    </el-radio-button>
                    <el-radio-button label="PAUSED">
                      已暂停
                    </el-radio-button>
                    <el-radio-button label="CANCELED">
                      已取消
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  违规情况
                </div>
              </template>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="违规描述" required>
                  <el-input v-model="formData.violationDesc" type="textarea" :rows="5" placeholder="请描述违规行为的具体情况" />
                </el-form-item>
                <el-form-item v-if="false" label="违规依据">
                  <div class="w-full">
                    <el-button type="text" icon="el-icon-plus">
                      添加依据
                    </el-button>
                    <el-button type="text" icon="el-icon-document">
                      从法规库选择
                    </el-button>
                    <el-button type="text" icon="el-icon-document">
                      从制度库选择
                    </el-button>
                    <el-table :data="[]" class="mt-3" border>
                      <el-table-column prop="type" label="依据类型" width="150" />
                      <el-table-column prop="name" label="依据名称" />
                      <el-table-column prop="clause" label="具体条款" />
                      <el-table-column label="操作" width="80">
                        <template #default>
                          <el-button type="text" icon="el-icon-delete" class="text-red-500" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
                <el-form-item label="违规影响" required>
                  <el-input v-model="formData.violationImpact" type="textarea" :rows="5" placeholder="请描述违规行为的影响和后果" />
                </el-form-item>
                <el-form-item label="责任认定" required>
                  <el-input v-model="formData.responsibility" type="textarea" :rows="5" placeholder="请描述责任认定的依据和结果" />
                </el-form-item>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  违规情况
                </div>
              </template>
              <div class="w-full">
                <div v-for="(measure, index) in formData.measureList" :key="index" class="mb-6 border border-gray-200 rounded-lg p-4">
                  <div class="mb-4 flex items-center justify-between">
                    <h4 class="text-lg font-medium">
                      处理措施 {{ index + 1 }}
                    </h4>
                    <el-button v-if="formData.measureList.length > 1" type="danger" size="small" @click="formData.measureList.splice(index, 1)">
                      删除
                    </el-button>
                  </div>
                  <el-form label-position="right" label-width="120px">
                    <el-form-item label="措施名称" required>
                      <el-input
                        v-model="measure.name"
                        placeholder="请输入措施名称"
                        maxlength="128"
                        show-word-limit
                        class="w-full"
                      />
                    </el-form-item>
                    <el-form-item label="措施类型" required>
                      <el-select v-model="measure.measureType" placeholder="请选择措施类型" class="w-full">
                        <el-option
                          v-for="option in measureTypeOptions"
                          :key="option.value"
                          :label="option.name"
                          :value="option.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="执行期限">
                      <div class="flex space-x-2">
                        <el-date-picker
                          v-model="measure.startDate"
                          type="date"
                          placeholder="开始日期"
                          class="flex-1"
                        />
                        <el-date-picker
                          v-model="measure.finishDate"
                          type="date"
                          placeholder="完成日期"
                          class="flex-1"
                        />
                      </div>
                    </el-form-item>
                    <el-form-item label="状态">
                      <el-select v-model="measure.status" placeholder="请选择状态" class="w-full">
                        <el-option label="未开始" value="NO_START" />
                        <el-option label="进行中" value="PROGRESSING" />
                        <el-option label="已完成" value="FINISHED" />
                        <el-option label="已暂停" value="PAUSED" />
                        <el-option label="已取消" value="CANCELED" />
                      </el-select>
                    </el-form-item>
                  </el-form>
                </div>
                <el-button type="primary" class="w-full" @click="addMeasure">
                  + 添加处理措施
                </el-button>
              </div>
            </el-card>
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关信息
                </div>
              </template>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="相关制度">
                  <div class="w-full">
                    <el-button type="text" icon="el-icon-plus">
                      添加制度
                    </el-button>
                    <el-table :data="[]" class="mt-3" border>
                      <el-table-column prop="name" label="制度名称" />
                      <el-table-column prop="version" label="版本号" width="120" />
                      <el-table-column label="操作" width="80">
                        <template #default>
                          <el-button type="text" icon="el-icon-delete" class="text-red-500" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
                <el-form-item label="相关案例">
                  <div class="w-full">
                    <el-button type="text" icon="el-icon-plus">
                      添加案例
                    </el-button>
                    <el-table :data="[]" class="mt-3" border>
                      <el-table-column prop="name" label="案例名称" />
                      <el-table-column prop="type" label="类型" width="120" />
                      <el-table-column label="操作" width="80">
                        <template #default>
                          <el-button type="text" icon="el-icon-delete" class="text-red-500" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
                <el-form-item label="关联文件">
                  <div class="w-full">
                    <el-upload action="#" list-type="text">
                      <el-button type="text" icon="el-icon-upload">
                        上传文件
                      </el-button>
                    </el-upload>
                    <el-table :data="[]" class="mt-3" border>
                      <el-table-column prop="name" label="文件名" />
                      <el-table-column prop="size" label="大小" width="120" />
                      <el-table-column prop="time" label="上传时间" width="180" />
                      <el-table-column label="操作" width="180">
                        <template #default>
                          <el-button type="text" icon="el-icon-view" />
                          <el-button type="text" icon="el-icon-delete" class="text-red-500" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
              </el-form>
            </el-card>
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  通知设置
                </div>
              </template>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="通知对象">
                  <el-select multiple placeholder="请选择通知对象" class="w-full">
                    <el-option label="张三 - 财务部" value="1" />
                    <el-option label="李四 - 人事部" value="2" />
                    <el-option label="王五 - 合规部" value="3" />
                  </el-select>
                </el-form-item>
                <el-form-item label="通知方式">
                  <el-checkbox-group>
                    <el-checkbox label="系统消息" />
                    <el-checkbox label="电子邮件" />
                    <el-checkbox label="短信" />
                    <el-checkbox label="其他" />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="通知事件">
                  <el-checkbox-group>
                    <el-checkbox label="处理开始" />
                    <el-checkbox label="处理完成" />
                    <el-checkbox label="状态变更" />
                    <el-checkbox label="其他重要更新" />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="通知内容">
                  <el-input type="textarea" :rows="5" placeholder="请输入自定义通知内容模板" />
                </el-form-item>
              </el-form>
            </el-card>
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  权限设置
                </div>
              </template>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="查看权限">
                  <el-radio-group>
                    <el-radio label="全公司" />
                    <el-radio label="部门主管" />
                    <el-radio label="指定人员" />
                    <el-radio label="仅处理人员" />
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="false" label="指定查看人员">
                  <el-select multiple placeholder="请选择查看人员" class="w-full">
                    <el-option label="张三 - 财务部" value="1" />
                    <el-option label="李四 - 人事部" value="2" />
                  </el-select>
                </el-form-item>
                <el-form-item label="编辑权限">
                  <el-radio-group>
                    <el-radio label="仅处理人" />
                    <el-radio label="指定人员" />
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="false" label="指定编辑人员">
                  <el-select multiple placeholder="请选择编辑人员" class="w-full">
                    <el-option label="王五 - 合规部" value="1" />
                    <el-option label="赵六 - 人事部" value="2" />
                  </el-select>
                </el-form-item>
                <el-form-item label="保密级别">
                  <el-radio-group>
                    <el-radio label="普通" />
                    <el-radio label="保密" />
                    <el-radio label="高度保密" />
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
          <el-col v-if="false" :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  处理模板
                </div>
              </template>
              <div class="space-y-3">
                <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
                  <div class="font-medium">
                    财务违规处理模板
                  </div>
                  <div class="text-xs text-gray-500">
                    适用于财务违规场景
                  </div>
                </div>
                <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
                  <div class="font-medium">
                    人事违规处理模板
                  </div>
                  <div class="text-xs text-gray-500">
                    适用于人事违规场景
                  </div>
                </div>
                <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
                  <div class="font-medium">
                    信息安全违规处理模板
                  </div>
                  <div class="text-xs text-gray-500">
                    适用于信息安全违规场景
                  </div>
                </div>
                <el-button type="text" icon="el-icon-plus" class="w-full">
                  保存为模板
                </el-button>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  AI辅助
                </div>
              </template>
              <div class="space-y-3">
                <el-button type="text" icon="el-icon-magic-stick" class="w-full">
                  AI建议处理措施
                </el-button>
                <el-button type="text" icon="el-icon-data-analysis" class="w-full">
                  AI分析影响
                </el-button>
                <el-button type="text" icon="el-icon-check" class="w-full">
                  AI合规性检查
                </el-button>
                <div class="border rounded p-3">
                  <div class="text-sm font-medium">
                    AI建议区
                  </div>
                  <div class="mt-2 text-xs text-gray-600">
                    根据当前输入内容，AI将在此处生成建议...
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相似案例
                </div>
              </template>
              <div class="space-y-4">
                <div
                  v-for="(item, index) in similarCases" :key="index"
                  class="cursor-pointer border rounded p-4 transition-colors hover:bg-gray-50"
                >
                  <div class="flex items-start justify-between">
                    <div>
                      <div class="text-gray-800 font-medium">
                        {{ item.title }}
                      </div>
                      <div class="mt-2 flex items-center text-sm space-x-4">
                        <span class="text-gray-600">处理类型: {{ item.type }}</span>
                        <span class="text-gray-600">处理结果: {{ item.result }}</span>
                      </div>
                    </div>
                    <el-button type="text" size="small" @click="viewCaseDetail(item.id)">
                      <el-icon>
                        <View />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .container {
    max-width: 1440px;
  }

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-form-item__label {
    font-size: 14px;
    color: #666;
  }

  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  .el-textarea__inner {
    min-height: 120px;
  }

  .el-card {
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .el-card__header {
    padding: 18px 20px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-table {
    width: 100%;
  }

  .el-table th {
    background-color: #f5f7fa;
  }

  .el-radio-button--mini .el-radio-button__inner {
    padding: 7px 12px;
  }

  .el-checkbox {
    margin-right: 15px;
  }
</style>
