<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import eventBus from '@/utils/eventBus'
import useSettingsStore from '@/store/modules/settings'
import baseInfo from '@/api/personal/baseInfo'
import useUserStore from '@/store/modules/user'
import storage from '@/utils/storage'

defineOptions({
  name: 'Respwd',
})

const _userinfo: any = ref(JSON.parse(storage.local.get('userinfo') || '') ?? '')

const userStore = useUserStore()
const isShow = ref(false)

const _settingsStore = useSettingsStore()
const formRef = ref<FormInstance>()
const form: any = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})

function validateCurrentPassword(rule: any, value: string, callback: any) {
  if (!value) {
    callback(new Error('请输入当前密码'))
  }
  else if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'))
  }
  else {
    callback()
  }
}

function validateNewPassword(rule: any, value: string, callback: any) {
  if (!value) {
    callback(new Error('请输入新密码'))
  }
  else if (value.length < 8) {
    callback(new Error('密码长度不能少于8位'))
  }
  else if (!/[A-Z]/.test(value)) {
    callback(new Error('必须包含至少一个大写字母'))
  }
  else if (!/[a-z]/.test(value)) {
    callback(new Error('必须包含至少一个小写字母'))
  }
  else if (!/[0-9]/.test(value)) {
    callback(new Error('必须包含至少一个数字'))
  }
  else if (!/[^A-Za-z0-9]/.test(value)) {
    callback(new Error('必须包含至少一个特殊字符'))
  }
  else {
    callback()
  }
}

function validateConfirmPassword(rule: any, value: string, callback: any) {
  if (!value) {
    callback(new Error('请再次输入新密码'))
  }
  else if (value !== form.value.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  }
  else {
    callback()
  }
}

const rules = ref<FormRules>({
  currentPassword: [{ validator: validateCurrentPassword, trigger: 'blur' }],
  newPassword: [{ validator: validateNewPassword, trigger: 'blur' }],
  confirmPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }],
})
onMounted(() => {
  eventBus.on('reswpd', () => {
    isShow.value = !isShow.value
  })
})
async function onSubmit() {
  formRef.value && formRef.value.validate(async (valid: any) => {
    if (valid) {
      ElMessageBox.confirm(
        '确认提交?',
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        },
      )
        .then(async () => {
          try {
            const userId = userStore.userId
            await baseInfo.updatePassword(userId, {
              currentPassword: form.value.currentPassword,
              newPassword: form.value.newPassword,
              confirmPassword: form.value.confirmPassword,
            })
            ElMessage.success('密码修改成功')
            isShow.value = false
            formRef.value?.resetFields()
            userStore.logout()
          }
          catch (error: any) {
            ElMessage.error(error.message || '密码修改失败，请重试')
          }
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消',
          })
        })
    }
  })
}
</script>

<template>
  <HDialog v-model="isShow" title="修改密码">
    <div class="px-4">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input v-model="form.currentPassword" type="password" placeholder="请输入当前密码" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="form.newPassword" type="password" placeholder="请输入新密码" show-password />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input v-model="form.confirmPassword" type="password" placeholder="请再次输入新密码" show-password />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="fotterbtn">
        <el-button class="cancel" @click="isShow = false; form.currentPassword = ''; form.newPassword = ''; form.confirmPassword = ''">
          取消
        </el-button>
        <el-button v-auth="'Respwd/index/save'" type="primary" @click="onSubmit">
          保存
        </el-button>
      </div>
    </template>
  </HDialog>
</template>
