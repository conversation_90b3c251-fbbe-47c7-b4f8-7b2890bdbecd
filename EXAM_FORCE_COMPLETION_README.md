# 🔒 考试强制完成机制实现文档

## 📋 功能概述

在考试页面（`src/views/hegui/prevention/training/examination/center.vue`）中实现了完整的强制考试完成机制，确保考试的公平性和完整性。

## 🚀 核心功能

### 1. **页面离开检测**

#### 监听事件
- `beforeunload` 事件：检测页面关闭、刷新、导航离开
- `visibilitychange` 事件：检测页面可见性变化（切换标签页等）

#### 触发场景
- ✅ 关闭浏览器标签页
- ✅ 刷新页面（F5、Ctrl+R）
- ✅ 导航到其他页面
- ✅ 关闭浏览器窗口
- ✅ 使用浏览器前进/后退按钮
- ✅ 切换到其他应用程序

### 2. **自动提交机制**

#### 自动提交触发条件
- 检测到页面离开行为
- 考试时间到达
- 用户主动点击返回按钮

#### 提交逻辑
```typescript
async function submitExam(isDefaultSubmit = false, isAutoSubmit = false)
```
- `isDefaultSubmit`: 是否为默认提交（返回按钮触发）
- `isAutoSubmit`: 是否为自动提交（页面离开/时间到达触发）

### 3. **考试开始前警告**

#### 警告内容
- 考试期间不能离开页面
- 不能刷新页面或关闭浏览器标签
- 不能使用浏览器前进/后退按钮
- 违反规则将导致考试自动提交

#### 实现方式
```typescript
async function showExamWarning()
```
- 使用 `ElMessageBox.confirm` 显示详细警告
- 用户必须确认才能开始考试
- 取消将返回上一页

### 4. **时间管理**

#### 倒计时警告
- 剩余5分钟：黄色警告提示
- 剩余1分钟：红色紧急提示
- 时间到达：立即自动提交

#### 时间到达处理
```typescript
// 时间到达，立即自动提交
console.log('考试时间到达，执行自动提交')
ElMessage.error('考试时间已结束，正在自动提交...')
setTimeout(() => {
  submitExam(true, true)
}, 500)
```

### 5. **异常处理与状态恢复**

#### 本地存储备份
```typescript
function saveExamStateToLocal()
function restoreExamStateFromLocal()
```
- 自动提交失败时保存考试状态到 `localStorage`
- 用户重新进入时尝试恢复考试状态
- 包含题目答案、当前题目、剩余时间等信息

#### 网络异常处理
- 自动提交失败时保存状态到本地
- 记录失败原因和时间点
- 提供状态恢复机制

## 🎨 用户界面增强

### 1. **考试状态指示器**
```vue
<el-tag v-if="!isSubmit && !autoSubmitInProgress" type="success" size="small">
  <el-icon><Document /></el-icon>
  考试进行中
</el-tag>
```

### 2. **状态显示**
- 🟢 考试进行中
- 🟡 正在提交...
- 🔵 已提交

### 3. **时间警告**
- 5分钟警告：`ElMessage.warning`
- 1分钟警告：`ElMessage.error`
- 时间到达：自动提交提示

## 🔧 技术实现细节

### 1. **防重复提交**
```typescript
const autoSubmitInProgress = ref(false)

if (autoSubmitInProgress.value) {
  return // 防止重复提交
}
```

### 2. **监听器管理**
```typescript
// 添加监听器
function addPageLeaveListeners() {
  window.addEventListener('beforeunload', handleBeforeUnload)
  document.addEventListener('visibilitychange', handleVisibilityChange)
}

// 移除监听器
function removePageLeaveListeners() {
  window.removeEventListener('beforeunload', handleBeforeUnload)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
}
```

### 3. **生命周期管理**
- `onMounted`: 显示警告、启用监听器
- `onUnmounted`: 清理定时器、移除监听器

## 📊 日志记录

### 关键操作日志
```typescript
console.log('考试页面离开监听已启用')
console.log('检测到页面离开，执行自动提交')
console.log('考试时间到达，执行自动提交')
console.log('考试状态已保存到本地存储')
```

### 提交原因记录
```typescript
const submitReason = isAutoSubmit ? 'auto_submit' : 'manual_submit'
console.log(`考试提交 - 原因: ${submitReason}, 时间: ${new Date().toISOString()}`)
```

## 🛡️ 安全特性

### 1. **多重保护**
- 页面离开检测
- 时间到达自动提交
- 状态备份恢复

### 2. **防作弊机制**
- 强制页面停留
- 自动提交警告
- 状态监控记录

### 3. **数据完整性**
- 本地状态备份
- 网络异常处理
- 重复提交防护

## 🎯 使用场景

### 正常考试流程
1. 用户进入考试页面
2. 显示考试须知警告
3. 用户确认开始考试
4. 启用页面离开监听
5. 用户正常答题
6. 手动提交或时间到达自动提交

### 异常情况处理
1. **意外离开**: 自动提交当前答案
2. **网络异常**: 保存状态到本地存储
3. **时间到达**: 立即自动提交
4. **重新进入**: 尝试恢复之前状态

## 📝 配置说明

### 时间警告配置
```typescript
if (countdown.value === 300) { // 5分钟警告
  ElMessage.warning('考试时间还剩5分钟，请抓紧时间答题！')
}
else if (countdown.value === 60) { // 1分钟警告
  ElMessage.error('考试时间还剩1分钟，即将自动提交！')
}
```

### 本地存储键名
```typescript
localStorage.setItem('exam_state_backup', JSON.stringify(examState))
```

## 🔄 后续优化建议

1. **服务端同步**: 定期向服务端同步考试状态
2. **断网检测**: 添加网络状态监控
3. **多设备防护**: 检测同一账号多设备登录
4. **行为分析**: 记录用户答题行为轨迹
5. **智能恢复**: 更智能的状态恢复策略

## 📞 技术支持

如有问题或需要进一步优化，请联系开发团队。
