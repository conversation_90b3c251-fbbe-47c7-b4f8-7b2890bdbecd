import api from '@/api/index'

const baseUrl = '/whiskerguardtrainingservice/api'

export default {
  // 考试列表
  getExamList(params: any) {
    return api({
      url: `${baseUrl}/exam/management/center/query`,
      method: 'POST',
      data: params,
    })
  },
  // 考试页面的详情
  getExamDetail(id: string) {
    return api({
      url: `${baseUrl}/exam/management/detail/${id}`,
      method: 'GET',
    })
  },
  // 开始考试
  startExam(examId: string) {
    return api({
      url: `${baseUrl}/exam/management/start?examId=${examId}`,
      method: 'POST',
    })
  },
  // 获取考试题目
  getExamQuestion(id: string) {
    return api({
      url: `${baseUrl}/exam/management/questions/${id}`,
      method: 'GET',
    })
  },
  // 提交考试
  submitExam(examRecordId: string, params: any) {
    return api({
      url: `${baseUrl}/exam/management/submit/all/answers?examRecordId=${examRecordId}`,
      method: 'POST',
      data: params,
    })
  },
  // 错题分析
  wrongAnalysis(examRecordId: string) {
    return api({
      url: `${baseUrl}/exam/management/wrong/analysis/${examRecordId}`,
      method: 'GET',
    })
  },

  // 考试信息管理
  // 获取考试信息详情
  getExamInfo(id: number) {
    return api({
      url: `${baseUrl}/exam/infos/${id}`,
      method: 'GET',
    })
  },
  // 创建考试信息
  createExamInfo(params: any) {
    return api({
      url: `${baseUrl}/exam/infos`,
      method: 'POST',
      data: params,
    })
  },
  // 更新考试信息
  updateExamInfo(id: number, params: any) {
    return api({
      url: `${baseUrl}/exam/infos/${id}`,
      method: 'PUT',
      data: params,
    })
  },
  // 删除考试信息
  deleteExamInfo(id: number) {
    return api({
      url: `${baseUrl}/exam/infos/${id}`,
      method: 'DELETE',
    })
  },
  // 生成考题
  generateExamQuestion(data: any) {
    return api.post(`/whiskerguardtrainingservice/api/exam/management/generate/questions?examId=${data.examId}&courseId=${data.courseId}`, {})
  },
}
