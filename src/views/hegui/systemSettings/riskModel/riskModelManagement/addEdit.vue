<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import riskModelApi from '@/api/riskModel/index'
import { disablePastDates } from '@/utils/dateUtils'

// 类型定义
interface RiskRule {
  id: number | null
  code: string
  name: string
  ruleType: string
  params: string
  description: string
  score: number
}

interface RiskCategory {
  id: number | null
  name: string
  level: string
  expression: string
  description: string
  riskRules: RiskRule[]
}

interface ModelForm {
  id: number | null
  name: string
  description: string
  effectiveFrom: string | undefined
  effectiveTo: string | undefined
  isDefault: boolean
  version: number
  categories: RiskCategory[]
}

const router = useRouter()
const route = useRoute()

// 编辑状态
const isEdit = ref(false)
const modelId = ref<number | null>(null)

// 表单数据
const modelForm = reactive<ModelForm>({
  id: null,
  name: '',
  description: '',
  effectiveFrom: undefined,
  effectiveTo: undefined,
  isDefault: false,
  version: 0,
  categories: [],
})

// 表单引用
const modelFormRef = ref(),

// 表单验证规则
modelFormRules = {
  name: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入模型描述', trigger: 'blur' }],
  effectiveFrom: [{ required: true, message: '请选择生效开始时间', trigger: 'change' }],
  effectiveTo: [{ required: true, message: '请选择生效结束时间', trigger: 'change' }],
},

// 风险分类数据
riskCategory = reactive({
  name: '',
  level: 'HIGH',
  expression: '',
  description: '',
}),

// 风险规则数据 - 为每个分类创建独立的规则表单
riskRules = ref<{ [key: number]: any }>({}),

// 编辑状态管理
editingCategory = ref<number | null>(null),
editingRule = ref<{ categoryIndex: number; ruleIndex: number } | null>(null)

// 获取或创建指定分类的风险规则表单
function getRiskRuleForm(categoryIndex: number) {
  if (!riskRules.value[categoryIndex]) {
    riskRules.value[categoryIndex] = reactive({
      code: '',
      name: '',
      ruleType: 'THRESHOLD',
      params: '',
      description: '',
      score: 0,
    })
  }
  return riskRules.value[categoryIndex]
}

// 开始编辑分类
function startEditCategory(categoryIndex: number) {
  editingCategory.value = categoryIndex
  const category = modelForm.categories[categoryIndex]
  Object.assign(riskCategory, {
    name: category.name,
    level: category.level,
    expression: category.expression,
    description: category.description,
  })
}

// 保存分类编辑
function saveEditCategory() {
  if (editingCategory.value !== null) {
    const category = modelForm.categories[editingCategory.value]
    Object.assign(category, {
      name: riskCategory.name,
      level: riskCategory.level,
      expression: riskCategory.expression,
      description: riskCategory.description,
    })
    cancelEditCategory()
    ElMessage.success('分类修改成功')
  }
}

// 取消编辑分类
function cancelEditCategory() {
  editingCategory.value = null
  Object.assign(riskCategory, {
    name: '',
    level: 'HIGH',
    expression: '',
    description: '',
  })
}

// 开始添加规则
function startAddRule(categoryIndex: number) {
  editingRule.value = { categoryIndex, ruleIndex: -1 }
  const ruleForm = getRiskRuleForm(categoryIndex)
  Object.assign(ruleForm, {
    code: '',
    name: '',
    ruleType: 'THRESHOLD',
    params: '',
    description: '',
    score: 0,
  })
}

// 开始编辑规则
function startEditRule(categoryIndex: number, ruleIndex: number) {
  editingRule.value = { categoryIndex, ruleIndex }
  const rule = modelForm.categories[categoryIndex].riskRules[ruleIndex]
  const ruleForm = getRiskRuleForm(categoryIndex)
  Object.assign(ruleForm, {
    code: rule.code,
    name: rule.name,
    ruleType: rule.ruleType,
    params: rule.params,
    description: rule.description,
    score: rule.score,
  })
}

// 保存规则编辑
function saveEditRule() {
  if (editingRule.value) {
    const { categoryIndex, ruleIndex } = editingRule.value
    const ruleForm = getRiskRuleForm(categoryIndex)
    const rule = modelForm.categories[categoryIndex].riskRules[ruleIndex]
    Object.assign(rule, {
      code: ruleForm.code,
      name: ruleForm.name,
      ruleType: ruleForm.ruleType,
      params: ruleForm.params,
      description: ruleForm.description,
      score: ruleForm.score,
    })
    cancelEditRule()
    ElMessage.success('规则修改成功')
  }
}

// 取消编辑规则
function cancelEditRule() {
  if (editingRule.value) {
    const { categoryIndex } = editingRule.value
    const ruleForm = getRiskRuleForm(categoryIndex)
    Object.assign(ruleForm, {
      code: '',
      name: '',
      ruleType: 'THRESHOLD',
      params: '',
      description: '',
      score: 0,
    })
  }
  editingRule.value = null
}

// 加载状态
const submitLoading = ref(false)

// 风险等级选项
const riskLevelOptions = [
  { label: '高风险', value: 'HIGH' },
  { label: '中风险', value: 'MEDIUM' },
  { label: '低风险', value: 'LOW' },
]

// 规则类型选项
const ruleTypeOptions = [
  { label: '阈值规则', value: 'THRESHOLD' },
  { label: '条件规则', value: 'CONDITION' },
  { label: '评分规则', value: 'SCORE' },
]

// 获取风险模型详情
async function getModelDetail() {
  if (!modelId.value) {
    return
  }

  try {
    const response = await riskModelApi.getRiskModelDetail(modelId.value)
    if (response) {
      _fillFormData(response)
    }
  }
  catch (error) {
    console.error('获取风险模型详情失败:', error)
    ElMessage.error('获取风险模型详情失败')
  }
}

// 保存风险模型
async function saveModel() {
  if (!modelFormRef.value) {
    return
  }

  try {
    await modelFormRef.value.validate()
    submitLoading.value = true

    const formData = {
      ...modelForm,
      categories: modelForm.categories.map(cat => ({
        ...cat,
        riskRules: cat.riskRules || [],
      })),
    }

    if (isEdit.value) {
      // 更新操作
      await riskModelApi.updateRiskModel(formData)
      ElMessage.success('风险模型更新成功')
    }
    else {
      // 新增操作
      await riskModelApi.createRiskModel(formData)
      ElMessage.success('风险模型创建成功')
    }
    router.push('/systemSettings/riskModel/riskModelManagement')
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('保存风险模型失败:', error)
      ElMessage.error('保存风险模型失败')
    }
  }
  finally {
    submitLoading.value = false
  }
}

// 取消编辑
async function cancelEdit() {
  try {
    await ElMessageBox.confirm(
      '确定要取消编辑吗？未保存的内容将丢失。',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '继续编辑',
        type: 'warning',
      },
    )
    router.go(-1)
  }
  catch {
    // 用户取消
  }
}

// 添加风险分类
function addRiskCategory() {
  if (!riskCategory.name.trim()) {
    ElMessage.warning('请输入分类名称')
    return
  }

  const newCategory: RiskCategory = {
    id: null, // 新增时ID为null
    name: riskCategory.name,
    level: riskCategory.level,
    expression: riskCategory.expression,
    description: riskCategory.description,
    riskRules: [],
  }

  modelForm.categories.push(newCategory)

  // 重置表单
  Object.assign(riskCategory, {
    name: '',
    level: 'HIGH',
    expression: '',
    description: '',
  })

  ElMessage.success('风险分类添加成功')
}

// 删除风险分类
function removeRiskCategory(index: number) {
  modelForm.categories.splice(index, 1)
  ElMessage.success('风险分类删除成功')
}

// 添加风险规则
function addRiskRule(categoryIndex: number) {
  const currentRiskRule = getRiskRuleForm(categoryIndex)
  if (!currentRiskRule.name.trim()) {
    ElMessage.warning('请输入规则名称')
    return
  }

  const newRule: RiskRule = {
    id: null, // 新增时ID为null
    code: currentRiskRule.code,
    name: currentRiskRule.name,
    ruleType: currentRiskRule.ruleType,
    params: currentRiskRule.params,
    description: currentRiskRule.description,
    score: currentRiskRule.score,
  }

  if (!modelForm.categories[categoryIndex].riskRules) {
    modelForm.categories[categoryIndex].riskRules = []
  }

  modelForm.categories[categoryIndex].riskRules.push(newRule)

  // 重置当前分类的表单
  Object.assign(currentRiskRule, {
    code: '',
    name: '',
    ruleType: 'THRESHOLD',
    params: '',
    description: '',
    score: 0,
  })

  ElMessage.success('风险规则添加成功')
}

// 删除风险规则
function removeRiskRule(categoryIndex: number, ruleIndex: number) {
  modelForm.categories[categoryIndex].riskRules.splice(ruleIndex, 1)
  ElMessage.success('风险规则删除成功')
}

// 数据回填函数
function _fillFormData(data: any) {
  if (data) {
    Object.assign(modelForm, {
      id: data.id,
      name: data.name,
      description: data.description,
      effectiveFrom: data.effectiveFrom,
      effectiveTo: data.effectiveTo,
      isDefault: data.isDefault,
      version: data.version,
      categories: data.categories || [],
    })
  }
}

// 组件挂载时获取数据
onMounted(() => {
  // 检查路由参数，判断是新增还是编辑
  const id = route.query.id
  if (id) {
    isEdit.value = true
    modelId.value = Number(id)
  }

  // 如果是编辑模式，获取详情数据
  if (isEdit.value) {
    getModelDetail()
  }
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="mr-4 text-xl c-[#000000] font-bold">
              {{ isEdit ? '编辑风险模型' : '新建风险模型' }}
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button v-debounce="3000" type="primary" class="!rounded-button whitespace-nowrap" :loading="submitLoading" @click="saveModel">
              保存
            </el-button>
            <el-button v-debounce="3000" class="!rounded-button whitespace-nowrap" @click="cancelEdit">
              取消
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <div class="min-h-screen flex bg-gray-50">
          <!-- 主内容区 -->
          <div class="flex-1 overflow-auto">
            <!-- 主编辑区 -->
            <div class="mx-6 mt-6 flex">
              <!-- 中间编辑区 -->
              <div class="mr-6 flex-1">
                <!-- 基本信息设置 -->
                <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                  <h2 class="mb-4 text-lg text-gray-900 font-semibold">
                    基本信息设置
                  </h2>
                  <el-form ref="modelFormRef" :model="modelForm" :rules="modelFormRules" label-width="120px">
                    <!-- 模型名称 -->
                    <el-form-item label="模型名称" prop="name">
                      <el-input
                        v-model="modelForm.name"
                        placeholder="请输入风险模型名称"
                        maxlength="100"
                        show-word-limit
                        clearable
                      />
                    </el-form-item>

                    <!-- 模型描述 -->
                    <el-form-item label="模型描述" prop="description">
                      <el-input
                        v-model="modelForm.description"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入风险模型描述"
                        maxlength="500"
                        show-word-limit
                      />
                    </el-form-item>

                    <!-- 生效时间 -->
                    <el-form-item label="生效时间" prop="effectiveFrom">
                      <div class="w-full flex justify-between">
                        <el-date-picker
                          v-model="modelForm.effectiveFrom"
                          type="datetime"
                          placeholder="开始时间"
                          style="width: 48%"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          :disabled-date="disablePastDates"
                        />
                        <span class="px-2 text-gray-500">至</span>
                        <el-date-picker
                          v-model="modelForm.effectiveTo"
                          type="datetime"
                          placeholder="结束时间"
                          style="width: 48%"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                        />
                      </div>
                    </el-form-item>

                    <!-- 是否默认模型 -->
                    <el-form-item label="默认模型">
                      <el-switch
                        v-model="modelForm.isDefault"
                        active-text="是"
                        inactive-text="否"
                      />
                      <div class="mt-1 text-xs text-gray-500">
                        设为默认模型后，新建评估将自动使用此模型
                      </div>
                    </el-form-item>
                  </el-form>
                </div>

                <!-- 风险分类管理 -->
                <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                  <h2 class="mb-4 text-lg text-gray-900 font-semibold">
                    风险分类管理
                  </h2>

                  <!-- 添加/编辑风险分类 -->
                  <div class="mb-6 border border-gray-200 rounded-lg bg-gray-50 p-4">
                    <h3 class="mb-3 text-base font-medium">
                      {{ editingCategory !== null ? '编辑风险分类' : '添加风险分类' }}
                    </h3>
                    <el-form :model="riskCategory" label-width="100px">
                      <el-row :gutter="16">
                        <el-col :span="12">
                          <el-form-item label="分类名称">
                            <el-input v-model="riskCategory.name" placeholder="请输入分类名称" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="风险等级">
                            <el-select v-model="riskCategory.level" placeholder="请选择风险等级" style="width: 100%">
                              <el-option
                                v-for="option in riskLevelOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                              />
                            </el-select>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-form-item label="表达式">
                        <el-input v-model="riskCategory.expression" placeholder="请输入风险表达式" />
                      </el-form-item>
                      <el-form-item label="分类描述">
                        <el-input
                          v-model="riskCategory.description"
                          type="textarea"
                          :rows="2"
                          placeholder="请输入分类描述"
                        />
                      </el-form-item>
                      <el-form-item>
                        <el-button v-if="editingCategory === null" type="primary" @click="addRiskCategory">
                          添加分类
                        </el-button>
                        <div v-else class="space-x-2">
                          <el-button type="primary" @click="saveEditCategory">
                            保存修改
                          </el-button>
                          <el-button @click="cancelEditCategory">
                            取消编辑
                          </el-button>
                        </div>
                      </el-form-item>
                    </el-form>
                  </div>

                  <!-- 已添加的风险分类列表 -->
                  <div v-if="modelForm.categories.length > 0">
                    <h3 class="mb-3 text-base font-medium">
                      已添加的风险分类
                    </h3>
                    <div v-for="(category, categoryIndex) in modelForm.categories" :key="category.id || categoryIndex" class="mb-4 border border-gray-200 rounded-lg p-4">
                      <div class="mb-3 flex items-start justify-between">
                        <div class="flex-1">
                          <h4 class="text-base font-medium">
                            {{ category.name }}
                          </h4>
                          <el-tag :type="category.level === 'HIGH' ? 'danger' : category.level === 'MEDIUM' ? 'warning' : 'success'" size="small">
                            {{ riskLevelOptions.find(opt => opt.value === category.level)?.label }}
                          </el-tag>
                          <p class="mt-1 text-sm text-gray-600">
                            {{ category.description }}
                          </p>
                          <p v-if="category.expression" class="mt-1 text-xs text-gray-500">
                            表达式: {{ category.expression }}
                          </p>
                        </div>
                        <div class="flex space-x-2">
                          <el-button
                            v-if="editingCategory !== categoryIndex"
                            type="primary"
                            size="small"
                            @click="startEditCategory(categoryIndex)"
                          >
                            编辑
                          </el-button>
                          <el-button type="danger" size="small" @click="removeRiskCategory(categoryIndex)">
                            删除
                          </el-button>
                        </div>
                      </div>

                      <!-- 风险规则管理 -->
                      <div class="mt-4">
                        <h5 class="mb-2 text-sm font-medium">
                          风险规则
                        </h5>

                        <!-- 风险规则管理 -->
                        <div class="space-y-3">
                          <!-- 添加新规则按钮 -->
                          <div class="flex justify-between items-center">
                            <span class="text-sm font-medium">规则列表</span>
                            <el-button
                              v-if="!editingRule || editingRule.categoryIndex !== categoryIndex"
                              type="primary"
                              size="small"
                              @click="startAddRule(categoryIndex)"
                            >
                              添加规则
                            </el-button>
                          </div>

                          <!-- 规则列表 -->
                          <div v-if="category.riskRules && category.riskRules.length > 0" class="space-y-2">
                            <div v-for="(rule, ruleIndex) in category.riskRules" :key="rule.id || ruleIndex">
                              <!-- 编辑模式 -->
                              <div v-if="editingRule && editingRule.categoryIndex === categoryIndex && editingRule.ruleIndex === ruleIndex" class="border border-blue-200 rounded bg-blue-50 p-3">
                                <div class="mb-2 flex justify-between items-center">
                                  <span class="text-sm font-medium text-blue-700">编辑规则</span>
                                  <div class="space-x-2">
                                    <el-button type="primary" size="small" @click="saveEditRule">
                                      保存
                                    </el-button>
                                    <el-button size="small" @click="cancelEditRule">
                                      取消
                                    </el-button>
                                  </div>
                                </div>
                                <el-form :model="getRiskRuleForm(categoryIndex)" label-width="80px" size="small">
                                  <el-row :gutter="12">
                                    <el-col :span="8">
                                      <el-form-item label="规则代码">
                                        <el-input v-model="getRiskRuleForm(categoryIndex).code" placeholder="规则代码" />
                                      </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                      <el-form-item label="规则名称">
                                        <el-input v-model="getRiskRuleForm(categoryIndex).name" placeholder="规则名称" />
                                      </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                      <el-form-item label="规则类型">
                                        <el-select v-model="getRiskRuleForm(categoryIndex).ruleType" placeholder="规则类型" style="width: 100%">
                                          <el-option
                                            v-for="option in ruleTypeOptions"
                                            :key="option.value"
                                            :label="option.label"
                                            :value="option.value"
                                          />
                                        </el-select>
                                      </el-form-item>
                                    </el-col>
                                  </el-row>
                                  <el-row :gutter="12">
                                    <el-col :span="12">
                                      <el-form-item label="参数">
                                        <el-input v-model="getRiskRuleForm(categoryIndex).params" placeholder="规则参数" />
                                      </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                      <el-form-item label="评分">
                                        <el-input-number v-model="getRiskRuleForm(categoryIndex).score" :min="0" :max="100" placeholder="评分" style="width: 100%" />
                                      </el-form-item>
                                    </el-col>
                                  </el-row>
                                  <el-form-item label="规则描述">
                                    <el-input
                                      v-model="getRiskRuleForm(categoryIndex).description"
                                      type="textarea"
                                      :rows="2"
                                      placeholder="请输入规则描述"
                                    />
                                  </el-form-item>
                                </el-form>
                              </div>

                              <!-- 显示模式 -->
                              <div v-else class="border border-gray-100 rounded bg-white p-3 hover:bg-gray-50">
                                <div class="flex items-start justify-between">
                                  <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-2">
                                      <span class="font-medium text-gray-900">{{ rule.name }}</span>
                                      <el-tag size="small" type="info">
                                        {{ rule.code }}
                                      </el-tag>
                                      <el-tag size="small">
                                        {{ ruleTypeOptions.find(opt => opt.value === rule.ruleType)?.label }}
                                      </el-tag>
                                      <span class="text-sm text-orange-600 font-medium">评分: {{ rule.score }}</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-1">
                                      {{ rule.description }}
                                    </p>
                                    <p class="text-xs text-gray-500">
                                      参数: {{ rule.params || '无' }}
                                    </p>
                                  </div>
                                  <div class="flex space-x-2">
                                    <el-button
                                      type="primary"
                                      size="small"
                                      @click="startEditRule(categoryIndex, ruleIndex)"
                                    >
                                      编辑
                                    </el-button>
                                    <el-button type="danger" size="small" @click="removeRiskRule(categoryIndex, ruleIndex)">
                                      删除
                                    </el-button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 添加新规则表单 -->
                          <div v-if="editingRule && editingRule.categoryIndex === categoryIndex && editingRule.ruleIndex === -1" class="border border-green-200 rounded bg-green-50 p-3">
                            <div class="mb-2 flex justify-between items-center">
                              <span class="text-sm font-medium text-green-700">添加新规则</span>
                              <div class="space-x-2">
                                <el-button type="primary" size="small" @click="addRiskRule(categoryIndex)">
                                  添加
                                </el-button>
                                <el-button size="small" @click="cancelEditRule">
                                  取消
                                </el-button>
                              </div>
                            </div>
                            <el-form :model="getRiskRuleForm(categoryIndex)" label-width="80px" size="small">
                              <el-row :gutter="12">
                                <el-col :span="8">
                                  <el-form-item label="规则代码">
                                    <el-input v-model="getRiskRuleForm(categoryIndex).code" placeholder="规则代码" />
                                  </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                  <el-form-item label="规则名称">
                                    <el-input v-model="getRiskRuleForm(categoryIndex).name" placeholder="规则名称" />
                                  </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                  <el-form-item label="规则类型">
                                    <el-select v-model="getRiskRuleForm(categoryIndex).ruleType" placeholder="规则类型" style="width: 100%">
                                      <el-option
                                        v-for="option in ruleTypeOptions"
                                        :key="option.value"
                                        :label="option.label"
                                        :value="option.value"
                                      />
                                    </el-select>
                                  </el-form-item>
                                </el-col>
                              </el-row>
                              <el-row :gutter="12">
                                <el-col :span="12">
                                  <el-form-item label="参数">
                                    <el-input v-model="getRiskRuleForm(categoryIndex).params" placeholder="规则参数" />
                                  </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                  <el-form-item label="评分">
                                    <el-input-number v-model="getRiskRuleForm(categoryIndex).score" :min="0" :max="100" placeholder="评分" style="width: 100%" />
                                  </el-form-item>
                                </el-col>
                              </el-row>
                              <el-form-item label="规则描述">
                                <el-input
                                  v-model="getRiskRuleForm(categoryIndex).description"
                                  type="textarea"
                                  :rows="2"
                                  placeholder="请输入规则描述"
                                />
                              </el-form-item>
                            </el-form>
                          </div>

                          <!-- 空状态 -->
                          <div v-if="!category.riskRules || category.riskRules.length === 0" class="text-center py-4 text-gray-500">
                            <p class="text-sm">暂无规则，点击上方按钮添加规则</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/toolsCss.scss";
</style>
