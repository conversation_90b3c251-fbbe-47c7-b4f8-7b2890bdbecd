<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Upload } from '@element-plus/icons-vue'

// 格式1的返回数据类型
interface ImportV1Result {
  successCount?: number // 新增成功数
  updatedCount?: number // 更新成功数
  failedCount?: number // 失败数
  failedRecords?: string[] // 失败记录的错误信息数组
}

interface ImportV1Props {
  // 弹窗显示状态
  visible?: boolean
  // 弹窗标题
  title?: string
  // 下载模板的API函数
  downloadTemplateApi?: () => Promise<any>
  // 导入数据的API函数 - 返回格式1
  importDataApi?: (file: File) => Promise<ImportV1Result>
  // 模板文件名
  templateFileName?: string
  // 支持的文件类型
  acceptFileTypes?: string
  // 最大文件大小(MB)
  maxFileSize?: number
  // 是否显示下载模板按钮
  showDownloadTemplate?: boolean
}

const props = withDefaults(defineProps<ImportV1Props>(), {
  visible: false,
  title: '数据导入',
  templateFileName: '导入模板.xlsx',
  acceptFileTypes: '.xlsx,.xls',
  maxFileSize: 10,
  showDownloadTemplate: true,
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': [result: ImportV1Result]
  'error': [error: any]
}>()

// 响应式数据
const downloading = ref(false)
const uploading = ref(false)
const uploadRef = ref()

// 关闭弹窗
function handleClose() {
  emit('update:visible', false)
}

// 下载模板
async function handleDownloadTemplate() {
  if (!props.downloadTemplateApi) {
    ElMessage.warning('未配置下载模板接口')
    return
  }

  try {
    downloading.value = true
    const response = await props.downloadTemplateApi()

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = props.templateFileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  }
  catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败，请重试')
  }
  finally {
    downloading.value = false
  }
}

// 文件上传前的校验
function beforeUpload(file: File) {
  // 检查文件类型
  const fileExtension = file.name.substring(file.name.lastIndexOf('.'))
  const allowedTypes = props.acceptFileTypes.split(',')
  if (!allowedTypes.includes(fileExtension)) {
    ElMessage.error(`只支持 ${props.acceptFileTypes} 格式的文件`)
    return false
  }

  // 检查文件大小
  const fileSizeMB = file.size / 1024 / 1024
  if (fileSizeMB > props.maxFileSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxFileSize}MB`)
    return false
  }

  return true
}

// 处理文件上传
async function handleFileUpload(file: File) {
  if (!props.importDataApi) {
    ElMessage.warning('未配置导入接口')
    return
  }

  try {
    uploading.value = true
    const result = await props.importDataApi(file)

    const newCount = result.successCount || 0
    const updateCount = result.updatedCount || 0
    const failureCount = result.failedCount || 0
    const totalSuccess = newCount + updateCount

    // 构建结果消息 - 始终显示三个字段
    const message = `导入完成！新增 ${newCount} 条，更新 ${updateCount} 条，失败 ${failureCount} 条`

    if (failureCount === 0) {
      // 完全成功
      ElMessage.success(message)
      emit('success', result)
      handleClose()
    }
    else {
      // 有失败记录，显示详细信息
      showImportErrorDialog(message, result, totalSuccess)

      if (totalSuccess > 0) {
        emit('success', result)
      }
      else {
        emit('error', result)
      }
      handleClose()
    }
  }
  catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请重试')
    emit('error', error)
  }
  finally {
    uploading.value = false
    // 清空文件选择
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
  }
}

// 显示导入错误详情对话框
function showImportErrorDialog(message: string, result: ImportV1Result, totalSuccess: number) {
  if (!result.failedRecords || result.failedRecords.length === 0) {
    ElMessageBox.alert(
      message,
      totalSuccess > 0 ? '导入结果' : '导入失败',
      {
        type: totalSuccess > 0 ? 'warning' : 'error',
      },
    )
    return
  }

  // 处理错误信息，按行分组
  const errorsByRow = new Map<string, string[]>()

  result.failedRecords.forEach((errorMsg) => {
    // 提取行号信息，格式如："第2行，性别：只能填写MALE/FEMALE/UNKNOWN"
    const rowMatch = errorMsg.match(/^第(\d+)行[，,](.+)$/)
    if (rowMatch) {
      const rowNum = rowMatch[1]
      const errorDetail = rowMatch[2]

      if (!errorsByRow.has(rowNum)) {
        errorsByRow.set(rowNum, [])
      }
      errorsByRow.get(rowNum)!.push(errorDetail)
    }
    else {
      // 如果不匹配行号格式，直接添加到"其他"分组
      if (!errorsByRow.has('其他')) {
        errorsByRow.set('其他', [])
      }
      errorsByRow.get('其他')!.push(errorMsg)
    }
  })

  // 构建错误详情HTML
  let errorHtml = `<div style="text-align: left; max-height: 400px; overflow-y: auto;">`

  // 按行号排序
  const sortedRows = Array.from(errorsByRow.keys()).sort((a, b) => {
    if (a === '其他') {
      return 1
    }
    if (b === '其他') {
      return -1
    }
    return Number.parseInt(a) - Number.parseInt(b)
  })

  sortedRows.forEach((rowNum, index) => {
    const errors = errorsByRow.get(rowNum)!

    if (index > 0) {
      errorHtml += `<div style="margin: 12px 0; border-top: 1px solid #eee;"></div>`
    }

    if (rowNum === '其他') {
      errorHtml += `<div style="font-weight: 600; color: #e74c3c; margin-bottom: 6px;">其他错误：</div>`
    }
    else {
      errorHtml += `<div style="font-weight: 600; color: #e74c3c; margin-bottom: 6px;">第${rowNum}行错误：</div>`
    }

    errors.forEach((error) => {
      errorHtml += `<div style="margin-left: 12px; margin-bottom: 4px; color: #666; line-height: 1.4;">• ${error}</div>`
    })
  })

  errorHtml += `</div>`

  // 构建完整消息
  const fullMessage = `
    <div style="margin-bottom: 16px; font-size: 14px;">
      ${message}
    </div>
    <div style="margin-bottom: 12px; font-weight: 600; color: #333;">
      错误详情：
    </div>
    ${errorHtml}
  `

  ElMessageBox.alert(
    fullMessage,
    totalSuccess > 0 ? '导入结果' : '导入失败',
    {
      type: totalSuccess > 0 ? 'warning' : 'error',
      dangerouslyUseHTMLString: true,
      customStyle: {
        width: '600px',
      },
    },
  )
}

// 自定义上传处理
function customUpload(options: any) {
  handleFileUpload(options.file)
  return Promise.resolve()
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    width="500px"
    @update:model-value="handleClose"
    @close="handleClose"
  >
    <div class="import-dialog-content">
      <div class="mb-4 flex items-center justify-center space-x-3">
        <!-- 下载模板按钮 -->
        <el-button
          v-if="showDownloadTemplate"
          :loading="downloading"
          @click="handleDownloadTemplate"
        >
          <el-icon class="mr-1">
            <Download />
          </el-icon>
          下载模板
        </el-button>

        <!-- 导入按钮 -->
        <el-upload
          ref="uploadRef"
          :show-file-list="false"
          :http-request="customUpload"
          :before-upload="beforeUpload"
          :accept="acceptFileTypes"
        >
          <el-button :loading="uploading" type="primary">
            <el-icon class="mr-1">
              <Upload />
            </el-icon>
            {{ uploading ? '导入中...' : '导入数据' }}
          </el-button>
        </el-upload>
      </div>

      <div class="text-center text-sm text-gray-500">
        <p>支持文件格式：{{ acceptFileTypes }}</p>
        <p>文件大小限制：{{ maxFileSize }}MB</p>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped>
.import-dialog-content {
  padding: 16px 0;
}
</style>
