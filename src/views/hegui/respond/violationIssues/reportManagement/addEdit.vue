<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import problemTaskApi, { type ProblemInvestigateAttachmentDTO, type ProblemInvestigateReportDTO } from '@/api/problemTask'
import DepartPerson from '@/components/departPerson/index.vue'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import dictApi from '@/api/modules/system/dict'
import UploadMbb from '@/components/uploadMbb/index.vue'

const router = useRouter()
const route = useRoute()

// 当前用户
const currentUser = ref('张明远')

// 表单数据
const formData = reactive<ProblemInvestigateReportDTO>({
  title: '',
  reportCode: '',
  investigateId: null,
  reportType: 'SPECIAL_INVESTIGE',
  investigateSource: undefined,
  employeeId: null, // 默认编制人ID
  establishDate: null,
  level: undefined,
  summary: '',
  orgId: null,
  // status: 'MODIFY',
  investigateBackground: '',
  investigateMethod: '',
  investigateProcess: '',
  investigateFound: '',
  investigateConclusion: '',
  recommendMeasure: '',
  attachmentList: [],
})

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入报告标题', trigger: 'blur' }],
  reportCode: [{ required: true, message: '请输入报告编号', trigger: 'blur' }],
}

// 是否自动生成编号
const autoGenerateCode = ref(true)

// 调查任务选项
const investigateOptions = ref<Array<{ id: number, title: string }>>([])

// 报告类型选项
const reportTypeOptions = ref<Array<{ name: string, value: string }>>([])

// 用于DepartPerson组件的employeeId（字符串类型）
const employeeIdForComponent = ref<string>('')

// 监听employeeIdForComponent变化，转换为数字类型赋给formData.employeeId
watch(employeeIdForComponent, (newValue) => {
  if (newValue) {
    formData.employeeId = Number(newValue)
  }
  else {
    formData.employeeId = null
  }
})

// 文件上传处理
function handleUploadSuccess(files: any[]) {
  console.log('上传成功的文件:', files)
  // 更新 formData.attachmentList
  formData.attachmentList = files.map(file => ({
    // relatedId: formData.id || 0,
    relatedType: 2, // 调查报告
    fileName: file.fileName || file.name,
    filePath: file.filePath || file.url,
    fileSize: file.fileSize || file.size,
    fileType: file.fileType || file.type,
    // createdBy: currentUser.value,
  }))
  ElMessage.success('文件上传成功')
}

function handleUploadError(error: any) {
  console.error('上传失败:', error)
  ElMessage.error('文件上传失败')
}

// 报告格式设置
const showPageNumber = ref(true)
const showCover = ref(true)
const showSignature = ref(true)

// 审批流程设置
const approvalFlow = ref([
  { order: 1, role: '合规主管', person: '李伟', required: true },
  { order: 2, role: '法务负责人', person: '王芳', required: true },
  { order: 3, role: 'CEO', person: '张强', required: false },
])

function addApprovalStep() {
  approvalFlow.value.push({
    order: approvalFlow.value.length + 1,
    role: '',
    person: '',
    required: true,
  })
}

function removeApproval(index: number) {
  approvalFlow.value.splice(index, 1)
  // 重新排序
  approvalFlow.value.forEach((item, i) => {
    item.order = i + 1
  })
}

function moveUp(index: number) {
  if (index > 0) {
    const temp = approvalFlow.value[index]
    approvalFlow.value[index] = approvalFlow.value[index - 1]
    approvalFlow.value[index - 1] = temp
    // 重新排序
    approvalFlow.value.forEach((item, i) => {
      item.order = i + 1
    })
  }
}

function moveDown(index: number) {
  if (index < approvalFlow.value.length - 1) {
    const temp = approvalFlow.value[index]
    approvalFlow.value[index] = approvalFlow.value[index + 1]
    approvalFlow.value[index + 1] = temp
    // 重新排序
    approvalFlow.value.forEach((item, i) => {
      item.order = i + 1
    })
  }
}

const approvalTimeLimit = ref(3)
const enableReminder = ref(true)
const reminderInterval = ref(2)

// 通知与权限设置
const personOptions = [
  { value: '1', label: '张明远 (合规部)' },
  { value: '2', label: '李伟 (合规主管)' },
  { value: '3', label: '王芳 (法务部)' },
  { value: '4', label: '张强 (CEO)' },
  { value: '5', label: '刘洋 (财务部)' },
]

const notifyPersons = ref(['1', '2'])
const notifyMethods = ref(['系统消息', '电子邮件'])
const viewPermission = ref('仅审批人员')
const specifiedViewers = ref([])
const exportPermission = ref('需审批')
const commentPermission = ref('允许')

// 加载数据
async function loadData() {
  try {
    // 加载报告类型选项
    reportTypeOptions.value = await dictApi.dictAll(32)
    // 加载调查任务列表
    const taskResponse = await problemTaskApi.searchTasks({}, 0, 100)
    if (taskResponse) {
      investigateOptions.value = taskResponse.content || []
    }

    // 如果是编辑模式，加载报告详情
    const reportId = route.query.id
    if (reportId) {
      const response = await problemTaskApi.getReportDetail(Number(reportId))
      if (response) {
        Object.assign(formData, response)
        // 附件列表已经在 formData.attachmentList 中，UploadMbb 组件会自动处理
      }
    }
  }
  catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  }
}

// 生成报告编号
function generateReportCode() {
  if (autoGenerateCode.value) {
    const timestamp = dayjs().format('YYYYMMDDHHmmss')
    formData.reportCode = `RPT${timestamp}`
  }
}

// 保存草稿
async function saveDraft() {
  try {
    // formData.status = 'MODIFY'
    const response = formData.id
      ? await problemTaskApi.updateReport(formData.id, formData)
      : await problemTaskApi.createReport(formData)

    if (response) {
      ElMessage.success('保存成功')
      if (!formData.id) {
        formData.id = response.id
      }
      router.back()
    }
    else {
      ElMessage.error(response.message || '保存失败')
    }
  }
  catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 提交审核
async function submitForReview() {
  try {
    // 验证必填字段
    if (!formData.title) {
      ElMessage.error('请输入报告标题')
      return
    }
    if (!formData.investigateId) {
      ElMessage.error('请选择关联调查')
      return
    }

    await ElMessageBox.confirm('确认提交审核吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // formData.status = 'PENDING'
    const response = formData.id
      ? await problemTaskApi.updateReport(formData.id, formData)
      : await problemTaskApi.createReport(formData)

    if (response) {
      ElMessage.success('提交审核成功')
      router.back()
    }
    else {
      ElMessage.error('提交失败')
    }
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  }
}
// 处理违规调查选择变化
function handleInvestigateChange(value: any, row: any) {
  if (row) {
    // 设置调查ID和名称
    formData.investigateId = value
    formData.investigateName = row.title
  }
  else {
    // 清空时同时清空两个字段
    formData.investigateId = undefined
    formData.investigateName = ''
  }
}
// 取消
function cancel() {
  router.back()
}

// 预览
function preview() {
  // 实现预览功能
  ElMessage.info('预览功能开发中')
}

// 监听自动生成编号
function onAutoGenerateChange() {
  if (autoGenerateCode.value) {
    generateReportCode()
  }
}

// 页面加载时执行
onMounted(() => {
  const investigateId = route.query.investigateId
  if (investigateId) {
    formData.investigateId = Number(investigateId)
  }
  loadData()
  generateReportCode()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航与操作区 -->
    <div class="bg-white shadow-sm">
      <div class="mx-auto max-w-7xl px-4 py-4">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-600">
          <span>应对之翼</span>
          <i class="el-icon-arrow-right mx-2 text-xs" />
          <span>违规问题调查</span>
          <i class="el-icon-arrow-right mx-2 text-xs" />
          <span>调查报告管理</span>
          <i class="el-icon-arrow-right mx-2 text-xs" />
          <span class="text-gray-800 font-medium">新增报告</span>
        </div>

        <!-- 页面标题和操作按钮 -->
        <div class="mt-4 flex items-center justify-between">
          <h1 class="text-xl font-bold">
            新增调查报告
          </h1>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="saveDraft">
              <i class="el-icon-document mr-1" />提交
            </el-button>
            <!-- <el-button class="!rounded-button whitespace-nowrap" @click="submitForReview">
              <i class="el-icon-check mr-1" />提交审核
            </el-button> -->
            <el-button class="!rounded-button whitespace-nowrap" @click="cancel">
              <i class="el-icon-close mr-1" />取消
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主体表单内容区 -->
    <div class="mx-auto max-w-7xl px-4 py-6">
      <!-- 基本信息编辑区 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            基本信息
          </div>
        </template>
        <el-form :model="formData" :rules="formRules" label-position="right" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="报告标题" prop="title" required>
                <el-input v-model="formData.title" placeholder="请输入报告标题" class="h-8" />
              </el-form-item>
              <el-form-item label="报告编号" prop="reportCode">
                <div class="flex items-center">
                  <el-input v-model="formData.reportCode" placeholder="系统自动生成" class="h-8 flex-1" readonly />
                </div>
              </el-form-item>
              <el-form-item label="报告类型">
                <el-select v-model="formData.reportType" placeholder="请选择报告类型" class="h-8 w-full">
                  <el-option
                    v-for="item in reportTypeOptions"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="编制日期">
                <el-date-picker
                  v-model="formData.establishDate"
                  type="date"
                  placeholder="选择日期"
                  class="h-8 w-full"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item prop="investigateId" required label="关联调查">
                <ViolationInvestigationSelector
                  v-model="formData.investigateId"
                  :display-value="formData.investigateName"
                  display-key="title"
                  placeholder="请选择关联的违规问题调查"
                  @change="handleInvestigateChange"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="编制人">
                <DepartPerson v-model="formData.employeeId" placeholder="请选择编制人" />
              </el-form-item>
              <el-form-item label="编制部门">
                <DepartmentTreeSelect v-model="formData.orgId" placeholder="请选择编制部门" />
              </el-form-item>
              <el-form-item label="优先级">
                <el-select v-model="formData.level" placeholder="请选择优先级" class="h-8 w-full">
                  <el-option label="低" value="LOW" />
                  <el-option label="中" value="MIDDLE" />
                  <el-option label="高" value="HIGH" />
                </el-select>
              </el-form-item>
              <el-form-item label="调查来源">
                <el-select v-model="formData.investigateSource" placeholder="请选择调查来源" class="h-8 w-full">
                  <el-option label="市场部" value="MARKETING" />
                  <el-option label="采购部" value="PROCUREMENT" />
                  <el-option label="人力资源部" value="HR" />
                  <el-option label="财务部" value="FINANCE" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 报告内容编辑区 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            调查报告摘要
          </div>
        </template>
        <el-input
          v-model="formData.summary"
          type="textarea"
          :rows="8"
          placeholder="请输入调查报告摘要，建议300-500字"
          resize="none"
        />
      </el-card>

      <!-- 调查背景 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            一、调查背景
          </div>
        </template>
        <el-input
          v-model="formData.investigateBackground"
          type="textarea"
          :rows="8"
          placeholder="请输入调查背景，包括调查缘起、调查对象、调查范围、调查目标等内容"
          resize="none"
        />
      </el-card>

      <!-- 调查方法 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            二、调查方法
          </div>
        </template>
        <el-input
          v-model="formData.investigateMethod"
          type="textarea"
          :rows="8"
          placeholder="请输入调查方法，包括调查团队组成、调查步骤、数据收集方法、证据收集与保存方法等内容"
          resize="none"
        />
      </el-card>

      <!-- 调查过程 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            三、调查过程
          </div>
        </template>
        <el-input
          v-model="formData.investigateProcess"
          type="textarea"
          :rows="8"
          placeholder="请输入调查过程，包括关键调查活动、人员访谈概述、文件审阅概述、现场调查概述等内容"
          resize="none"
        />
      </el-card>

      <!-- 调查发现 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            四、调查发现
          </div>
        </template>
        <el-input
          v-model="formData.investigateFound"
          type="textarea"
          :rows="8"
          placeholder="请输入调查发现，包括事实发现、证据分析、违规行为认定、原因分析等内容"
          resize="none"
        />
      </el-card>

      <!-- 调查结论 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            五、调查结论
          </div>
        </template>
        <el-input
          v-model="formData.investigateConclusion"
          type="textarea"
          :rows="8"
          placeholder="请输入调查结论，包括结论概述、违规性质和严重程度、责任认定、影响评估等内容"
          resize="none"
        />
      </el-card>

      <!-- 建议措施 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            六、建议措施
          </div>
        </template>
        <el-input
          v-model="formData.recommendMeasure"
          type="textarea"
          :rows="8"
          placeholder="请输入建议措施，包括具体措施建议、优先级设置、责任部门推荐、实施时间建议、预期效果等内容"
          resize="none"
        />
      </el-card>

      <!-- 附件 -->
      <el-card class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            七、附件
          </div>
        </template>
        <UploadMbb
          v-model="formData.attachmentList"
          :max="1"
          :size="20"
          service-name="whiskerguardregulatoryservice"
          category-name="investigation-report"
          :use-file-path="true"
          tip-text="支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 20MB"
          @upload-success="handleUploadSuccess"
          @upload-error="handleUploadError"
        />
      </el-card>

      <!-- 报告格式设置区 -->
      <el-card v-if="false" class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            报告格式
          </div>
        </template>
        <el-form label-position="right" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="报告模板">
                <el-select placeholder="请选择报告模板" class="h-8 w-full">
                  <el-option label="标准模板" value="1" />
                  <el-option label="简洁模板" value="2" />
                  <el-option label="详细模板" value="3" />
                  <el-option label="自定义模板" value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="字体设置">
                <el-select placeholder="请选择字体" class="h-8 w-full">
                  <el-option label="宋体" value="1" />
                  <el-option label="黑体" value="2" />
                  <el-option label="微软雅黑" value="3" />
                  <el-option label="Arial" value="4" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="字号设置">
                <el-select placeholder="请选择字号" class="h-8 w-full">
                  <el-option label="小号" value="1" />
                  <el-option label="标准" value="2" />
                  <el-option label="大号" value="3" />
                </el-select>
              </el-form-item>
              <el-form-item label="显示页码">
                <el-switch v-model="showPageNumber" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="页眉内容">
            <el-input placeholder="请输入页眉内容" class="h-8" />
          </el-form-item>
          <el-form-item label="页脚内容">
            <el-input placeholder="请输入页脚内容" class="h-8" />
          </el-form-item>
          <el-form-item label="显示封面">
            <el-switch v-model="showCover" />
          </el-form-item>
          <el-form-item v-if="showCover" label="封面图片">
            <el-upload
              class="upload-demo"
              action="https://jsonplaceholder.typicode.com/posts/"
              :show-file-list="false"
            >
              <el-button type="primary" class="!rounded-button whitespace-nowrap">
                <i class="el-icon-upload mr-1" />上传封面
              </el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="显示签名区域">
            <el-switch v-model="showSignature" />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 审批流程设置区 -->
      <el-card v-if="false" class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            审批流程
          </div>
        </template>
        <el-form label-position="right" label-width="120px">
          <el-form-item label="审批流程">
            <el-select placeholder="请选择审批流程" class="h-8 w-full">
              <el-option label="标准流程" value="1" />
              <el-option label="简化流程" value="2" />
              <el-option label="严格流程" value="3" />
              <el-option label="自定义流程" value="4" />
            </el-select>
          </el-form-item>
          <el-form-item label="审批人设置">
            <el-table :data="approvalFlow" border style="width: 100%">
              <el-table-column prop="order" label="审批顺序" width="100" />
              <el-table-column prop="role" label="审批角色" />
              <el-table-column prop="person" label="审批人" />
              <el-table-column prop="required" label="必选" width="80">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.required" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180">
                <template #default="scope">
                  <el-button size="small" @click="moveUp(scope.$index)">
                    <i class="el-icon-top" />
                  </el-button>
                  <el-button size="small" @click="moveDown(scope.$index)">
                    <i class="el-icon-bottom" />
                  </el-button>
                  <el-button size="small" type="danger" @click="removeApproval(scope.$index)">
                    <i class="el-icon-delete" />
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-2">
              <el-button type="primary" plain class="!rounded-button whitespace-nowrap" @click="addApprovalStep">
                <i class="el-icon-plus mr-1" />添加审批步骤
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="审批时限">
            <el-input-number v-model="approvalTimeLimit" :min="1" :max="30" label="天" />
          </el-form-item>
          <el-form-item label="催办设置">
            <el-switch v-model="enableReminder" />
            <span class="ml-2 text-sm text-gray-500">超时自动催办</span>
          </el-form-item>
          <el-form-item v-if="enableReminder" label="催办间隔">
            <el-input-number v-model="reminderInterval" :min="1" :max="7" label="天" />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 通知与权限设置区 -->
      <el-card v-if="false" class="mb-6">
        <template #header>
          <div class="text-base font-bold">
            通知与权限
          </div>
        </template>
        <el-form label-position="right" label-width="120px">
          <el-form-item label="通知对象">
            <el-select v-model="notifyPersons" multiple placeholder="请选择通知对象" class="w-full">
              <el-option
                v-for="item in personOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="通知方式">
            <el-checkbox-group v-model="notifyMethods">
              <el-checkbox label="系统消息" />
              <el-checkbox label="电子邮件" />
              <el-checkbox label="短信" />
              <el-checkbox label="其他" />
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="查看权限">
            <el-radio-group v-model="viewPermission">
              <el-radio label="仅审批人员" />
              <el-radio label="部门主管" />
              <el-radio label="全公司" />
              <el-radio label="指定人员" />
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="viewPermission === '指定人员'" label="指定查看人员">
            <el-select v-model="specifiedViewers" multiple placeholder="请选择指定查看人员" class="w-full">
              <el-option
                v-for="item in personOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="导出权限">
            <el-radio-group v-model="exportPermission">
              <el-radio label="允许" />
              <el-radio label="不允许" />
              <el-radio label="需审批" />
            </el-radio-group>
          </el-form-item>
          <el-form-item label="评论权限">
            <el-radio-group v-model="commentPermission">
              <el-radio label="允许" />
              <el-radio label="不允许" />
              <el-radio label="需审批" />
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.el-form-item {
  margin-bottom: 24px;
}

.el-collapse-item__header {
  font-weight: 500;
}

.el-table {
  margin-top: 10px;
}

.upload-demo {
  margin-top: 10px;
}
</style>
