import api from '../index'

// 风险模型管理API
const riskModelApi = {
  // 获取风险模型列表
  getRiskModels(params: any) {
    return api({
      url: '/whiskerguardorgservice/api/risk/models',
      method: 'get',
      params,
    })
  },

  // 创建风险模型
  createRiskModel(data: any) {
    return api({
      url: '/whiskerguardorgservice/api/risk/models',
      method: 'post',
      data,
    })
  },

  // 更新风险模型
  updateRiskModel(data: any) {
    return api({
      url: `/whiskerguardorgservice/api/risk/models/${data.id}`,
      method: 'patch',
      data,
    })
  },

  // 删除风险模型
  deleteRiskModel(id: number) {
    return api({
      url: `/whiskerguardorgservice/api/risk/models/${id}`,
      method: 'delete',
    })
  },

  // 获取风险模型详情
  getRiskModelDetail(id: number) {
    return api({
      url: `/whiskerguardorgservice/api/risk/models/${id}`,
      method: 'get',
    })
  },

  // 设置默认模型
  setDefaultModel(id: number) {
    return api({
      url: `/whiskerguardorgservice/api/risk/models/${id}/default`,
      method: 'put',
    })
  },
}

export default riskModelApi
